# This configuration collects system logs and metric for OSX and Linux systems, sending the results to ClickStack via the OTLP endpoint on port 4317.
receivers:
  # filelog:
  #   include:
  #     - /var/log/**/*.log # Linux
  #     - /var/log/syslog
  #     - /var/log/messages
  #     - /private/var/log/*.log # macOS
  #   start_at: beginning # modify to collect new files only
  prometheus:
    config:
      global:
        scrape_interval: 15s
      scrape_configs:
        - job_name: "node_exporter"
          static_configs:
            - targets: ["***********:9101"]

  hostmetrics:
    collection_interval: 1s
    scrapers:
      cpu:
        metrics:
          system.cpu.time:
            enabled: true
          system.cpu.utilization:
            enabled: true
      memory:
        metrics:
          system.memory.usage:
            enabled: true
          system.memory.utilization:
            enabled: true
      filesystem:
        metrics:
          system.filesystem.usage:
            enabled: true
          system.filesystem.utilization:
            enabled: true
      paging:
        metrics:
          system.paging.usage:
            enabled: true
          system.paging.utilization:
            enabled: true
          system.paging.faults:
            enabled: true
      disk:
      load:
      network:
      processes:

exporters:
  otlp:
    endpoint: localhost:4317
    headers:
      authorization: <api-key>
    tls:
      insecure: true
    sending_queue:
      enabled: true
      num_consumers: 10
      queue_size: 262144 # 262,144 items × ~8 KB per item ≈ 2 GB

service:
  pipelines:
    logs:
      receivers: [otlp]
      exporters: [otlp]
    metrics:
      receivers: [otlp, prometheus]
      exporters: [otlp]
