version: "3.8"

services:
  hyperdx:
    image: docker.hyperdx.io/hyperdx/hyperdx-all-in-one
    ports:
      - "8080:8080"
      - "4317:4317"
      - "4318:4318"
    # environment:
    # - CLICKHOUSE_USER=admin
    # - CLICKHOUSE_ENDPOINT=${CLICKHOUSE_ENDPOINT}
    # - CLICKHOUSE_PASSWORD=admin
    volumes:
      - ./.volumes/db:/data/db
      - ./.volumes/ch_data:/var/lib/clickhouse
      - ./.volumes/ch_logs:/var/log/clickhouse-server
    restart: unless-stopped
    networks:
      - clickstack

  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    network_mode: "host" # Equivalent to --network=host
    user: "0:0" # Equivalent to --user 0:0
    volumes:
      - ./otel-collector.yaml:/etc/otel/config.yaml
      - /var/log:/var/log:ro
      - /private/var/log:/private/var/log:ro
    command: --config /etc/otel/config.yaml
    restart: unless-stopped

volumes:
  db:
  ch_data:
  ch_logs:

networks:
  clickstack:
    driver: bridge
