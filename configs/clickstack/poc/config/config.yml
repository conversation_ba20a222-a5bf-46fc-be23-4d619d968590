logger:
  level: error
  console: true
  log: /var/log/clickhouse-server/clickhouse-server.log
  errorlog: /var/log/clickhouse-server/clickhouse-server.err.log

s3queue_log:
  database: system
  table: s3queue_log

s3:
  max_connections: 100
  connection_timeout: 10
  retry_attempts: 10
  stream_like_engine_allow_direct_select: 1

format_schema_inference: 1
format_schema_inference_cache_size: 10000
format_schema_inference_sample_rows: 1000

max_server_memory_usage: 3221225472 # ~3GB in bytes
max_server_memory_usage_to_ram_ratio: 0.7 # Use up to 40% of RAM
mark_cache_size: 5368709120 # ~5GB in bytes

max_concurrent_queries: 10
default_profile: default
default_database: analytics

path: /var/lib/clickhouse/
tmp_path: /var/lib/clickhouse/tmp/

users_config: users.xml

tcp_port: 9000
http_port: 8123
listen_host: 0.0.0.0

prometheus:
  endpoint: /metrics
  port: 9363

max_connections: 1024

merge_tree:
  parts_to_delay_insert: 300
  parts_to_throw_insert: 400

keeper_server: #keeper means zookeeper
  tcp_port: 2181
  server_id: 1
  log_storage_path: /var/lib/clickhouse/coordination/log
  snapshot_storage_path: /var/lib/clickhouse/coordination/snapshots

  coordination_settings:
    operation_timeout_ms: 10000
    session_timeout_ms: 30000
    raft_logs_level: trace

    raft_configuration:
      server:
        id: 1
        hostname: localhost
        port: 9234
