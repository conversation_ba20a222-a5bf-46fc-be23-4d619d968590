users:
  admin:
    profile: default
    networks:
      ip: "::/0"
    quota: default
    access_management: 1

profiles:
  default:
    # ~2GB per query
    max_memory_usage: 2000000000
    # ~2GB per user
    max_memory_usage_for_user: 2000000000
    # When GROUP BY operation exceeds 2GB, starts using disk
    max_bytes_before_external_group_by: 2000000000
    # When sorting exceeds 2GB, starts using disk
    max_bytes_before_external_sort: 2000000000
    # Prevents one query from using all vCPUs
    max_threads: 2
    # Enables caching of uncompressed data in RAM
    # Improves read performance for repeated scans at the cost of some memory
    use_uncompressed_cache: 1
