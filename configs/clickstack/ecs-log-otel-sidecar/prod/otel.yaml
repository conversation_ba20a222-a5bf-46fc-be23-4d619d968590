receivers:
  filelog:
    include: 
    - ${env:LOG_FILE_PATH}
    start_at: beginning
    operators:
      # Parse JSON logs
      - type: json_parser
        timestamp:
          parse_from: attributes.time
          layout: '%Y-%m-%dT%H:%M:%S.%LZ'

processors:
  batch:
    timeout: 2s

exporters:
  clickhouse:
    endpoint: tcp://${env:CLICKSTACK_ENDPOINT}:9000?dial_timeout=10s&compress=lz4&async_insert=1
    database: ${env:HYPERDX_OTEL_EXPORTER_CLICKHOUSE_DATABASE}
    password: ${env:CLICKSTACK_PASSWORD}
    username: ${env:CLICKSTACK_USER}
    ttl: 1h
    timeout: 5s
    sending_queue:
      queue_size: 1000
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s

service:
  pipelines:
    logs:
      receivers: [filelog]
      processors: [batch]
      exporters: [clickhouse]