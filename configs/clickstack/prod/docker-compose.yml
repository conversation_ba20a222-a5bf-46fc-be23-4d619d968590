name: hdx-oss
services:
  db:
    image: mongo:5.0.14-focal
    volumes:
      - .volumes/db:/data/db
    ports:
      - 27017:27017
    networks:
      - internal

  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    environment:
      CLICKHOUSE_ENDPOINT: "tcp://ch-server:9000?dial_timeout=10s"
      HYPERDX_OTEL_EXPORTER_CLICKHOUSE_DATABASE: default
      HYPERDX_LOG_LEVEL: info
      OPAMP_SERVER_URL: "http://app:4320"
      CLICKHOUSE_USER: "api"
      CLICKHOUSE_PASSWORD: "<password>"
    volumes:
      - ./configs/otel.yml:/etc/otel/config.yml
    command: --config /etc/otel/config.yml
    ports:
      - "13133:13133" # health_check extension
      - "24225:24225" # fluentd receiver
      - "4317:4317" # OTLP gRPC receiver
      - "4318:4318" # OTLP http receiver
      - "8888:8888" # metrics extension
    restart: always
    networks:
      - internal
    depends_on:
      - ch-server

  app:
    image: hyperdx/hyperdx:2-nightly
    ports:
      - 8080:8080
      - 8000:8000
    environment:
      FRONTEND_URL: http://**********:8080
      # HYPERDX_API_KEY: ${HYPERDX_API_KEY}
      HYPERDX_API_PORT: 8000
      HYPERDX_APP_PORT: 8080
      HYPERDX_APP_URL: http://**********
      HYPERDX_LOG_LEVEL: "debug"
      MINER_API_URL: "http://miner:5123"
      MONGO_URI: "mongodb://db:27017/hyperdx"
      NEXT_PUBLIC_SERVER_URL: http://**********:8000
      OPAMP_PORT: 4320
      OTEL_EXPORTER_OTLP_ENDPOINT: "http://otel-collector:4318"
      OTEL_SERVICE_NAME: "hdx-oss-app"
      USAGE_STATS_ENABLED: ${USAGE_STATS_ENABLED:-true}
      DEFAULT_CONNECTIONS: |
        [
        {
          "name":"Observability DB",
          "host":"http://ch-server:8123",
          "username":"api",
          "password":"<password>"
        }]
      DEFAULT_SOURCES: |
        [
        {
          "from": {
            "databaseName": "observability",
            "tableName": "otel_logs"
          },
          "kind": "log",
          "timestampValueExpression": "TimestampTime",
          "name": "Logs",
          "displayedTimestampValueExpression": "Timestamp",
          "implicitColumnExpression": "Body",
          "serviceNameExpression": "ServiceName",
          "bodyExpression": "Body",
          "eventAttributesExpression": "LogAttributes",
          "resourceAttributesExpression": "ResourceAttributes",
          "defaultTableSelectExpression": "Timestamp,ServiceName,SeverityText,Body",
          "severityTextExpression": "SeverityText",
          "traceIdExpression": "TraceId",
          "spanIdExpression": "SpanId",
          "connection": "Observability DB",
          "traceSourceId": "Traces",
          "sessionSourceId": "Sessions",
          "metricSourceId": "Metrics"
        },
        {
          "from": {
            "databaseName": "observability",
            "tableName": "otel_traces"
          },
          "kind": "trace",
          "timestampValueExpression": "Timestamp",
          "name": "Traces",
          "displayedTimestampValueExpression": "Timestamp",
          "implicitColumnExpression": "SpanName",
          "serviceNameExpression": "ServiceName",
          "bodyExpression": "SpanName",
          "eventAttributesExpression": "SpanAttributes",
          "resourceAttributesExpression": "ResourceAttributes",
          "defaultTableSelectExpression": "Timestamp,ServiceName,StatusCode,round(Duration/1e6),SpanName",
          "traceIdExpression": "TraceId",
          "spanIdExpression": "SpanId",
          "durationExpression": "Duration",
          "durationPrecision": 9,
          "parentSpanIdExpression": "ParentSpanId",
          "spanNameExpression": "SpanName",
          "spanKindExpression": "SpanKind",
          "statusCodeExpression": "StatusCode",
          "statusMessageExpression": "StatusMessage",
          "connection": "Observability DB",
          "logSourceId": "Logs",
          "sessionSourceId": "Sessions",
          "metricSourceId": "Metrics"
        },
        {
          "from": {
            "databaseName": "observability",
            "tableName": "otel_metrics"
          },
          "kind": "metric",
          "timestampValueExpression": "TimeUnix",
          "name": "Metrics",
          "resourceAttributesExpression": "ResourceAttributes",
          "metricTables": {
            "gauge": "otel_metrics_gauge",
            "histogram": "otel_metrics_histogram",
            "sum": "otel_metrics_sum",
            "_id": "682586a8b1f81924e628e808",
            "id": "682586a8b1f81924e628e808"
          },
          "connection": "Observability DB",
          "logSourceId": "Logs",
          "traceSourceId": "Traces",
          "sessionSourceId": "Sessions"
        },
        {
          "from": {
            "databaseName": "observability",
            "tableName": "hyperdx_sessions"
          },
          "kind": "session",
          "timestampValueExpression": "TimestampTime",
          "name": "Sessions",
          "displayedTimestampValueExpression": "Timestamp",
          "implicitColumnExpression": "Body",
          "serviceNameExpression": "ServiceName",
          "bodyExpression": "Body",
          "eventAttributesExpression": "LogAttributes",
          "resourceAttributesExpression": "ResourceAttributes",
          "defaultTableSelectExpression": "Timestamp,ServiceName,SeverityText,Body",
          "severityTextExpression": "SeverityText",
          "traceIdExpression": "TraceId",
          "spanIdExpression": "SpanId",
          "connection": "Observability DB",
          "logSourceId": "Logs",
          "traceSourceId": "Traces",
          "metricSourceId": "Metrics"
        }
        ]
    networks:
      - internal
    depends_on:
      - ch-server
      - db

  ch-server:
    image: clickhouse/clickhouse-server:latest-alpine
    ports:
      - 8123:8123 # http api
      - 9000:9000 # native
      - 9363:9363 #prometheus endpoint
    environment:
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1
      - CLICKHOUSE_DB=observability
      - CLICKHOUSE_USER=api
      - CLICKHOUSE_PASSWORD=<password>
    volumes:
      - ./configs/config.yml:/etc/clickhouse-server/config.yml
      - ./configs/users.yml:/etc/clickhouse-server/users.yml
      - ./.volumes/ch_data:/var/lib/clickhouse
      - ./.volumes/ch_logs:/var/log/clickhouse-server
    restart: on-failure
    networks:
      - internal
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "localhost:8123/ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 8GiB
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

networks:
  internal:

volumes:
  .volumes:
