# ClickStack Production Configuration

## 📁 Directory Overview

```
configs/clickstack/prod/
├── configs/
│   ├── config.xml          # ClickHouse server configuration (XML format)
│   ├── config.yml          # ClickHouse server configuration (YAML format)
│   ├── otel.yaml           # OpenTelemetry collector configuration
│   ├── users.xml           # User management and access control (XML format)
│   └── users.yml           # User management and access control (YAML format)
├── docker-compose.yml      # Complete observability stack deployment
└── README.md              # This documentation
```

## 🔧 Configuration Files

### ClickHouse Server Configuration

#### `config.xml` / `config.yml`
Main ClickHouse server configuration files available in both XML and YAML formats.

**Key Settings:**
- **Ports**: HTTP (8123), TCP (9000), Interserver (9009)
- **Host**: Listens on all interfaces (`0.0.0.0`)
- **Performance**: 
  - Max connections: 4096 (XML) / 1024 (YAML)
  - Max concurrent queries: 100 (XML) / 20 (YAML)
  - Cache sizes optimized for production workloads
- **Storage**: `/var/lib/clickhouse/` with dedicated tmp and user files paths
- **Timezone**: UTC (XML) / Asia/Kolkata (YAML)
- **Prometheus Integration**: Metrics endpoint on port 9363
- **Logging**: Debug level with comprehensive query and system logging

#### `users.xml` / `users.yml`
User management and access control configuration files.

**Default Users:**
- `admin`: Full administrative access
- `api`: API service account
- `worker`: Worker process account

**Security Features:**
- Network-based access control
- Memory usage quotas per user
- Profile-based resource limits
- Access management controls

### OpenTelemetry Configuration

#### `otel.yaml`
Comprehensive observability configuration for collecting system and application metrics.

**Monitored Services:**
- **Node Exporters**: System metrics from multiple instances
- **NATS Cluster**: 4-node NATS server monitoring
- **Prometheus**: Self-monitoring metrics
- **NATS Surveyor**: Advanced NATS metrics collection

**Data Flow:**
```
Prometheus Receivers → OTLP Processors → ClickStack (port 4317)
```

## 🐳 Docker Compose Deployment

The `docker-compose.yml` provides a complete observability stack with HyperDX frontend, ClickHouse backend, and OpenTelemetry collection.

### Services Overview

| Service | Image | Purpose | Ports |
|---------|-------|---------|-------|
| **app** | `hyperdx/hyperdx:2-nightly` | HyperDX observability frontend | 8080 (UI), 8000 (API) |
| **ch-server** | `clickhouse/clickhouse-server:latest-alpine` | ClickHouse database backend | 8123 (HTTP), 9000 (Native), 9363 (Metrics) |
| **otel-collector** | `otel/opentelemetry-collector-contrib:latest` | OpenTelemetry data collection | 4317 (OTLP gRPC), 4318 (OTLP HTTP), 24225 (Fluentd) |
| **db** | `mongo:5.0.14-focal` | MongoDB for application metadata | 27017 |

### Quick Start

```bash
# Clone and navigate to the directory
cd /path/to/configs/clickstack/prod

# Start the complete stack
docker-compose up -d

# Monitor logs
docker-compose logs -f

# Check service health
docker-compose ps
```

### Service Configuration

#### HyperDX Application (`app`)
- **Frontend URL**: `http://**********:8080`
- **API URL**: `http://**********:8000`
- **Pre-configured Data Sources**:
  - Logs: `observability.otel_logs`
  - Traces: `observability.otel_traces`
  - Metrics: `observability.otel_metrics`
  - Sessions: `observability.hyperdx_sessions`

#### ClickHouse Server (`ch-server`)
- **Database**: `observability`
- **User**: `api` / `<password>`
- **Health Check**: Automated with 30s intervals
- **Resource Limits**: 8GiB memory
- **Data Persistence**: `.volumes/ch_data` and `.volumes/ch_logs`

#### OpenTelemetry Collector (`otel-collector`)
- **Configuration**: `./configs/otel.yml`
- **Target Database**: ClickHouse `observability` database
- **Receivers**: OTLP (gRPC/HTTP), Fluentd
- **Health Endpoint**: `:13133`

### Access Points

```bash
# HyperDX Web Interface
open http://**********:8080

# ClickHouse HTTP API
curl http://localhost:8123/ping

# ClickHouse Native Client
clickhouse-client --host localhost --port 9000 --user api --password '<password>'

# OpenTelemetry Health Check
curl http://localhost:13133/

# Prometheus Metrics
curl http://localhost:9363/metrics
```

### Data Flow Architecture

```
Applications/Services → OpenTelemetry Collector → ClickHouse → HyperDX Frontend
                                ↓
                           MongoDB (metadata)
```

## 🚀 Usage

### Starting ClickHouse with Custom Config

#### Using XML Configuration
```bash
# Production deployment with XML config
clickhouse-server --config-file=/path/to/config.xml
```

## 🔍 Key Configuration Differences

### XML vs YAML Configurations

| Setting | XML Value | YAML Value | Purpose |
|---------|-----------|------------|---------|
| Max Connections | 4096 | 1024 | Connection pooling |
| Max Concurrent Queries | 100 | 20 | Query concurrency |
| Timezone | UTC | Asia/Kolkata | Regional settings |
| Default Database | default | observability | Primary database |
| Cache Sizes | Larger | Smaller | Memory allocation |

## 📊 Monitoring and Observability


### OpenTelemetry Pipeline
The OTEL configuration collects:
- **System Metrics**: CPU, memory, disk, network from node exporters
- **NATS Metrics**: Message rates, connections, subscriptions
- **Application Metrics**: Custom service metrics via Prometheus

### Health Checks

```bash
# ClickHouse health check
curl http://localhost:8123/ping

# Check system tables
clickhouse-client --query "SELECT * FROM system.clusters"

# Monitor query performance
clickhouse-client --query "SELECT * FROM system.query_log ORDER BY query_start_time DESC LIMIT 10"
```

## 🛠️ Troubleshooting

### Common Issues

#### Connection Problems
```bash
# Check if ClickHouse is listening
netstat -tlnp | grep -E ':(8123|9000|9009)'

# Test TCP connection
clickhouse-client --host localhost --port 9000

# Test HTTP connection
curl http://localhost:8123/
```

#### Performance Issues
```bash
# Monitor active queries
clickhouse-client --query "SELECT * FROM system.processes"

# Check memory usage
clickhouse-client --query "SELECT * FROM system.metrics WHERE metric LIKE '%Memory%'"

# Analyze slow queries
clickhouse-client --query "
SELECT query, query_duration_ms, memory_usage 
FROM system.query_log 
WHERE query_duration_ms > 1000 
ORDER BY query_duration_ms DESC 
LIMIT 10"
```

#### OpenTelemetry Issues
```bash
# Check OTEL collector health
curl http://localhost:13133/

# Verify Prometheus targets
curl http://localhost:9090/api/v1/targets

# Check metrics ingestion
clickhouse-client --query "SELECT count() FROM observability.otel_metrics"
```

### Log Locations

```bash
# ClickHouse logs
tail -f /var/log/clickhouse-server/clickhouse-server.log
tail -f /var/log/clickhouse-server/clickhouse-server.err.log

# OpenTelemetry collector logs
journalctl -u otel-collector -f
```