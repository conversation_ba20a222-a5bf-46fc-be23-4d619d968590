users:
  admin:
    profile: default
    password: <password>
    networks:
      ip: "::/0"
    quota: default
    access_management: 1
  api:
    profile: default
    password: <password>
    networks:
      ip: "::/0"
    quota: default
    access_management: 1
  worker:
    profile: default
    password: <password>
    networks:
      ip: "::/0"
    quota: default
    access_management: 1

profiles:
  default:
    max_memory_usage: 6000000000
    max_memory_usage_for_user: 4000000000
    max_bytes_before_external_group_by: 4000000000
    max_bytes_before_external_sort: 4000000000
    use_uncompressed_cache: 0
    load_balancing: in_order
    log_queries: 1
    max_threads: 4
    queue_max_wait_ms: 3000
    stream_like_engine_allow_direct_select: 0
    min_insert_block_size_rows_for_materialized_views: 1000
    min_insert_block_size_bytes_for_materialized_views: 100000000

quotas:
  default:
    interval:
      # Sets a sliding time window (3600s = 1 hour)
      duration: 3600
      queries: 0
      errors: 0
      result_rows: 0
      read_rows: 0
      execution_time: 0
