logger:
  level: debug
  console: true
  log: /var/log/clickhouse-server/clickhouse-server.log
  errorlog: /var/log/clickhouse-server/clickhouse-server.err.log

tcp_port: 9000
http_port: 8123
listen_host: 0.0.0.0
interserver_http_host: ch-server
interserver_http_port: 9009

max_connections: 1024
keep_alive_timeout: 64
max_concurrent_queries: 20
uncompressed_cache_size: 0
mark_cache_size: 8000000000

path: /var/lib/clickhouse/
tmp_path: /var/lib/clickhouse/tmp/
user_files_path: /var/lib/clickhouse/user_files/

users_config: users.xml
default_profile: default
default_database: observability
timezone: Asia/Kolkata
mlock_executable: false

format_schema_inference: 0
format_schema_inference_cache_size: 10000
format_schema_inference_sample_rows: 1000

async_insert_threads: 2
background_buffer_flush_schedule_pool_size: 2
background_common_pool_size: 2
background_distributed_schedule_pool_size: 1
background_fetches_pool_size: 1
background_merges_mutations_concurrency_ratio: 2
background_message_broker_schedule_pool_size: 1
background_move_pool_size: 1
background_pool_size: 5
background_schedule_pool_size: 1
backup_threads: 1
merge_tree:
  number_of_free_entries_in_pool_to_execute_mutation: 1
  number_of_free_entries_in_pool_to_execute_optimize_entire_partition: 1
  number_of_free_entries_in_pool_to_lower_max_size_of_merge: 1
  parts_to_delay_insert: 1000
  parts_to_throw_insert: 3000

max_server_memory_usage: 6000000000
max_server_memory_usage_to_ram_ratio: 0.75

prometheus:
  endpoint: /metrics
  port: 9363
  metrics: true
  events: true
  asynchronous_metrics: true
  errors: true

query_log:
  database: system
  table: query_log
  flush_interval_milliseconds: 7500

metric_log:
  database: system
  table: metric_log
  flush_interval_milliseconds: 7500
  collect_interval_milliseconds: 1000

asynchronous_metric_log:
  database: system
  table: asynchronous_metric_log
  flush_interval_milliseconds: 7000

opentelemetry_span_log:
  engine: |
    engine MergeTree
    partition by toYYYYMM(finish_date)
    order by (finish_date, finish_time_us, trace_id)
  database: system
  table: opentelemetry_span_log
  flush_interval_milliseconds: 7500

crash_log:
  database: system
  table: crash_log
  # partition_by: ""
  flush_interval_milliseconds: 1000

processors_profile_log:
  database: system
  table: processors_profile_log
  partition_by: toYYYYMM(event_date)
  flush_interval_milliseconds: 7500

part_log:
  database: system
  table: part_log
  partition_by: toYYYYMM(event_date)
  flush_interval_milliseconds: 7500

trace_log:
  database: system
  table: trace_log
  partition_by: toYYYYMM(event_date)
  flush_interval_milliseconds: 7500

query_thread_log:
  database: system
  table: query_thread_log
  partition_by: toYYYYMM(event_date)
  flush_interval_milliseconds: 7500

query_views_log:
  database: system
  table: query_views_log
  partition_by: toYYYYMM(event_date)
  flush_interval_milliseconds: 7500

remote_servers:
  hdx_cluster:
    - shard:
        - replica:
            host: ch-server
            port: 9000

distributed_ddl:
  path: /clickhouse/task_queue/ddl

format_schema_path: /var/lib/clickhouse/format_schemas/
# keeper_server: #keeper means zookeeper
#   tcp_port: 2181
#   server_id: 1
#   log_storage_path: /var/lib/clickhouse/coordination/log
#   snapshot_storage_path: /var/lib/clickhouse/coordination/snapshots

#   coordination_settings:
#     operation_timeout_ms: 10000
#     session_timeout_ms: 30000
#     raft_logs_level: trace

#   raft_configuration:
#     server:
#       id: 1
#       hostname: localhost
#       port: 9234
