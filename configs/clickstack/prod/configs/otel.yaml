# This configuration collects system logs and metric for OSX and Linux systems, sending the results to ClickStack via the OTLP endpoint on port 4317.
receivers:
  prometheus:
    config:
      global:
        scrape_interval: 15s

      scrape_configs:
        - job_name: "node_exporter"
          static_configs:
            - targets: ["***********:9101"]
        - job_name: "node_exporter_monitoring_ec2"
          metrics_path: "/metrics"
          static_configs:
            - targets: ["localhost:9100"] # Address of the node_exporter
              labels:
                service: monitoring
                instance: monitoring-ec2

        - job_name: "nats-server-nodes"
          metrics_path: "/metrics"
          static_configs:
            - targets: ["nats-1.ravenclaw-ns:9100"]
              labels:
                service: nats-node-1
            - targets: ["nats-2.ravenclaw-ns:9100"]
              labels:
                service: nats-node-2
            - targets: ["nats-3.ravenclaw-ns:9100"]
              labels:
                service: nats-node-3
            - targets: ["nats-4.ravenclaw-ns:9100"]
              labels:
                service: nats-node-4

        - job_name: "prometheus"
          metrics_path: "/metrics"
          static_configs:
            - targets: ["localhost:9090"]

        - job_name: "nats-surveyor"
          metrics_path: "/metrics"
          static_configs:
            - targets: ["nats-1.ravenclaw-ns:7777"]
              labels:
                service: nats-node-1
            - targets: ["nats-2.ravenclaw-ns:7777"]
              labels:
                service: nats-node-2
            - targets: ["nats-3.ravenclaw-ns:7777"]
              labels:
                service: nats-node-3
            - targets: ["nats-4.ravenclaw-ns:7777"]
              labels:
                service: nats-node-4

        - job_name: "prometheus-nats-exporter"
          metrics_path: "/metrics"
          static_configs:
            - targets: ["nats-1.ravenclaw-ns:7778"]
              labels:
                service: nats-node-1
            - targets: ["nats-2.ravenclaw-ns:7778"]
              labels:
                service: nats-node-2
            - targets: ["nats-3.ravenclaw-ns:7778"]
              labels:
                service: nats-node-3
            - targets: ["nats-4.ravenclaw-ns:7778"]
              labels:
                service: nats-node-4

        - job_name: "sql_exporter"
          metrics_path: "/metrics"
          scrape_interval: 6h
          static_configs:
            - targets: ["localhost:9399"]

        - job_name: "benthos"
          metrics_path: "/metrics"
          static_configs:
            - targets: ["benthos.ravenclaw-ns:4195"]
              labels:
                service: benthos

        - job_name: "clickhouse"
          metrics_path: "/metrics"
          static_configs:
            - targets: ["clickhouse.ravenclaw-ns:9363"]
              labels:
                service: clickhouse
            - targets: ["clickhouse.ravenclaw-ns:9100"]
              labels:
                service: clickhouse-node-exporter

        - job_name: "metabase"
          metrics_path: "metrics"
          static_configs:
            - targets: ["metabase.ravenclaw-ns:9192"]
              labels:
                service: metabase
            - targets: ["metabase.ravenclaw-ns:9101"]
              labels:
                service: metabase-node-exporter

        - job_name: "pushgateway"
          metrics_path: "/metrics"
          scrape_interval: 20s
          static_configs:
            - targets: ["localhost:9091"]

  hostmetrics:
    collection_interval: 30s
    scrapers:
      cpu:
      load:
      memory:
      disk:
      filesystem:
      network:
      paging:
      processes:
      process:
      system:

exporters:
  clickhouse:
    endpoint: tcp://ch-server:9000?dial_timeout=10s&compress=lz4&async_insert=1
    database: ${env:HYPERDX_OTEL_EXPORTER_CLICKHOUSE_DATABASE}
    password: ${env:CLICKHOUSE_PASSWORD}
    user: ${env:CLICKHOUSE_USER}
    ttl: 1h
    timeout: 5s
    sending_queue:
      queue_size: 1000
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s

service:
  pipelines:
    # logs:
    #   receivers: [otlp]
    #   exporters: [otlp]
    metrics:
      receivers: [hostmetrics, prometheus]
      exporters: [otlp]
