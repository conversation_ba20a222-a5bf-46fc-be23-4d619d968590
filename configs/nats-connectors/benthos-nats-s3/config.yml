input:
  label: "events_dlq"
  nats_jetstream:
    urls: ["${NATS_URLS}"]
    subject: "${NATS_SUBJECT}"
    stream: "${NATS_STREAM}"
    deliver: "${NATS_CONSUMER_DELIVER}"
    queue: ""
    durable: "${NATS_CONSUMER_DURABLE}"
    bind: true
pipeline:
  processors:
    - label: "json"
      mapping: |
        root = {
          "metadata": meta(),
          "data": content().string()
        }
output:
  aws_s3:
    bucket: "${AWS_S3_BUCKET}"
    path: ${AWS_S3_PATH}/${! meta("nats_timestamp_unix_nano")}.json
    region: "${BUCKET_REGION}"
    content_type: application/json
    metadata:
      exclude_prefixes: ["x", "nats", "X"]
    max_in_flight: 64
    batching:
      count: 0
logger:
  level: DEBUG
  format: logfmt
  add_timestamp: true
  level_name: level
  timestamp_name: time
  message_name: msg
  static_fields:
    "@service": benthos
  file:
    path: ""
    rotate: false
    rotate_max_age_days: 0
