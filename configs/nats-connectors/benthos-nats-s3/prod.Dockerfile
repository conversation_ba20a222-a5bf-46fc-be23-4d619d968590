FROM --platform=linux/arm64 771151923073.dkr.ecr.ap-south-1.amazonaws.com/benthos:1.0.0

# Create a directory for the configuration
WORKDIR /app

# Set environment variables
ENV NATS_URLS='nats://localhost:4222'
ENV NATS_SUBJECT="dlq.>"
ENV NATS_STREAM="EVENTS_DLQ"
ENV NATS_CONSUMER_DELIVER="all"
ENV NATS_CONSUMER_DURABLE="s3-archiver-test"

# AWS related environment variables
ENV AWS_S3_BUCKET="nats-s3-archival-bucket"
ENV AWS_S3_PATH="test/local"
ENV BUCKET_REGION="ap-south-1"

# Copy the config file
COPY ./config.yml /app/config.yml

# Command to run
CMD ["run", "./config.yml"]
