# Running a NATS Cluster

1. Log in as root

```bash
sudo su -
apt-get update
apt-get upgrade -y
```
---


2. Download nats binary and add it to bin folder

```bash
curl -sf https://binaries.nats.dev/nats-io/nats-server/v2@v2.10.25 | sh
sudo mkdir -p /etc/nats
sudo mv nats-server /usr/local/bin/
```

---

3. Add the [server.conf](./nats-server.conf) file to the `/etc/nats` folder
   - Don't forget to change the following:
      - user_name
      - password
      - service discovery namespace
      - nats node service name
      - cluster name
      - server name
    - If you want error log level to set to debug and enable tracing, then uncomment the following lines
      ``` bash
      # debug: true
      # trace: true
      ```
   - Path - `/etc/nats/server.conf`

---

4. Make empty jetstream directory and empty log file
```bash
sudo mkdir -p /data/jetstream
sudo touch /tmp/nats-server.log
```

---

5. Now create a nats systemctl service by referring this [README](../system-service/README.md)

---

6. Check the logs of nats

```bash
sudo cat /tmp/nats-server.log
OR
sudo tail -n 10 /tmp/nats-server.log
```

---
