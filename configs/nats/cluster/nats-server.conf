server_name=<nats-server-name> # e.g. nats-1-dev, nats-2-dev, nats-3-dev
listen=4222
jetstream {
    store_dir: /data/jetstream
    max_mem: 1G
    max_file: 100G
}
max_payload: 64M
server_tags: ["events"]

# set the below options to true only if needed
# debug: true
# trace: true

logtime: true
logfile_size_limit: 1GB
log_file: "/tmp/nats-server.log"
http_port: 8222

accounts: {
    $SYS: {
        users: [
            {user: <system_username>, password: <system_password>}

        ]
    },
    JETSTREAM: {
        jetstream: true
        users: [
            {user: <user_1>, password: <password_1>}
            {user: <user_2>, password: <password_2>}
        ]
    }
}

cluster {
  name: <cluster_name> # e.g. nats-cluster-dev
  listen: 0.0.0.0:6222

  authorization {
        user: <cluster_username>
        password: <cluster_password>
        timeout: 1s
   }


  routes: [
    nats://<cluster_user>:<cluster_password>@<nats_server_name>.<namespace>:6222

    # add only other node endpoints
    # e.g.
    # nats://nats-2-dev.ravenclaw-dev-ns:6222
    # nats://nats-3-dev.ravenclaw-dev-ns:6222
  ]
}
