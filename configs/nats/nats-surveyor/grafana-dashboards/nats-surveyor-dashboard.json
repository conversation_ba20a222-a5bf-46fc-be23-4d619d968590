{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "8.3.4"}, {"type": "panel", "id": "heatmap", "name": "Heatmap", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "iteration": 1644365183491, "links": [], "liveNow": false, "panels": [{"description": "Combined messages received by all servers", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 0}, "id": 79, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(rate(nats_core_recv_msgs_count{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "Received", "refId": "A"}], "title": "Received Messages", "type": "stat"}, {"description": "Combined messages sent by all servers", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 0}, "id": 80, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(rate(nats_core_sent_msgs_count{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "<PERSON><PERSON>", "refId": "A"}], "title": "Sent Messages", "type": "stat"}, {"description": "Combined bytes received by all servers", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 0}, "id": 53, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(rate(nats_core_recv_bytes{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "Received", "refId": "A"}], "title": "Received Bytes", "type": "stat"}, {"description": "Combined bytes sent by all servers", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 0}, "id": 52, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(rate(nats_core_sent_bytes{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "<PERSON><PERSON>", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "stat"}, {"description": "Total current connections across all servers", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 12, "y": 0}, "id": 45, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(nats_core_connection_count{server_cluster=~\"$cluster\"})", "refId": "A"}], "title": "Connections", "type": "stat"}, {"description": "Slow consumers seen per second", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 14, "y": 0}, "id": 56, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(rate(nats_core_slow_consumer_count{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "Slow", "refId": "A"}], "title": "Slow Consumers", "type": "stat"}, {"description": "Number of known clusters", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 16, "y": 0}, "id": 55, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "count(sum(nats_core_info{server_cluster=~\"$cluster\"}) by (server_cluster))", "legendFormat": "Servers", "refId": "A"}], "title": "Clusters", "type": "stat"}, {"description": "Number of known servers", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 18, "y": 0}, "id": 78, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "count(nats_core_info{server_cluster=~\"$cluster\"})", "legendFormat": "Servers", "refId": "A"}], "title": "Servers", "type": "stat"}, {"description": "Number of routes reported as active", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 20, "y": 0}, "id": 49, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(nats_core_route_count{server_cluster=~\"$cluster\"})", "refId": "A"}], "title": "Route Connections", "type": "stat"}, {"description": "Number of routes reported as active", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "0"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 22, "y": 0}, "id": 50, "interval": "", "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(nats_core_gateway_count{server_cluster=~\"$cluster\"})", "legendFormat": "Gateways", "refId": "A"}], "title": "Gateway Connections", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 248, "panels": [], "title": "JetStream Status", "type": "row"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 5, "x": 0, "y": 5}, "hideTimeOverride": true, "id": 258, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(nats_core_jetstream_filestore_size_bytes{})", "hide": false, "interval": "", "legendFormat": "Capacity", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(nats_core_jetstream_filestore_used_bytes{})", "hide": false, "interval": "", "legendFormat": "Consumed", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(nats_core_jetstream_filestore_reserved_bytes{})", "hide": false, "interval": "", "legendFormat": "Reserved", "refId": "C"}], "title": "JS Filestore", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 5, "x": 5, "y": 5}, "hideTimeOverride": true, "id": 268, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(nats_core_jetstream_memstore_size_bytes{})", "hide": false, "interval": "", "legendFormat": "Capacity", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(nats_core_jetstream_memstore_used_bytes{})", "hide": false, "interval": "", "legendFormat": "Consumed", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(nats_core_jetstream_memstore_reserved_bytes{})", "hide": false, "interval": "", "legendFormat": "Reserved", "refId": "C"}], "title": "JS Memstore", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.+_bytes.+/"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 4, "w": 2, "x": 10, "y": 5}, "hideTimeOverride": true, "id": 207, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "name"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "group by(server_domain) (nats_core_jetstream_info{server_jetstream=\"true\"})", "hide": false, "interval": "", "legendFormat": "{{server_domain}}", "refId": "A"}], "title": "JS  Domains", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.+_bytes.+/"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 4, "w": 2, "x": 12, "y": 5}, "hideTimeOverride": true, "id": 217, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "name"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "(nats_core_jetstream_cluster_raft_group_leader * on(server_id) group_left(server_cluster,server_name, server_domain) nats_core_jetstream_info) > 0", "hide": false, "interval": "", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "JS  Meta-Leader", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.+_bytes.+/"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 4, "w": 2, "x": 14, "y": 5}, "hideTimeOverride": true, "id": 197, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "count(group by(server_cluster) (nats_core_jetstream_info{server_jetstream=\"true\"}))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "JS  Clusters", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.+_bytes.+/"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 4, "w": 2, "x": 16, "y": 5}, "hideTimeOverride": true, "id": 177, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "count(nats_core_jetstream_info{server_jetstream=\"true\"})", "hide": false, "interval": "", "legendFormat": "Enabled", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "count(nats_core_jetstream_enabled == 1)", "hide": false, "interval": "", "legendFormat": "Running", "refId": "B"}], "title": "JS Servers", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.+_bytes.+/"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 4, "w": 2, "x": 18, "y": 5}, "hideTimeOverride": true, "id": 278, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(nats_core_jetstream_ha_assets{})", "hide": false, "interval": "", "legendFormat": "Enabled", "refId": "A"}], "title": "JS HA Assets", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.+_bytes.+/"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 4, "w": 2, "x": 20, "y": 5}, "hideTimeOverride": true, "id": 237, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "nats_core_jetstream_cluster_raft_group_replicas * on(server_id) group_left(server_domain, server_cluster,server_name) nats_core_jetstream_info > 0", "hide": false, "interval": "", "legendFormat": "{{server_domain}}:{{server_name}}", "refId": "A"}], "title": "Meta's Replicas", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.+_bytes.+/"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 4, "w": 2, "x": 22, "y": 5}, "hideTimeOverride": true, "id": 227, "interval": "", "links": [], "maxPerRow": 12, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "8.3.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum by (server_domain, leader) (nats_core_jetstream_cluster_raft_group_info{raft_group=\"_meta_\"})", "hide": false, "interval": "", "legendFormat": "{{leader}}", "refId": "A"}], "title": "<PERSON><PERSON>'s Votes", "transparent": true, "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 43, "panels": [], "title": "Cluster Overview", "type": "row"}, {"description": "Combined current connections", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Connections", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Subscriptions"}, "properties": [{"id": "custom.axisLabel", "value": "Subscriptions"}]}]}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 7}, "id": 33, "links": [], "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(nats_core_connection_count{server_cluster=~\"$cluster\"})", "format": "time_series", "instant": false, "interval": "", "legendFormat": "Connections", "refId": "A"}, {"expr": "sum(nats_core_subs_count{server_cluster=~\"$cluster\"})", "instant": false, "interval": "", "legendFormat": "Subscriptions", "refId": "B"}], "title": "Cluster Connections", "type": "timeseries"}, {"description": "Combined slow consumers detected per second", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 10}, "id": 37, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(rate(nats_core_slow_consumer_count{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "Slow Consumers", "refId": "A"}], "title": "Cluster Slow Consumers", "type": "timeseries"}, {"description": "Combined total messages for all servers", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "messages / second", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 14}, "id": 31, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(rate(nats_core_sent_msgs_count{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "<PERSON><PERSON>", "refId": "A"}, {"expr": "sum(rate(nats_core_recv_msgs_count{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "Received", "refId": "B"}], "title": "Cluster Message Traffic", "type": "timeseries"}, {"description": "Combined cluster network usage", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 14}, "id": 29, "interval": "", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(rate(nats_core_sent_bytes{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "<PERSON><PERSON>", "refId": "A"}, {"expr": "sum(rate(nats_core_recv_bytes{server_cluster=~\"$cluster\"}[1m]))", "legendFormat": "Received", "refId": "B"}], "title": "Cluster Network Usage", "type": "timeseries"}, {"description": "Distribution of NATS Server Versions across the cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 20}, "id": 8, "links": [], "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(nats_core_info{server_cluster=~\"$cluster\"}) by (server_version)", "legendFormat": "{{server_version}}", "refId": "A"}], "title": "Server Versions", "type": "timeseries"}, {"cards": {}, "color": {"cardColor": "#b4ff00", "colorScale": "linear", "colorScheme": "interpolateYlOrRd", "exponent": 0.5, "mode": "opacity"}, "dataFormat": "timeseries", "description": "RTT times for the cluster as observed from the Surveyor", "gridPos": {"h": 5, "w": 12, "x": 12, "y": 20}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 92, "interval": "", "legend": {"show": false}, "reverseYBuckets": false, "targets": [{"expr": "nats_core_rtt_nanoseconds", "format": "time_series", "instant": false, "legendFormat": "RTT", "refId": "A"}], "title": "Cluster RTT", "tooltip": {"show": true, "showHistogram": true}, "type": "heatmap", "xAxis": {"show": true}, "yAxis": {"format": "ns", "logBase": 1, "show": true}, "yBucketBound": "auto"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 18, "panels": [{"description": "Point in time view of clients connected to each Server", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "current connections", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 6}, "id": 14, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "nats_core_connection_count{server_cluster=~\"$cluster\"}", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Current Connections", "type": "timeseries"}, {"description": "Number of subscriptions active on the server", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "subscriptions", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 6}, "id": 20, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "nats_core_subs_count{server_cluster=~\"$cluster\"}", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Active Subscriptions", "type": "timeseries"}, {"description": "Connections received by the server in a second", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "connections / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 13}, "id": 16, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_total_connection_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Connections per second", "type": "timeseries"}, {"description": "The difference in subscription count per minute", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "subscriptions", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 13}, "id": 22, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "delta(nats_core_subs_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Subscription Delta", "type": "timeseries"}, {"description": "Slow consumers detected by each server", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "slow / second", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 20}, "id": 41, "interval": "", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_slow_consumer_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Slow Consumers", "type": "timeseries"}], "title": "Client Connection Detail", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 71, "panels": [{"description": "Bytes sent for each gateway", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "bytes / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "id": 72, "interval": "", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_gateway_sent_bytes{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}} - {{server_cluster}} -> {{server_gateway_name}}", "refId": "A"}], "title": "Gateway Bytes Sent", "type": "timeseries"}, {"description": "Bytes received for each gateway", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "bytes / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "id": 73, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_gateway_recv_bytes{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}} - {{server_cluster}} -> {{server_gateway_name}}", "refId": "A"}], "title": "Gateway Bytes Received", "type": "timeseries"}, {"description": "Messages sent for each gateway", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "messages / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "id": 74, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_gateway_sent_msgs_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}} - {{server_cluster}} -> {{server_gateway_name}}", "refId": "A"}], "title": "Gateway Messages Sent", "type": "timeseries"}, {"description": "Messages received for each gateway", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "messages / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "id": 75, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_gateway_recv_msg_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}} - {{server_cluster}} -> {{server_gateway_name}}", "refId": "A"}], "title": "Gateway Messages Received", "type": "timeseries"}], "title": "Gateway Details", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 60, "panels": [{"description": "Bytes sent for each route", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "bytes / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 8}, "id": 62, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_route_sent_bytes{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "Server {{server_name}} - Route {{server_route_name}}", "refId": "A"}], "title": "Route Bytes Sent", "type": "timeseries"}, {"description": "Bytes received for each route", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "bytes / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 8}, "id": 63, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_route_recv_bytes{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "Server {{server_name}} - Route {{server_route_name}}", "refId": "A"}], "title": "Route Bytes Received", "type": "timeseries"}, {"description": "Messages sent for each route", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "messages / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "id": 64, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_route_sent_msg_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "Server {{server_name}} - Route {{server_route_name}}", "refId": "A"}], "title": "Route Messages Sent", "type": "timeseries"}, {"description": "Messages received for each route", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "messages / sec", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "id": 65, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_route_recv_msg_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "Server {{server_name}} - Route {{server_route_name}}", "refId": "A"}], "title": "Route Messages Received", "type": "timeseries"}, {"description": "The number of servers in each cluster", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "cluster size", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 22}, "id": 10, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "sum(nats_core_info{server_cluster=~\"$cluster\"}) by (server_cluster)", "legendFormat": "{{server_cluster}}", "refId": "A"}], "title": "Cluster Size", "type": "timeseries"}, {"description": "Data in the pending buffers that needs to be sent to remotes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 22}, "id": 67, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "nats_core_route_pending_bytes{server_cluster=~\"$cluster\"}", "legendFormat": "Server {{server_name}} - Route {{server_route_name}}", "refId": "A"}], "title": "Route Pending Data", "type": "timeseries"}], "title": "Route Details", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 24, "panels": [{"description": "Data sent per second", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 9}, "id": 26, "interval": "", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_sent_bytes{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"description": "Data received per second", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 9}, "id": 27, "interval": "", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_recv_bytes{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "<PERSON><PERSON> Received", "type": "timeseries"}, {"description": "Messages sent per second", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "id": 68, "interval": "", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_sent_msgs_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Messages Sent", "type": "timeseries"}, {"description": "Messages received per second", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "id": 69, "interval": "", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_recv_msgs_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Messages Received", "type": "timeseries"}], "title": "Network Usage Detail", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 29}, "id": 77, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "CPU used by the NATS Server process", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "cpu %", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 10}, "id": 4, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "nats_core_cpu_percentage{server_cluster=~\"$cluster\"}", "legendFormat": "{{server_name}}", "refId": "B"}], "title": "CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The number of bytes in use by the NATS server", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bytes", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 10}, "id": 2, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "nats_core_mem_bytes{server_cluster=~\"$cluster\"}", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Memory", "type": "timeseries"}], "title": "Node Resource Usage", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 30}, "id": 84, "panels": [{"description": "The number of NATS Servers expected to reply vs how many replied during a poll", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Expected", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 11}, "id": 82, "links": [], "options": {"legend": {"calcs": ["lastNotNull", "min"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "nats_survey_expected_count", "legendFormat": "Expected", "refId": "A"}, {"expr": "nats_survey_surveyed_count", "legendFormat": "Responded", "refId": "B"}], "title": "Expected vs Received", "type": "timeseries"}, {"description": "Cluster wide RTT as calculated from the Surveyor", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "RTT", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ns"}, "overrides": []}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 18}, "id": 39, "options": {"legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "rate(nats_core_rtt_nanoseconds[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "title": "Cluster RTT", "type": "timeseries"}, {"description": "Number of times the surveyor reconnected to the network in a minute", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 23}, "id": 90, "interval": "", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": "increase(nats_survey_nats_reconnects[5m])", "legendFormat": "Reconnections", "refId": "A"}], "title": "NATS Reconnects", "type": "timeseries"}, {"description": "Time it takes the surveyor to gather the statistics from surveyed NATS Servers", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "time per poll", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 23}, "id": 86, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"expr": " rate(nats_survey_duration_seconds_sum[5m])\n/\n  rate(nats_survey_duration_seconds_count[5m])", "legendFormat": "Poll Time", "refId": "A"}], "title": "Poll Time", "type": "timeseries"}], "title": "Surveyor", "type": "row"}], "refresh": "5s", "schemaVersion": 34, "style": "dark", "tags": [], "templating": {"list": [{"hide": 0, "label": "datasource", "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"allValue": ".*", "current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(nats_core_info, server_cluster)", "hide": 0, "includeAll": true, "label": "Cluster", "multi": true, "name": "cluster", "options": [], "query": {"query": "label_values(nats_core_info, server_cluster)", "refId": "Prometheus-cluster-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "query_result(nats_survey_expected_count)", "hide": 2, "includeAll": false, "multi": false, "name": "expected", "options": [], "query": {"query": "query_result(nats_survey_expected_count)", "refId": "Prometheus-expected-Variable-Query"}, "refresh": 1, "regex": "/.* ([^\\ ]*) .*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "NATS Surveyor", "uid": "GGxJ_5oZy", "version": 1, "weekStart": ""}