{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": 2, "iteration": 1607509798782, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["surveyor"], "title": "Surveyor", "type": "dashboards"}], "panels": [{"datasource": null, "description": "", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}, "id": 17, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "sum(nats_core_connection_count{server_cluster=~\"$cluster\"})", "interval": "", "legendFormat": "Connections", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}, "id": 19, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "sum(nats_core_subs_count{server_cluster=~\"$cluster\"})", "interval": "", "legendFormat": "Subscriptions", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "yellow", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0}, "id": 21, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "sum(rate(nats_core_total_connection_count{server_cluster=~\"$cluster\"}[1m]))", "interval": "", "legendFormat": "Connections / Second", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 5, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "Point in time view of clients connected to each Server", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "nats_core_connection_count{server_cluster=~\"$cluster\"}", "interval": "", "legendFormat": "{{server_name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Current Connections", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "current connections", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "Point in time view of clients connected to each Server", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "percentage": true, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "nats_core_connection_count{server_cluster=~\"$cluster\"}", "interval": "", "legendFormat": "{{server_name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Current Connections %", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": "current connections", "logBase": 1, "max": "110", "min": null, "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "Connections received by the server in a second", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "rate(nats_core_total_connection_count{server_cluster=~\"$cluster\"}[1m])", "interval": "", "legendFormat": "{{server_name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connections per second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": "connections / sec", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "Slow consumers detected by each server", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 15, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(nats_core_slow_consumer_count{server_cluster=~\"$cluster\"}[1m])", "legendFormat": "{{server_name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Slow Consumers", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "slow / second", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Connections", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 9, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "Number of subscriptions active on the server", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 10}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "nats_core_subs_count{server_cluster=~\"$cluster\"}", "legendFormat": "{{server_name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Active Subscriptions", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": "subscriptions", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "The difference in subscription count per minute", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 10}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(delta(nats_core_subs_count{server_cluster=~\"$cluster\"}[1m]))", "interval": "", "legendFormat": "{{server_name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Subscription Delta", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "subscriptions", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Subscriptions", "type": "row"}], "refresh": "30s", "schemaVersion": 26, "style": "dark", "tags": ["surveyor"], "templating": {"list": [{"allValue": ".*", "current": {"selected": false, "text": "All", "value": ["$__all"]}, "datasource": "Prometheus", "definition": "label_values(nats_core_info, server_cluster)", "hide": 0, "includeAll": true, "label": "Cluster", "multi": true, "name": "cluster", "options": [], "query": "label_values(nats_core_info, server_cluster)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "27", "value": "27"}, "datasource": "Prometheus", "definition": "query_result(nats_survey_expected_count)", "hide": 2, "includeAll": false, "label": null, "multi": false, "name": "expected", "options": [{"selected": true, "text": "27", "value": "27"}], "query": "query_result(nats_survey_expected_count)", "refresh": 0, "regex": "/.* ([^\\ ]*) .*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Clients", "uid": "MCcOJo0Mz", "version": 21}