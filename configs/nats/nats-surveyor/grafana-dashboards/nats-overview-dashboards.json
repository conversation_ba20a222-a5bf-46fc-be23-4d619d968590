{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 7, "iteration": 1608137093702, "links": [{"asDropdown": false, "icon": "external link", "includeVars": true, "keepTime": true, "tags": ["surveyor"], "title": "Surveyor", "type": "dashboards"}], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 14, "panels": [], "title": "Client Overview", "type": "row"}, {"cacheTimeout": null, "datasource": null, "description": "Total current connections across all servers", "fieldConfig": {"defaults": {"custom": {}, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 1}, "id": 2, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "sum(nats_core_connection_count{server_cluster=~\"$cluster\"})", "interval": "", "legendFormat": "Connections", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 1}, "id": 16, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "sum(rate(nats_core_total_connection_count{server_cluster=~\"$cluster\"}[1m]))", "interval": "", "legendFormat": "Connections / sec", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 1}, "id": 19, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "(sum(rate(nats_core_sent_msgs_count{server_cluster=~\"$cluster\"}[1m])) + sum(rate(nats_core_recv_msgs_count{server_cluster=~\"$cluster\"}[1m])))", "interval": "", "legendFormat": "Messages/sec", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"cacheTimeout": null, "datasource": null, "description": "Total current connections across all servers", "fieldConfig": {"defaults": {"custom": {}, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 1}, "id": 17, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "sum(nats_core_subs_count{server_cluster=~\"$cluster\"})", "interval": "", "legendFormat": "Subscriptions", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 12, "panels": [], "title": "Cluster Overview", "type": "row"}, {"cacheTimeout": null, "datasource": null, "description": "Number of known servers", "fieldConfig": {"defaults": {"custom": {}, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 8}, "id": 6, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "count(nats_core_info{server_cluster=~\"$cluster\"})", "legendFormat": "Servers", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"cacheTimeout": null, "datasource": null, "description": "Number of known clusters", "fieldConfig": {"defaults": {"custom": {}, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 8}, "id": 4, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "count(sum(nats_core_info{server_cluster=~\"$cluster\"}) by (server_cluster))", "interval": "", "legendFormat": "Clusters", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"cacheTimeout": null, "datasource": null, "description": "Number of routes reported as active", "fieldConfig": {"defaults": {"custom": {}, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 8}, "id": 8, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "sum(nats_core_route_count{server_cluster=~\"$cluster\"})", "interval": "", "legendFormat": "Route Connections", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"cacheTimeout": null, "datasource": null, "description": "Number of routes reported as active", "fieldConfig": {"defaults": {"custom": {}, "mappings": [{"id": 0, "op": "=", "text": "0", "type": 1, "value": "null"}], "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 8}, "id": 10, "interval": "", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "7.1.1", "targets": [{"expr": "sum(nats_core_gateway_count{server_cluster=~\"$cluster\"})", "interval": "", "legendFormat": "Gateway Conns", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "", "type": "stat"}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": null, "decimals": null, "description": "Distribution of NATS Server Versions across the cluster", "fieldConfig": {"defaults": {"custom": {"align": null}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 14}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.1.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.+/", "color": "#5794F2"}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(nats_core_info{server_cluster=~\"$cluster\"}) by (server_version)", "legendFormat": "{{server_version}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Server Versions", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "30s", "schemaVersion": 26, "style": "dark", "tags": ["surveyor"], "templating": {"list": [{"allValue": ".*", "current": {"selected": true, "text": "All", "value": ["$__all"]}, "datasource": "Prometheus", "definition": "label_values(cluster)", "hide": 0, "includeAll": true, "label": "Cluster", "multi": true, "name": "cluster", "options": [], "query": "label_values(cluster)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "NATS Overview", "uid": "JY0sCo0Gz", "version": 14}