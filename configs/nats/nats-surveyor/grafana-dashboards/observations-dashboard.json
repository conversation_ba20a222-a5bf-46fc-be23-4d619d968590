{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "6.4.4"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": null, "iteration": 1596189643900, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Percentile of service time spent in the service ", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.10, sum(rate(nats_latency_service_duration_bucket{service=~\"${service}\"}[$range])) by (le,service))", "legendFormat": "{{service}} 10th %", "refId": "A"}, {"expr": "histogram_quantile(0.50, sum(rate(nats_latency_service_duration_bucket{service=~\"${service}\"}[$range])) by (le,service))", "legendFormat": "{{service}} 50th %", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(nats_latency_service_duration_bucket{service=~\"${service}\"}[$range])) by (le,service))", "legendFormat": "{{service}} 95th %", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Service Duration Percentiles", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "duration", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Percentile of total time spent in the service and in the total which includes network overhead ", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}, "id": 15, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.10, sum(rate(nats_latency_total_duration_bucket{service=~\"${service}\"}[$range])) by (le,service))", "legendFormat": "{{service}} 10th %", "refId": "A"}, {"expr": "histogram_quantile(0.50, sum(rate(nats_latency_total_duration_bucket{service=~\"${service}\"}[$range])) by (le,service))", "legendFormat": "{{service}} 50th %", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(nats_latency_total_duration_bucket{service=~\"${service}\"}[$range])) by (le,service))", "legendFormat": "{{service}} 95th %", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total Duration Percentiles", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "duration", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Average times taken to serve a request.\n\n* **service time** is the time taken to execute the service\n* **total time** includes network latencies between all parties", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0}, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(nats_latency_service_duration_sum{service=~\"${service}\"}[$range]) / rate(nats_latency_service_duration_count{service=~\"${service}\"}[$range])) by (service)", "legendFormat": "{{service}} service time", "refId": "A"}, {"expr": "sum(rate(nats_latency_total_duration_sum{service=~\"${service}\"}[$range]) / rate(nats_latency_total_duration_count{service=~\"${service}\"}[$range])) by (service)", "legendFormat": "{{service}} total time", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Average Service Duration", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "duration", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Time distribution spent traversing the NATS cluster", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "id": 4, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/10th/", "color": "#1F60C4"}, {"alias": "/50th/", "color": "#8AB8FF"}, {"alias": "/95th/", "color": "#C0D8FF"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.10, sum(rate(nats_latency_system_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 10th %", "refId": "D"}, {"expr": "histogram_quantile(0.50, sum(rate(nats_latency_system_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 50th %", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(nats_latency_system_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 95th %", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "System RTT Times Percentiles", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "rtt time", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Response time distribution between NATS and the Requesting Client", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/10th/", "color": "#3274D9"}, {"alias": "/50th/", "color": "#8AB8FF"}, {"alias": "/95th/", "color": "#C0D8FF"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.10, sum(rate(nats_latency_requestor_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 10th %", "refId": "D"}, {"expr": "histogram_quantile(0.50, sum(rate(nats_latency_requestor_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 50th %", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(nats_latency_requestor_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 95th %", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requestor RTT Times Percentiles", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "rtt time", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Response time distribution between NATS and the Service", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "id": 17, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/10th/", "color": "#3274D9"}, {"alias": "/50th/", "color": "#8AB8FF"}, {"alias": "/95th/", "color": "#C0D8FF"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.10, sum(rate(nats_latency_responder_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 10th %", "refId": "D"}, {"expr": "histogram_quantile(0.50, sum(rate(nats_latency_responder_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 50th %", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(nats_latency_responder_rtt_bucket{service=~\"${service}\"}[$range])) by (service, le))", "legendFormat": "{{service}} 95th %", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Responder RTT Times Percentiles", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "rtt time", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${DS_PROMETHEUS}", "description": "Number of observations received by status", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "id": 6, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(nats_latency_observation_status_count{service=~\"${service}\"}[$range])) by (service,status)", "legendFormat": "{{service}} {{status}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Observations Statuses Received", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "observations / second", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 20, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".+", "current": {}, "datasource": "${DS_PROMETHEUS}", "definition": "label_values(nats_latency_total_duration_sum, service)", "hide": 0, "includeAll": true, "label": "Service", "multi": true, "name": "service", "options": [], "query": "label_values(nats_latency_total_duration_sum, service)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"text": "1m", "value": "1m"}, "hide": 0, "label": "Range", "name": "range", "options": [{"selected": true, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}], "query": "1m,5m,10m", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Service Observations", "uid": "FVNA1y1Zz", "version": 2}