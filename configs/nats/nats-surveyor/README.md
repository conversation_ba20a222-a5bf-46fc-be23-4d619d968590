# Run NATS Surveyor systemctl service

1. Download NATS Surveyor Binary

```bash
wget https://github.com/nats-io/nats-surveyor/releases/download/v0.4.0/nats-surveyor-v0.4.0-linux-amd64.zip
sudo apt-get install unzip
unzip nats-surveyor-v0.4.0-linux-amd64.zip
sudo mv nats-surveyor-v0.4.0-linux-amd64/nats-surveyor /usr/local/bin/
sudo chmod +x /usr/local/bin/nats-surveyor
sudo mkdir -p /etc/nats-surveyor
```

2. Create a Nats Surveyor Systemctl Service
   - Service file - [service.ini](./nats-surveyor-service.ini)
   - Path - `/etc/systemd/system/nats-surveyor.service`

---

3. Create a new user - `nats`

```bash
sudo useradd --system --no-create-home --shell /usr/sbin/nologin nats
```

4. Give permissions to file and folder for `nats` user
```bash

sudo chown -R nats:nats /usr/local/bin/nats-surveyor
sudo chmod -R 750 /usr/local/bin/nats-surveyor

sudo chown -R nats:nats /etc/systemd/system/nats-surveyor.service
sudo chmod -R 750 /etc/systemd/system/nats-surveyor.service
```

5. Run the following commands to start and enable the service

```bash
sudo systemctl daemon-reload
sudo systemctl enable nats-surveyor
sudo systemctl start nats-surveyor
```

---

6. Check whether service is running or not

```bash
sudo systemctl status nats-surveyor
sudo journalctl -u nats-surveyor
```
---

7. Stop/restart/remove the service

```bash
sudo systemctl restart nats-surveyor
sudo systemctl stop nats-surveyor
sudo systemctl disable nats-surveyor
```
---

# Add Scrape config in `prometheus.yaml`

- Make sure NATS hosted EC2 instance has port 7777 enable to access in security group

```yaml
scrape_configs:
- job_name: 'nats-surveyor'
  metrics_path: '/metrics'
  static_configs:
    - targets: ['<nats_private_ip>:7777']
      labels:
        service: nats
```

---

# Grafana Dashboards

1. Copy Grafana Dashboard yamls from this github repo - [Link](https://github.com/nats-io/nats-surveyor/tree/main/docker-compose/grafana/provisioning/dashboards)
   - Also copied the json files in the repo too - [Here](./grafana-dashboards)

2. [Nats Surveyor Github Repo](https://github.com/nats-io/nats-surveyor/tree/main)
