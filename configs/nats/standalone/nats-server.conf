jetstream {
    store_dir: /data/jetstream
    max_mem: 1G
    max_file: 100G
}
max_payload: 64M
http_port: 8222

# set the below options to true only if needed
# debug: true
# trace: true

logtime: true
logfile_size_limit: 1GB
log_file: "/tmp/nats-server.log"

accounts: {
    \$SYS: {
        users: [
            {user: admin, password: password}

        ]
    },
    JETSTREAM: {
        jetstream: true
        users: [
            {user: <user_name>, password: <password>}
        ]
    }
}
