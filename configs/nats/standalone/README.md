# Running a NATS standalone server

1. Log in as root

```bash
sudo su -
apt-get update
apt-get upgrade -y
```
---


2. Download nats binary and add it to bin folder

```bash
curl -sf https://binaries.nats.dev/nats-io/nats-server/v2@v2.10.14 | sh
mkdir -p /etc/nats
sudo mv nats-server /usr/local/bin/
```

---

3. Add the [server.conf](./nats-server.conf) file to the `/etc/nats` folder
   - Don't forget to change the user_name and password
   - If you want error log level to set to debug and enable tracing, then uncomment the following lines
     ``` bash
     # debug: true
     # trace: true
     ```
   - Path - `/etc/nats/server.conf`

---


4. Check the logs of nats

```bash
sudo cat /tmp/nats-server.log
OR
sudo tail -n 10 /tmp/nats-server.log
```
