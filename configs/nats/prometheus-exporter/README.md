# Run NATS Surveyor systemctl service

> Before moving forward, make sure that for nats config, `nats-server.conf` has `http_port: 8222` enabled
> Doc: [NATS Server Monitoring](https://docs.nats.io/running-a-nats-service/configuration/monitoring)

1. Download NATS Surveyor Binary

```bash
wget https://github.com/nats-io/prometheus-nats-exporter/releases/download/v0.15.0/prometheus-nats-exporter-v0.15.0-linux-x86_64.tar.gz
tar -xzf prometheus-nats-exporter-v0.15.0-linux-x86_64.tar.gz
sudo mv prometheus-nats-exporter /usr/local/bin/
sudo chmod +x /usr/local/bin/prometheus-nats-exporter
sudo mkdir -p /etc/prometheus-nats-exporter
```

2. Create a Nats Surveyor Systemctl Service
   - Service file - [service.ini](./prometheus-nats-exporter.ini)
   - Path - `/etc/systemd/system/prometheus-nats-exporter.service`

---

3. Create a new user - `nats`

```bash
sudo useradd --system --no-create-home --shell /usr/sbin/nologin nats

```

4. Give permissions to file and folder for `nats` user
```bash
sudo touch /tmp/prometheus-nats-exporter.log
sudo chown -R nats:nats /usr/local/bin/prometheus-nats-exporter
sudo chmod -R 750 /usr/local/bin/prometheus-nats-exporter

sudo chown -R nats:nats /etc/systemd/system/prometheus-nats-exporter.service
sudo chmod -R 750 /etc/systemd/system/prometheus-nats-exporter.service

sudo chown -R nats:nats /tmp/prometheus-nats-exporter.log
sudo chmod -R 750 /tmp/prometheus-nats-exporter.log
```

5. Run the following commands to start and enable the service

```bash
sudo systemctl daemon-reload
sudo systemctl enable prometheus-nats-exporter
sudo systemctl start prometheus-nats-exporter
```

---

6. Check whether service is running or not

```bash
sudo systemctl status prometheus-nats-exporter
sudo journalctl -u prometheus-nats-exporter
```
---

7. Stop/restart/remove the service

```bash
sudo systemctl restart prometheus-nats-exporter
sudo systemctl stop prometheus-nats-exporter
sudo systemctl disable prometheus-nats-exporter
```
---

# Add Scrape config in `prometheus.yaml`

- Make sure NATS hosted EC2 instance has port 7778 enable to access in security group

```yaml
scrape_configs:
- job_name: 'prometheus-nats-exporter'
  metrics_path: '/metrics'
  static_configs:
    - targets: ['<nats_private_ip>:7778']
      labels:
        service: nats
```

---

# Grafana Dashboards

1. Nats Grafana Dashboards
    1. [NATS Server Dashboard](https://grafana.com/grafana/dashboards/2279-nats-servers/)
    2. [NATS JetStream](https://grafana.com/grafana/dashboards/14725-nats-jetstream/)
    3. [NATS Server Dashboard](https://grafana.com/grafana/dashboards/16256-nats-server-dashboard/)
    4. [NATS JetStream](https://grafana.com/grafana/dashboards/14862-nats-jetstream/)
    5. [NATS Dashboard Json (official)](https://github.com/nats-io/prometheus-nats-exporter/tree/main/walkthrough)

2. [Prometheus Nats Exporter Github Repo](https://github.com/nats-io/prometheus-nats-exporter)
