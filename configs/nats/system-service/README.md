# Run NATS systemctl service

1. Create a Nats Systemctl Service
   - Service file - [service.ini](./nats-systemctl-service.ini)
   - Path - `/etc/systemd/system/nats-server.service`

---

2. Create a new user - `nats`

```bash
sudo useradd --system --no-create-home --shell /usr/sbin/nologin nats
```

3. Give permissions to file and folder for `nats` user
```bash

sudo chown -R nats:nats /usr/local/bin/nats-server
sudo chmod -R 750 /usr/local/bin/nats-server

sudo chown -R nats:nats /etc/nats/server.conf
sudo chmod -R 750 /etc/nats/server.conf

sudo chown -R nats:nats /tmp/nats-server.log
sudo chmod -R 750 /tmp/nats-server.log

sudo chown -R nats:nats /data/jetstream
sudo chmod -R 750 /data/jetstream

sudo chown -R nats:nats /etc/systemd/system/nats-server.service
sudo chmod -R 750 /etc/systemd/system/nats-server.service
```

2. Run the following commands to start and enable the service

```bash
sudo systemctl daemon-reload
sudo systemctl enable nats-server
sudo systemctl start nats-server
```

---

3. Check whether service is running or not

```bash
sudo systemctl status nats-server
sudo journalctl -u nats-server
```
---

4. Stop/restart/remove the service

```bash
sudo systemctl restart nats-server
sudo systemctl stop nats-server
sudo systemctl disable nats-server
```
---
