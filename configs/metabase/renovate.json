{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "packageRules": [{"matchPackageNames": ["eclipse-temurin"], "enabled": false}, {"matchPackageNames": ["metabase/metabase"], "matchUpdateTypes": ["minor", "patch"], "automerge": true, "ignoreTests": true}], "customManagers": [{"customType": "regex", "fileMatch": [".github/workflows/build.yml"], "datasourceTemplate": "github-releases", "depNameTemplate": "metabase/metabase", "matchStrings": ["METABASE_VERSION: (?<currentValue>v0\\.\\d{1,2}\\.\\d{1,2})"]}]}