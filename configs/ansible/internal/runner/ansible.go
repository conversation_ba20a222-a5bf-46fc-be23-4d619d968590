package runner

import (
	"context"
	"strconv"
	"strings"

	"github.com/apenella/go-ansible/v2/pkg/execute"
	"github.com/apenella/go-ansible/v2/pkg/execute/result/transformer"
	"github.com/apenella/go-ansible/v2/pkg/playbook"
	config "github.com/ravenmailio/ops/configs/ansible/configs"
)

type Runner struct {
	config         *config.Config
	defaultOptions *playbook.AnsiblePlaybookOptions
}

type PlaybookExecution struct {
	PlaybookPath string
	Inventory    string
	ExtraVars    map[string]interface{}
	Tags         []string
	Limit        string
	Environment  string
}

// New<PERSON><PERSON><PERSON> creates a new runner with defaults
func NewRunner(cfg *config.Config) *Runner {
	defaultOptions := &playbook.AnsiblePlaybookOptions{
		Inventory:     cfg.Ansible.InventoryPath + "/dev/hosts.yml",
		User:          cfg.Ansible.SSHUser,
		SSHCommonArgs: "-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null",
		Verbose:       cfg.Ansible.Verbose,
	}

	if cfg.Ansible.SSHKeyPath != "" {
		defaultOptions.PrivateKey = cfg.Ansible.SSHKeyPath
	}

	return &Runner{
		config:         cfg,
		defaultOptions: defaultOptions,
	}
}

// ExecutePlaybook runs an Ansible Playbook with specified parameters
func (r *Runner) ExecutePlaybook(ctx context.Context, exec PlaybookExecution) error {
	options := *r.defaultOptions

	if exec.Inventory != "" {
		options.Inventory = exec.Inventory
	}

	if len(exec.ExtraVars) > 0 {
		options.ExtraVars = exec.ExtraVars
	}

	if len(exec.Tags) > 0 {
		options.Tags = strings.Join(exec.Tags, ",")
	}

	execLimit, _ := strconv.Atoi(exec.Limit)
	if execLimit > 0 {
		options.Limit = exec.Limit
	}

	// playbook executor
	playbookCmd := &playbook.AnsiblePlaybookCmd{
		Playbooks:       append([]string{}, exec.PlaybookPath),
		PlaybookOptions: &options,
	}

	// setup execution context with proper output handling
	executor := execute.NewDefaultExecute(
		execute.WithCmd(playbookCmd),
		execute.WithTransformers(
			// helps to see whats happening in real time
			transformer.Prepend("ANSIBLE"),
		),
	)

	err := executor.Execute(ctx)
	if err != nil {
		return err
	}

	return nil
}
