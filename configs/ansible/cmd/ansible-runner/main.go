package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	config "github.com/ravenmailio/ops/configs/ansible/configs"
	"github.com/ravenmailio/ops/configs/ansible/internal/runner"
)

func main() {
	var (
		configPath = flag.String("config", "./configs/app.yaml", "Path to configuration file")
		playbook   = flag.String("playbook", "", "Playbook to execute")
		inventory  = flag.String("inventory", "", "Inventory to use")
		tags       = flag.String("tags", "", "Tags to run (comma-separated)")
		limit      = flag.String("limit", "", "Limit execution to specific hosts")
		extraVars  = flag.String("extra-vars", "", "Extra variables (JSON format)")
	)

	flag.Parse()

	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// create ansible runner
	ansibleRunner := runner.NewRunner(cfg)

	if *playbook == "" {
		fmt.Println("Usage examples:")
		fmt.Println("  # Deploy web application")
		fmt.Println("  ./ansible-runner -playbook applications/web-server.yml")
		fmt.Println("  # Run security updates on specific hosts")
		fmt.Println("  ./ansible-runner -playbook maintenance/updates.yml -limit web-servers")
		fmt.Println("  # Deploy to staging environment")
		fmt.Println("  ./ansible-runner -playbook applications/web-server.yml -inventory ./inventories/staging/hosts.yml")
		os.Exit(1)
	}

	execution := runner.PlaybookExecution{
		PlaybookPath: fmt.Sprintf("%s/%s", cfg.Ansible.PlaybookPath, *playbook),
		Inventory:    *inventory,
		Limit:        *limit,
	}

	if *tags != "" {
		execution.Tags = strings.Split(*tags, ",")
	}

	ctx := context.Background()
	if err := ansibleRunner.ExecutePlaybook(ctx, execution); err != nil {
		log.Fatalf("Failed to execute playbook: %v", err)
	}

	if *extraVars != "" {
		// TODO: Implement JSON parsing for extra vars
	}

	fmt.Println("Playbook executed successfully")
}
