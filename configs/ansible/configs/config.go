package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v2"
)

type Config struct {
	Ansible   AnsibleConfig   `yaml:"ansible"`
	AWS       AWSConfig       `yaml:"aws"`
	Inventory InventoryConfig `yaml:"inventory"`
}

type AnsibleConfig struct {
	PlaybookPath  string `yaml:"playbook_path"`
	InventoryPath string `yaml:"inventory_path"`
	SSHUser       string `yaml:"ssh_user"`
	SSHKeyPath    string `yaml:"ssh_key_path"`
	Verbose       bool   `yaml:"verbose"`
}

type AWSConfig struct {
	Region  string `yaml:"region"`
	Profile string `yaml:"profile"`
}

type InventoryConfig struct {
	RefreshInterval int `yaml:"refresh_internal"`
}

// LoadConfig reads configuration from a YAML file
func LoadConfig(configPath string) (*Config, error) {
	config := &Config{
		Ansible: AnsibleConfig{
			PlaybookPath:  "./playbooks",
			InventoryPath: "./inventories",
			SSHUser:       "ubuntu",
			Verbose:       true,
		},
		AWS: AWSConfig{
			Region: "us-east-1",
		},
		Inventory: InventoryConfig{
			RefreshInterval: 300,
		},
	}

	if _, err := os.Stat(configPath); err == nil {
		data, err := os.ReadFile(configPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}

		if err := yaml.Unmarshal(data, config); err != nil {
			return nil, fmt.Errorf("failed to parse config file: %w", err)
		}
	}

	return config, nil
}
