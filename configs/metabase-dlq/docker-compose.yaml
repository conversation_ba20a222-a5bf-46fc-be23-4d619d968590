version: "3.10"
services:
  metabase:
    build:
      context: ./
      dockerfile: Dockerfile
    container_name: metabase
    hostname: metabase
    volumes:
      - /dev/urandom:/dev/random:ro
      - ./nats-dlq.csv:/app/nats-dlq.csv
    ports:
      - 80:3000
    environment:
      MB_DB_TYPE: postgres
      MB_DB_DBNAME: metabaseappdb
      MB_DB_PORT: 5432
      MB_DB_USER: metabase
      MB_DB_PASS: metabasepassword
      MB_DB_HOST: postgres
    networks:
      - metanet1
    healthcheck:
      test: curl --fail -I http://localhost:80/api/health || exit 1
      interval: 15s
      timeout: 5s
      retries: 5
  postgres:
    image: postgres:latest
    container_name: postgres
    hostname: postgres
    environment:
      POSTGRES_USER: metabase
      POSTGRES_DB: metabaseappdb
      POSTGRES_PASSWORD: metabasepassword 
    volumes:
      - ./postgres_data:/var/lib/postgresql/data
    networks:
      - metanet1
networks:
  metanet1:
    driver: bridge
