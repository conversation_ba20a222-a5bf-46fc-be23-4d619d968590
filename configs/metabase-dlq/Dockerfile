ARG METABASE_VERSION

FROM metabase/metabase:v0.43.4 as metabase
FROM eclipse-temurin:11

COPY --from=metabase /app /app

RUN mkdir -p /app/plugins && cd /app/plugins && wget https://github.com/Markenson/csv-metabase-driver/releases/download/v1.3.1/csv.metabase-driver.jar && cd .. && useradd -ms /bin/sh metabase && chown -R metabase /app
WORKDIR /app
USER metabase

EXPOSE 3000
ENTRYPOINT ["/app/run_metabase.sh"]
