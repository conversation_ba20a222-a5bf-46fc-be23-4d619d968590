logger:
  level: error
  console: true
  log: /var/log/clickhouse-server/clickhouse-server.log
  errorlog: /var/log/clickhouse-server/clickhouse-server.err.log

# named_collections:
  # s3queue_conf:
  #   url: s3://dev.ravenclaw.org.11ac4b23-8fee-452f-9ff7-ab32f6030cee/detections/*/*.json
  #   use_environment_credentials: true
  #   mode: ordered

s3queue_log:
  database: system
  table: s3queue_log

s3:
  max_connections: 10
  connection_timeout: 10
  retry_attempts: 10
  stream_like_engine_allow_direct_select: 0

async_insert_threads: 1
background_buffer_flush_schedule_pool_size: 1
background_common_pool_size: 2
background_distributed_schedule_pool_size: 1
background_fetches_pool_size: 1
background_merges_mutations_concurrency_ratio: 1
background_message_broker_schedule_pool_size: 1
background_move_pool_size: 1
background_pool_size: 2
background_schedule_pool_size: 1
backup_threads: 1
merge_tree:
  number_of_free_entries_in_pool_to_execute_mutation: 1
  number_of_free_entries_in_pool_to_execute_optimize_entire_partition: 1
  number_of_free_entries_in_pool_to_lower_max_size_of_merge: 1

format_schema_inference: 0
format_schema_inference_cache_size: 10000
format_schema_inference_sample_rows: 1000

max_server_memory_usage: 3221225472  # ~3GB in bytes
max_server_memory_usage_to_ram_ratio: 0.7  # Use up to 40% of RAM
mark_cache_size: 5368709120  # ~5GB in bytes

max_concurrent_queries: 10
default_profile: default
default_database: analytics

path: /var/lib/clickhouse/
tmp_path: /var/lib/clickhouse/tmp/

users_config: users.xml

tcp_port: 9000
http_port: 8123
listen_host: 0.0.0.0

prometheus:
  endpoint: /metrics
  port: 9363

max_connections: 1024

merge_tree:
  parts_to_delay_insert: 1000
  parts_to_throw_insert: 3000

keeper_server: #keeper means zookeeper
  tcp_port: 2181
  server_id: 1
  log_storage_path: /var/lib/clickhouse/coordination/log
  snapshot_storage_path: /var/lib/clickhouse/coordination/snapshots

  coordination_settings:
    operation_timeout_ms: 10000
    session_timeout_ms: 30000
    raft_logs_level: trace

  raft_configuration:
    server:
      id: 1
      hostname: localhost
      port: 9234
