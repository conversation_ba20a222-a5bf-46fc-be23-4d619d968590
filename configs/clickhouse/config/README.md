# ClickHouse Configuration

This directory contains the core ClickHouse server and user configuration files that customize the database behavior, performance, and security settings.

## Directory Structure

```
config/
├── config.yml    # Main server configuration
├── users.yml     # User profiles, quotas, and access control
└── README.md     # This file
```

## Configuration Files

### `config.yml` - Server Configuration

Main ClickHouse server configuration that defines system behavior, resource limits, and integration settings.

#### Key Configuration Sections

#### **Logging Configuration**
```yaml
logger:
  level: error                    # Log level (error, warning, info, debug, trace)
  console: true                   # Enable console output
  log: /var/log/clickhouse-server/clickhouse-server.log
  errorlog: /var/log/clickhouse-server/clickhouse-server.err.log
```

#### **S3 Integration Settings**
```yaml
s3:
  max_connections: 10             # Maximum S3 connections
  connection_timeout: 10          # Connection timeout in seconds
  retry_attempts: 10              # Number of retry attempts
  stream_like_engine_allow_direct_select: 0  # Disable direct SELECT on streaming engines
```

#### **S3Queue Logging**
```yaml
s3queue_log:
  database: system                # Database for S3Queue logs
  table: s3queue_log             # Table name for logging
```

#### **Memory Management**
```yaml
max_server_memory_usage: 3221225472          # ~3GB total server memory limit
max_server_memory_usage_to_ram_ratio: 0.7   # Use up to 70% of available RAM
mark_cache_size: 5368709120                  # ~5GB mark cache size
```

#### **Performance & Threading**
```yaml
# Async processing
async_insert_threads: 1
background_buffer_flush_schedule_pool_size: 1
background_common_pool_size: 2
background_distributed_schedule_pool_size: 1
background_fetches_pool_size: 1
background_merges_mutations_concurrency_ratio: 1
background_message_broker_schedule_pool_size: 1
background_move_pool_size: 1
background_pool_size: 2
background_schedule_pool_size: 1

# Concurrent query limits
max_concurrent_queries: 10
```

#### **MergeTree Engine Settings**
```yaml
merge_tree:
  number_of_free_entries_in_pool_to_execute_mutation: 1
  number_of_free_entries_in_pool_to_execute_optimize_entire_partition: 1
  number_of_free_entries_in_pool_to_lower_max_size_of_merge: 1
  parts_to_delay_insert: 1000    # Delay inserts when parts exceed this
  parts_to_throw_insert: 3000    # Reject inserts when parts exceed this
```

#### **Network Configuration**
```yaml
tcp_port: 9000                   # Native ClickHouse protocol port
http_port: 8123                  # HTTP interface port
listen_host: 0.0.0.0            # Listen on all interfaces
max_connections: 1024            # Maximum client connections
```

#### **Prometheus Metrics**
```yaml
prometheus:
  endpoint: /metrics             # Metrics endpoint path
  port: 9363                    # Prometheus metrics port
```

#### **ClickHouse Keeper (ZooKeeper replacement)**
```yaml
keeper_server:
  tcp_port: 2181
  server_id: 1
  log_storage_path: /var/lib/clickhouse/coordination/log
  snapshot_storage_path: /var/lib/clickhouse/coordination/snapshots
  
  coordination_settings:
    operation_timeout_ms: 10000
    session_timeout_ms: 30000
    raft_logs_level: trace
  
  raft_configuration:
    server:
      id: 1
      hostname: localhost
      port: 9234
```

#### **Schema Inference**
```yaml
format_schema_inference: 0                    # Disable automatic schema inference
format_schema_inference_cache_size: 10000     # Schema cache size
format_schema_inference_sample_rows: 1000     # Rows to sample for inference
```

### `users.yml` - User Configuration

Defines user accounts, profiles, quotas, and access permissions.

#### **User Definition**
```yaml
users:
  admin:                         # Username
    profile: default             # Profile to use
    networks:
      ip: "::/0"                # Allow connections from any IP
    quota: default               # Quota to apply
    access_management: 1         # Enable SQL-based access management
```

#### **User Profiles**
The `default` profile defines resource limits and performance settings for users:

##### **Memory Limits**
```yaml
max_memory_usage: **********                    # ~2GB per query
max_memory_usage_for_user: **********           # ~2GB per user total
max_bytes_before_external_group_by: **********  # Use disk when GROUP BY exceeds 2GB
max_bytes_before_external_sort: **********      # Use disk when sorting exceeds 2GB
```

##### **Performance Settings**
```yaml
use_uncompressed_cache: 0                       # Disable uncompressed cache
queue_max_wait_ms: 1000                        # Maximum queue wait time
stream_like_engine_allow_direct_select: 0      # Disable direct SELECT on streaming engines
```

##### **Materialized View Settings**
```yaml
# Critical for S3Queue and streaming data processing
min_insert_block_size_rows_for_materialized_views: 1000      # Batch size: 1000 rows
min_insert_block_size_bytes_for_materialized_views: 100000000 # Batch size: 100MB
```

#### **Quotas**
```yaml
quotas:
  default:
    interval:
      duration: 3600             # 1-hour sliding window
      queries: 0                 # Unlimited queries (0 = no limit)
      errors: 0                  # Unlimited errors (0 = no limit)
```


### Log Monitoring

#### Check Server Logs
```bash
# View recent logs
docker exec clickhouse-server tail -f /var/log/clickhouse-server/clickhouse-server.log

# Check error logs
docker exec clickhouse-server tail -f /var/log/clickhouse-server/clickhouse-server.err.log
```

#### S3Queue Logs
```sql
-- Monitor S3Queue processing
SELECT * FROM system.s3queue_log ORDER BY event_time DESC LIMIT 20;
```

## Troubleshooting

### Common Configuration Issues

#### Memory Errors
```bash
# Check current memory usage
docker exec clickhouse-server clickhouse-client --query="
SELECT formatReadableSize(total_memory_tracker) as current_memory 
FROM system.asynchronous_metrics 
WHERE metric = 'MemoryTracking'"
```

#### Connection Issues
```bash
# Check active connections
docker exec clickhouse-server clickhouse-client --query="
SELECT count() as active_connections 
FROM system.processes"

# Test connection limits
docker exec clickhouse-server clickhouse-client --query="
SELECT getSetting('max_connections') as max_conn"
```

#### Performance Issues
```sql
-- Check slow queries
SELECT 
    query,
    query_duration_ms,
    memory_usage
FROM system.query_log 
WHERE query_duration_ms > 1000 
ORDER BY query_duration_ms DESC 
LIMIT 10;
```

### Configuration Validation

#### Verify Settings
```sql
-- Check active settings
SELECT name, value FROM system.settings WHERE name LIKE '%memory%';

-- Check server configuration
SELECT * FROM system.server_settings;
```

## Related Documentation

- [ClickHouse Configuration Files](https://clickhouse.com/docs/en/operations/configuration-files)
- [ClickHouse Settings](https://clickhouse.com/docs/en/operations/settings)
- [User Management](https://clickhouse.com/docs/en/operations/access-rights)
- [Performance Tuning](https://clickhouse.com/docs/en/guides/improving-query-performance)
