users:
  admin:
    profile: default
    networks:
      ip: "::/0"
    quota: default
    access_management: 1

profiles:
  default:
    # ~2GB per query
    max_memory_usage: 2000000000
    # ~2GB per user
    max_memory_usage_for_user: 2000000000
    # When GROUP BY operation exceeds 1GB, starts using disk
    max_bytes_before_external_group_by: 2000000000
    # When sorting exceeds 2GB, starts using disk
    max_bytes_before_external_sort: 2000000000
    # Prevents one query from using all vCPUs
    #max_threads: 1
    # Enables caching of uncompressed data in RAM
    # Improves read performance for repeated scans at the cost of some memory
    use_uncompressed_cache: 0
    # Queue settings for better performance
    queue_max_wait_ms:
      1000
      #background_pool_size: 2
    stream_like_engine_allow_direct_select: 0

    # important, means materialzed view will insert data on every 1000 row or 100 MB
    min_insert_block_size_rows_for_materialized_views: 1000
    min_insert_block_size_bytes_for_materialized_views:
      100000000

      #background_pool_size: 2
      #background_merges_mutations_concurrency_ratio: 1
      #number_of_free_entries_in_pool_to_execute_mutation: 1

quotas:
  default:
    interval:
      # Sets a sliding time window (3600s = 1 hour)
      duration: 3600
      # "0" means unlimited queries
      # If set to e.g. "1000", then after 1000 queries in one hour,
      # further queries would be rejected until the window resets
      queries: 0
      # "0" means unlimited errors
      # You could cap failed attempts (e.g. to 10) to prevent spamming
      # or runaway failing jobs
      errors: 0
