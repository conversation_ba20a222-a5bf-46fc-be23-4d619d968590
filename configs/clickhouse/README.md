# ClickHouse Setup & Usage Guide

This directory contains ClickHouse configuration and query files for analytics and data processing.

## Directory Structure

```
configs/clickhouse/
├── docker-compose.yml          # Docker Compose configuration
├── config/
│   ├── config.yml             # ClickHouse server configuration
│   └── users.yml              # User access configuration
├── nats-queries/              # SQL queries for NATS integration
├── s3-queries/                # SQL queries for S3 integration
└── README.md                  # This file
```

## Prerequisites

- Docker and Docker Compose installed
- AWS credentials configured (for S3 integration)

## Running ClickHouse

### 1. Start ClickHouse Server

```bash
# Start the ClickHouse server
docker-compose up -d

# Check if the service is running
docker-compose ps

# View logs
docker-compose logs -f clickhouse
```

### 2. Health Check

The service includes a health check that pings the HTTP interface:
```bash
# Manual health check
curl http://localhost:8123/ping
```

## Connecting to ClickHouse

### Using ClickHouse Client (CLI)

#### Option 1: Using Docker Exec
```bash
# Connect to the running container
docker exec -it clickhouse-server clickhouse-client

# Connect with specific database
docker exec -it clickhouse-server clickhouse-client --database=analytics
```

#### Option 2: Using Standalone Client
```bash
# Install ClickHouse client locally (if not using Docker)
clickhouse-client --host=localhost --port=9000 --user=admin --password=<strongpassword>

# Connect to specific database
clickhouse-client --host=localhost --port=9000 --user=admin --password=<strongpassword> --database=analytics
```

## Running Queries

### Interactive Mode
Once connected via CLI:
```sql
-- Show available databases
SHOW DATABASES;

-- Use analytics database
USE analytics;

-- Show tables
SHOW TABLES;

-- Describe a table structure
DESCRIBE TABLE your_table_name;

-- Run a sample query
SELECT count() FROM your_table_name;
```

## Available Query Sets

### NATS Queries (`nats-queries/`)
Contains SQL files for NATS message processing:
- **Create Tables**: `*-create-tables.sql` - Set up S3Queue tables for NATS data
- **Drop Tables**: `*-drop-tables.sql` - Clean up test tables
- **Show Tables**: `*-show-tables.sql` - Display created tables

Example environments:
- `01-dev-testing-without-id-headers-*`
- `02-dev-testing-with-id-headers-*`
- `03-prod-testing-without-id-headers-*`
- And more...

### S3 Queries (`s3-queries/`)
Contains SQL files for S3 data processing and analysis.

## Common Operations

### Create a New Table
```sql
CREATE TABLE my_table (
    id UInt64,
    name String,
    timestamp DateTime
) ENGINE = MergeTree()
ORDER BY id;
```

### Insert Data
```sql
INSERT INTO my_table VALUES (1, 'example', now());
```

### Query Data
```sql
SELECT * FROM my_table WHERE id = 1;
```

### Performance Monitoring
```sql
-- Check running queries
SELECT * FROM system.processes;

-- View query log
SELECT * FROM system.query_log ORDER BY event_time DESC LIMIT 10;

-- Check table sizes
SELECT 
    table,
    formatReadableSize(sum(bytes)) as size,
    sum(rows) as rows
FROM system.parts 
WHERE active 
GROUP BY table;
```

## Configuration

### Server Configuration
- **Port 8123**: HTTP interface
- **Port 9000**: Native ClickHouse protocol
- **Port 9363**: Prometheus metrics

### Environment Variables
- `CLICKHOUSE_DB=analytics` - Default database
- `CLICKHOUSE_USER=admin` - Default user
- `CLICKHOUSE_PASSWORD=<strongpassword>` - User password
- `AWS_DEFAULT_REGION=us-east-1` - AWS region for S3 access

### Data Persistence
- `./clickhouse-data` - Database files
- `./clickhouse-logs` - Server logs
- `./clickhouse-keeper-log` - Keeper logs
- `./clickhouse-keeper-snapshot` - Coordination snapshots

## Troubleshooting

### Check Container Status
```bash
docker-compose ps
docker-compose logs clickhouse
```

### View Metrics
```bash
# Prometheus metrics
curl http://localhost:9363/metrics
```

## Stopping ClickHouse

```bash
# Stop the service
docker-compose down

# Stop and remove volumes (WARNING: This will delete all data)
docker-compose down -v
```

## Security Notes

- Change the default password in `docker-compose.yml`
- Review user configurations in `config/users.yml`
- Consider network security for production deployments
- Monitor access logs and query patterns

## Additional Resources

- [ClickHouse Documentation](https://clickhouse.com/docs)
- [ClickHouse SQL Reference](https://clickhouse.com/docs/en/sql-reference)
- [Docker Hub - ClickHouse](https://hub.docker.com/r/clickhouse/clickhouse-server)
