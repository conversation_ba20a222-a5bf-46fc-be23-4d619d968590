# NATS Queries Configuration

This directory contains SQL queries for processing NATS (Neural Autonomic Transport System) message data using ClickHouse's S3Queue engine.

## Directory Structure

```
nats-queries/
├── 01-dev-testing-without-id-headers-create-tables.sql
├── 01-dev-testing-without-id-headers-drop-tables.sql
├── 01-dev-testing-without-id-headers-show-tables.sql
├── 02-dev-testing-with-id-headers-create-tables.sql
├── 02-dev-testing-with-id-headers-drop-tables.sql
├── 02-dev-testing-with-id-headers-show-tables.sql
├── 03-prod-testing-without-id-headers-create-tables.sql
├── 03-prod-testing-without-id-headers-drop-tables.sql
├── 03-prod-testing-without-id-headers-show-tables.sql
├── 04-prod-testing-with-id-headers-create-tables.sql
├── 04-prod-testing-with-id-headers-drop-tables.sql
├── 04-prod-testing-with-id-headers-show-tables.sql
├── 05-dev-testing-with-id-and-data-headers-create-tables.sql
├── 05-dev-testing-with-id-and-data-headers-drop-tables.sql
├── 05-dev-testing-with-id-and-data-headers-show-tables.sql
├── 06-prod-testing-with-id-data-headers-create-tables.sql
├── 06-prod-testing-with-id-data-headers-drop-tables.sql
├── 06-prod-testing-with-id-data-headers-show-tables.sql
└── README.md
```

## Purpose

This system processes NATS Dead Letter Queue (DLQ) messages stored in S3 buckets. It handles different message header configurations across development and production environments.

## Query Organization

The SQL files are organized by:

1. **Environment** - `dev` or `prod`
2. **Header Configuration** - Different levels of message metadata
3. **Operation Type** - `create-tables`, `drop-tables`, or `show-tables`

### Naming Convention

Format: `{sequence}-{environment}-testing-{header-type}-{operation}.sql`

- **Sequence**: 01-06 (execution order)
- **Environment**: `dev` or `prod`
- **Header Type**: 
  - `without-id-headers` - Basic NATS metadata only
  - `with-id-headers` - Includes Message ID and Organization ID
  - `with-id-and-data-headers` - Full metadata with additional data headers
- **Operation**: `create-tables`, `drop-tables`, `show-tables`

## Configuration Types

### 1. Without ID Headers (01, 03)
**Environment**: Development (01) / Production (03)
**S3 Path**: `s3://nats-s3-archival-bucket/{env}/EVENTS_DLQ/**`

**Headers Captured**:
- `X-Delivery-Count` - Number of delivery attempts
- `X-Error` - Error information
- `X-Original-Consumer` - Original message consumer
- `X-Original-Consumer-Sequence` - Consumer sequence number
- `X-Original-Stream` - Source stream name
- `X-Original-Stream-Sequence` - Stream sequence number
- `X-Original-Subject` - Message subject
- `X-Original-Timestamp` - Original message timestamp
- Standard NATS metadata (delivered, pending, sequences, subject, timestamp)

### 2. With ID Headers (02, 04)
**Environment**: Development (02) / Production (04)
**S3 Path**: `s3://nats-s3-archival-bucket/{env}/EVENTS_DLQ_WITH_ID/**`

**Additional Headers**:
- `X-Message-ID` - Unique message identifier
- `X-Organization-ID` - Organization identifier

### 3. With ID and Data Headers (05, 06)
**Environment**: Development (05) / Production (06)
**S3 Path**: `s3://nats-s3-archival-bucket/{env}/EVENTS_DLQ_WITH_ID_AND_DATA/**`

**Additional Headers**:
- All headers from previous configurations
- Additional data-specific headers for enhanced processing

## Table Architecture

Each configuration creates two main tables:

### 1. S3Queue Table
**Purpose**: Monitors S3 bucket for new DLQ message files
**Engine**: S3Queue
**Format**: JSONEachRow
**Settings**:
- `mode = 'ordered'` - Processes files in chronological order
- `s3queue_enable_logging_to_s3queue_log = 1` - Enables processing logs

### 2. Message Headers Table
**Purpose**: Stores structured NATS message metadata
**Engine**: MergeTree
**Ordering**: By timestamp (nats_timestamp_unix_nano)

## Usage Instructions

### Setting Up a Configuration

#### For Development Environment (Example: With ID Headers)
```bash
# 1. Create tables
docker exec -i clickhouse-server clickhouse-client --database=analytics < nats-queries/02-dev-testing-with-id-headers-create-tables.sql

# 2. Verify tables were created
docker exec -i clickhouse-server clickhouse-client --database=analytics < nats-queries/02-dev-testing-with-id-headers-show-tables.sql

# 3. Monitor data ingestion (after some time)
docker exec clickhouse-server clickhouse-client --database=analytics --query="SELECT count() FROM natsDLQMsgHeadersDevWithIDTable"
```

#### For Production Environment
```bash
# Use the corresponding prod files (03, 04, or 06)
docker exec -i clickhouse-server clickhouse-client --database=analytics < nats-queries/04-prod-testing-with-id-headers-create-tables.sql
```

### Switching Configurations

If you need to switch from one header configuration to another:

```bash
# 1. Drop current tables
docker exec -i clickhouse-server clickhouse-client --database=analytics < nats-queries/02-dev-testing-with-id-headers-drop-tables.sql

# 2. Create new configuration
docker exec -i clickhouse-server clickhouse-client --database=analytics < nats-queries/05-dev-testing-with-id-and-data-headers-create-tables.sql
```

## Monitoring and Analysis

### Check Data Ingestion
```sql
-- Count messages in each environment
SELECT 'Dev Without ID' as config, count() FROM natsDLQMsgHeadersDevWithoutIDTable
UNION ALL
SELECT 'Dev With ID' as config, count() FROM natsDLQMsgHeadersDevWithIDTable;

-- View recent messages
SELECT * FROM natsDLQMsgHeadersDevWithIDTable 
ORDER BY nats_timestamp_unix_nano DESC 
LIMIT 10;
```

### S3Queue Monitoring
```sql
-- Check S3Queue processing status
SELECT * FROM system.s3queue_log 
WHERE table LIKE '%natsDLQ%' 
ORDER BY event_time DESC 
LIMIT 20;

-- Processing statistics by table
SELECT 
    table,
    count() as total_operations,
    countIf(status = 'Processed') as successful,
    countIf(status = 'Failed') as failed
FROM system.s3queue_log 
WHERE table LIKE '%natsDLQ%'
GROUP BY table;
```

### Error Analysis
```sql
-- Analyze delivery failures
SELECT 
    `X-Error`,
    `X-Delivery-Count`,
    count() as occurrences
FROM natsDLQMsgHeadersDevWithIDTable
GROUP BY `X-Error`, `X-Delivery-Count`
ORDER BY occurrences DESC;

-- Check high delivery count messages
SELECT * FROM natsDLQMsgHeadersDevWithIDTable
WHERE toInt32(`X-Delivery-Count`) > 5
ORDER BY toInt32(`X-Delivery-Count`) DESC;
```

## S3 Bucket Structure

Expected S3 bucket organization:
```
s3://nats-s3-archival-bucket/
├── dev/
│   ├── EVENTS_DLQ/           # Without ID headers
│   ├── EVENTS_DLQ_WITH_ID/   # With ID headers
│   └── EVENTS_DLQ_WITH_ID_AND_DATA/  # With ID and data headers
└── prod/
    ├── EVENTS_DLQ/
    ├── EVENTS_DLQ_WITH_ID/
    └── EVENTS_DLQ_WITH_ID_AND_DATA/
```

## Choosing the Right Configuration

### Use Case Guidelines

1. **Without ID Headers (01, 03)**
   - Basic NATS DLQ monitoring
   - Single-tenant environments
   - Simple error tracking

2. **With ID Headers (02, 04)**
   - Multi-tenant environments
   - Need message traceability
   - Organization-specific analysis

3. **With ID and Data Headers (05, 06)**
   - Advanced analytics requirements
   - Detailed message processing
   - Enhanced debugging capabilities

## Troubleshooting

### Common Issues

1. **No Data Appearing**
   ```sql
   -- Check S3Queue status
   SELECT * FROM system.s3queue_log WHERE table LIKE '%natsDLQ%' ORDER BY event_time DESC LIMIT 5;
   
   -- Verify S3 bucket access
   SELECT * FROM system.errors WHERE name LIKE '%S3%';
   ```

2. **Permission Errors**
   - Verify AWS credentials have S3 bucket read access
   - Check bucket policy allows ClickHouse access

3. **Memory Issues**
   ```sql
   -- Check table sizes
   SELECT 
       table,
       formatReadableSize(sum(bytes)) as size,
       sum(rows) as rows
   FROM system.parts 
   WHERE table LIKE '%natsDLQ%' AND active
   GROUP BY table;
   ```

### Cleanup
```bash
# Remove all NATS tables (be careful!)
for drop_file in nats-queries/*-drop-tables.sql; do
    echo "Executing: $drop_file"
    docker exec -i clickhouse-server clickhouse-client --database=analytics < "$drop_file"
done
```

## Best Practices

1. **Start Small** - Begin with development environment and basic headers
2. **Monitor Resource Usage** - DLQ processing can be resource-intensive
3. **Regular Cleanup** - Implement TTL for old DLQ messages
4. **Environment Separation** - Keep dev and prod configurations separate
5. **Testing** - Always test configuration changes in development first

## Related Documentation

- [ClickHouse S3Queue Engine](https://clickhouse.com/docs/en/engines/table-engines/integrations/s3queue)
- [NATS Documentation](https://docs.nats.io/)
- [ClickHouse MergeTree Engine](https://clickhouse.com/docs/en/engines/table-engines/mergetree-family/mergetree)
