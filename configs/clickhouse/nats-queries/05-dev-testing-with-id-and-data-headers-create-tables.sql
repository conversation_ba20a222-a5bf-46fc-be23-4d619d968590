create table natsDLQQueueDevWithIDData (metadata String, data String) ENGINE = S3Queue (
    's3://nats-s3-archival-bucket/dev/EVENTS_DLQ_WITH_ID_AND_DATA/**',
    'JSONEachRow'
) SETTINGS mode = 'ordered',
s3queue_enable_logging_to_s3queue_log = 1;

CREATE TABLE natsDLQMsgHeadersDevWithIDDataTable (
    `X-Delivery-Count` String,
    `X-Error` String,
    `X-Message-ID` String,
    `X-Organization-ID` String,
    `X-Original-Consumer` String,
    `X-Original-Consumer-Sequence` String,
    `X-Original-Stream` String,
    `X-Original-Stream-Sequence` String,
    `X-Original-Subject` String,
    `X-Original-Timestamp` String,
    nats_num_delivered String,
    nats_num_pending String,
    nats_sequence_consumer String,
    nats_sequence_stream String,
    nats_subject String,
    nats_timestamp_unix_nano String,
    data String,
) ENGINE = MergeTree
ORDER BY
    (nats_timestamp_unix_nano);

CREATE MATERIALIZED VIEW natsDLQDevWithIdDataConsumer TO natsDLQMsgHeadersDevWithIDDataTable AS
SELECT
    JSONExtractString (metadata, 'X-Delivery-Count') AS `X-Delivery-Count`,
    JSONExtractString (metadata, 'X-Error') AS `X-Error`,
    JSONExtractString (metadata, 'X-Message-ID') AS `X-Message-ID`,
    JSONExtractString (metadata, 'X-Organization-ID') AS `X-Organization-ID`,
    JSONExtractString (metadata, 'X-Original-Consumer') AS `X-Original-Consumer`,
    JSONExtractString (metadata, 'X-Original-Consumer-Sequence') AS `X-Original-Consumer-Sequence`,
    JSONExtractString (metadata, 'X-Original-Stream') AS `X-Original-Stream`,
    JSONExtractString (metadata, 'X-Original-Stream-Sequence') AS `X-Original-Stream-Sequence`,
    JSONExtractString (metadata, 'X-Original-Subject') AS `X-Original-Subject`,
    JSONExtractString (metadata, 'X-Original-Timestamp') AS `X-Original-Timestamp`,
    JSONExtractString (metadata, 'nats_num_delivered') AS nats_num_delivered,
    JSONExtractString (metadata, 'nats_num_pending') AS nats_num_pending,
    JSONExtractString (metadata, 'nats_sequence_consumer') AS nats_sequence_consumer,
    JSONExtractString (metadata, 'nats_sequence_stream') AS nats_sequence_stream,
    JSONExtractString (metadata, 'nats_subject') AS nats_subject,
    JSONExtractString (metadata, 'nats_timestamp_unix_nano') AS nats_timestamp_unix_nano,
    data as data
FROM
    natsDLQQueueDevWithIDData;
