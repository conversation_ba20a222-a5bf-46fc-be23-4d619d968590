-- Create S3Queue table engine
CREATE TABLE `S3Prod-{ORG_ID}-QueueTable` (
    -- Raw JSON data
    data String
) ENGINE = S3Queue (
    's3://prod.ravenclaw.org.{ORG_ID}/detections/**',
    'JSONAsString'
) SETTINGS mode = 'ordered',
s3queue_loading_retries = 3,
s3queue_polling_min_timeout_ms = 1000,
s3queue_polling_max_timeout_ms = 5000,
s3queue_polling_backoff_ms = 1000,
s3queue_max_processed_files_before_commit = 100,
s3queue_max_processed_rows_before_commit = 1000,
s3queue_tracked_files_limit = 1000,
s3queue_tracked_file_ttl_sec = 3600;
