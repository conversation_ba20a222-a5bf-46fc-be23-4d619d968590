-- Create a table that stores rules as separate rows (this approach works reliably)
CREATE TABLE S3ProdDataAnalysisTable (
    -- Main fields
    reason String,
    verdict String,
    severity String,
    enhanced_reasoning String,
    -- Mail data
    mail_subject String,
    mail_sender_name String,
    mail_sender_email String,
    mail_sent_at DateTime64 (3),
    mail_origin_ip String,
    -- Analysis fields
    analysis_dmarc String,
    analysis_malicious_url UInt8,
    analysis_internal_domain UInt8,
    -- Detection fields
    detection_insider_threat UInt8,
    detection_malware UInt8,
    detection_bec UInt8,
    -- Rule information
    rule_name String,
    rule_success UInt8,
    rule_shadow UInt8,
    rule_mailbox String,
    orgId String,
    msgId String,
    scan_time DateTime64 (3),
    -- Processing timestamp
    processed_at DateTime DEFAULT now ()
) ENGINE = MergeTree ()
ORDER BY
    (mail_sent_at, rule_name);


-- Improved table with better data types and partitioning
CREATE TABLE S3ProdDataAnalysisTableNew (
reason String,
    verdict String,
    severity String,
    enhanced_reasoning String,
    -- Mail data
    mail_subject String,
    mail_sender_name String,
    mail_sender_email String,
    mail_sent_at DateTime64 (3),
    mail_origin_ip String,
    -- Analysis fields
    analysis_dmarc String,
    analysis_malicious_url UInt8,
    analysis_internal_domain UInt8,
    -- Detection fields
    detection_insider_threat UInt8,
    detection_malware UInt8,
    detection_bec UInt8,
    -- Rule information
    rule_name String,
    rule_success UInt8,
    rule_shadow UInt8,
    rule_mailbox String,
    orgId String,
    msgId String,
    scan_time DateTime64 (3),
    -- Processing timestamp
    processed_at DateTime DEFAULT now () 
) ENGINE = MergeTree()
PARTITION BY (toMonday(scan_time), orgId)
ORDER BY (orgId, scan_time, msgId, rule_name)
SETTINGS index_granularity = 4096;