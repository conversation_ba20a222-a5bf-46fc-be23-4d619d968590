-- Create a view that pivots rules into columns as you requested
CREATE VIEW S3ProdOrgAnalysisWithRulesViewTable AS
SELECT
    reason,
    verdict,
    severity,
    mail_subject,
    mail_sender_name,
    mail_sender_email,
    mail_sent_at,
    mail_origin_ip,
    analysis_dmarc,
    analysis_malicious_url,
    analysis_internal_domain,
    detection_insider_threat,
    detection_malware,
    detection_bec,
    -- S3 Path extracted fields
    orgId,
    msgId,
    scan_time,
    -- Rule columns - each rule gets its own column with success value
    -- Rule columns - each rule gets its own column with success value
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00000') AS RVNCLW_RUL_00000,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00001') AS RVNCLW_RUL_00001,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00002') AS RVNCLW_RUL_00002,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00003') AS RVNCLW_RUL_00003,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00004') AS RVNCLW_RUL_00004,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00005') AS RVNCLW_RUL_00005,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00006') AS RVNCLW_RUL_00006,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00007') AS RVNCLW_RUL_00007,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00008') AS RVNCLW_RUL_00008,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00009') AS RVNCLW_RUL_00009,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00010') AS RVNCLW_RUL_00010,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00011') AS RVNCLW_RUL_00011,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00012') AS RVNCLW_RUL_00012,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00013') AS RVNCLW_RUL_00013,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00014') AS RVNCLW_RUL_00014,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00015') AS RVNCLW_RUL_00015,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00016') AS RVNCLW_RUL_00016,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00017') AS RVNCLW_RUL_00017,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00018') AS RVNCLW_RUL_00018,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00019') AS RVNCLW_RUL_00019,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00020') AS RVNCLW_RUL_00020,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00021') AS RVNCLW_RUL_00021,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00022') AS RVNCLW_RUL_00022,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00024') AS RVNCLW_RUL_00024,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00026') AS RVNCLW_RUL_00026,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00027') AS RVNCLW_RUL_00027,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00028') AS RVNCLW_RUL_00028,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00029') AS RVNCLW_RUL_00029,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00030') AS RVNCLW_RUL_00030,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00031') AS RVNCLW_RUL_00031,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00032') AS RVNCLW_RUL_00032,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00037') AS RVNCLW_RUL_00037,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00038') AS RVNCLW_RUL_00038,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00039') AS RVNCLW_RUL_00039,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00040') AS RVNCLW_RUL_00040,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00041') AS RVNCLW_RUL_00041,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00042') AS RVNCLW_RUL_00042,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00043') AS RVNCLW_RUL_00043,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00044') AS RVNCLW_RUL_00044,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00045') AS RVNCLW_RUL_00045,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00046') AS RVNCLW_RUL_00046,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00047') AS RVNCLW_RUL_00047,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00048') AS RVNCLW_RUL_00048,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00049') AS RVNCLW_RUL_00049,
    maxIf (rule_success, rule_name = 'RVNCLW_RUL_00050') AS RVNCLW_RUL_00050
FROM
    S3ProdDataAnalysisTable
GROUP BY
    reason,
    verdict,
    severity,
    mail_subject,
    mail_sender_name,
    mail_sender_email,
    mail_sent_at,
    mail_origin_ip,
    analysis_dmarc,
    analysis_malicious_url,
    analysis_internal_domain,
    detection_insider_threat,
    detection_malware,
    detection_bec,
    orgId,
    msgId,
    scan_time;
