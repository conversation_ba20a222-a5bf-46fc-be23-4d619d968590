-- Create materialized view that works with string-based JSON extraction
CREATE MATERIALIZED VIEW `S3ProdOrg-{ORG_ID}-AnalysisMV` TO S3ProdDataAnalysisTable AS
SELECT
    -- Main fields extracted from the root JSON
    JSONExtractString (data, 'reason') AS reason,
    JSONExtractString (data, 'verdict') AS verdict,
    JSONExtractString (data, 'severity') AS severity,
    JSONExtractString (data, 'enhanced_reasoning') AS enhanced_reasoning,
    -- Mail data
    JSONExtractString (data, 'mail_data', 'subject') AS mail_subject,
    JSONExtractString (data, 'mail_data', 'sender', 'name') AS mail_sender_name,
    JSONExtractString (data, 'mail_data', 'sender', 'email') AS mail_sender_email,
    parseDateTimeBestEffort(JSONExtractString (data, 'mail_data', 'sent_at')) AS mail_sent_at,
    JSONExtractString (data, 'mail_data', 'origin_ip') AS mail_origin_ip,
    -- Analysis fields
    JSONExtractString (data, 'analysis', 'dmarc') AS analysis_dmarc,
    if (
        JSONExtractString (data, 'analysis', 'malicious_url') = 'true',
        1,
        0
    ) AS analysis_malicious_url,
    if (
        JSONExtractString (data, 'analysis', 'internal_domain') = 'true',
        1,
        0
    ) AS analysis_internal_domain,
    -- Detection fields  
    if (
        JSONExtractString (data, 'detections', 'insider_threat') = 'true',
        1,
        0
    ) AS detection_insider_threat,
    if (
        JSONExtractString (data, 'detections', 'malware') = 'true',
        1,
        0
    ) AS detection_malware,
    if (
        JSONExtractString (data, 'detections', 'bec') = 'true',
        1,
        0
    ) AS detection_bec,
    -- Rule fields extracted from each rule in the array
    JSONExtractString (rule_json, 'name') AS rule_name,
    if (
        JSONExtractString (rule_json, 'success') = 'true',
        1,
        0
    ) AS rule_success,
    if (
        JSONExtractString (rule_json, 'shadow') = 'true',
        1,
        0
    ) AS rule_shadow,
    JSONExtractString (rule_json, 'action', 'mailbox') AS rule_mailbox,
    -- S3 Path extracted fields
    -- Extract orgId from path pattern: s3://....org.<org-id>/detections/...
    -- extractAll(_path, '\\.org\\.([^/]+)/')[1] AS orgId,
    -- splitByString('/detections', _path)[1] as orgId,
    -- Extract msgId from path pattern: .../<msg-id>/<timestamp>.json
    '{ORG_ID}' as orgId,
    extractAll(_path, '/([^/]+)/[^/]+\\.json$')[1] AS msgId,
    -- Extract scan_time from filename: <timestamp>.json
    parseDateTimeBestEffort(
        extractAll(_path, '/([^/]+)\\.json$')[1]
    ) AS scan_time
FROM
    `S3Prod-{ORG_ID}-QueueTable` ARRAY
    JOIN JSONExtractArrayRaw (data, 'rules') AS rule_json
    WHERE extractAll(_path, '/([^/]+)\\.json$')[1] >= '{START-TIMESTAMP}';