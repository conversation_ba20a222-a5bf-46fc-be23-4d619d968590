# S3 Queries Configuration

This directory contains SQL query templates and tools for managing S3-based data processing in ClickHouse.

## Directory Structure

```
s3-queries/
├── query-template/            # SQL template files
│   ├── 01-queue.sql          # S3Queue table creation
│   ├── 02-table.sql          # Main data table
│   ├── 03-mv.sql             # Materialized view
│   ├── 04-rules-view.sql     # Rules processing view
│   ├── 05-select-queries.sql # Sample select queries
│   └── 06-drop-queries.sql   # Cleanup queries
├── src/
│   └── main.py               # Template rendering script
└── README.md                 # This file
```

## Purpose

This system processes security detection data from S3 buckets using ClickHouse's S3Queue engine. It's designed to:

1. **Ingest data** from S3 buckets organized by organization ID
2. **Process JSON detection data** into structured tables
3. **Apply rules and transformations** via materialized views
4. **Support multi-tenant** architecture with per-organization isolation

## Template System

### SQL Templates

The query templates use placeholder variables that get replaced when generating organization-specific queries:

- `{ORG_ID}` - Organization identifier
- `{START-TIMESTAMP}` - Unix timestamp for data processing start time

### Template Files (Execution Order)

#### 1. `01-queue.sql` - S3Queue Table
Creates the S3Queue table that monitors S3 buckets for new files:
```sql
CREATE TABLE `S3Prod-{ORG_ID}-QueueTable` (
    data String
) ENGINE = S3Queue (
    's3://prod.ravenclaw.org.{ORG_ID}/detections/**',
    'JSONAsString'
)
```

**Key Settings:**
- `mode = 'ordered'` - Processes files in order
- `s3queue_loading_retries = 3` - Retry failed files
- `s3queue_polling_min_timeout_ms = 1000` - Minimum polling interval
- `s3queue_tracked_files_limit = 1000` - Maximum tracked files

#### 2. `02-table.sql` - Main Data Table
Creates the primary table for storing processed detection data with optimized schema for analytics.

#### 3. `03-mv.sql` - Materialized View
Creates a materialized view that automatically processes data from the queue table into the main table.

#### 4. `04-rules-view.sql` - Rules Processing View
Creates views for applying business logic and rules to the detection data.

#### 5. `05-select-queries.sql` - Sample Queries
Contains example SELECT queries for data analysis.

#### 6. `06-drop-queries.sql` - Cleanup
Contains DROP statements for removing all created objects.

## Using the Template Generator

### Prerequisites
- Python 3.x
- Access to ClickHouse server

### Basic Usage

```bash
# Navigate to the s3-queries directory
cd /path/to/configs/clickhouse/s3-queries
```

# Generate queries for a specific organization
- Add org_id to org.txt

# Example
```
ABCDEFG, 2024-06-01 00:00:00
```

### Example

```bash
# Generate queries for organization "acme-corp" starting from 2024-06-01
python src/main.py
```

This creates:
```
generated/
└── acme-corp/
    ├── 01-queue.sql
    ├── 02-table.sql
    ├── 03-mv.sql
    ├── 04-rules-view.sql
    ├── 05-select-queries.sql
    └── 06-drop-queries.sql
```

## Executing Generated Queries

### Sequential Execution
Execute the generated SQL files in order

# For organization "acme-corp"
ORG_ID="abc....def"

## S3 Bucket Structure

The system expects S3 buckets organized as:
```
s3://prod.ravenclaw.org.{ORG_ID}/
└── detections/
    ├── 2024/01/01/file1.json
    ├── 2024/01/01/file2.json
    └── ...
```

## Monitoring

### Check S3Queue Status
```sql
-- View S3Queue processing status
SELECT * FROM system.s3queue_log 
WHERE table = 'S3Prod-{ORG_ID}-QueueTable' 
ORDER BY event_time DESC LIMIT 10;

-- Check processed files
SELECT count() as files_processed 
FROM system.s3queue_log 
WHERE table = 'S3Prod-{ORG_ID}-QueueTable' 
AND status = 'Processed';
```

### Performance Monitoring
```sql
-- Check table sizes
SELECT 
    table,
    formatReadableSize(sum(bytes)) as size,
    sum(rows) as rows
FROM system.parts 
WHERE database = 'analytics' 
AND table LIKE '%{ORG_ID}%'
AND active 
GROUP BY table;
```

## Related Documentation

- [ClickHouse S3Queue Engine](https://clickhouse.com/docs/en/engines/table-engines/integrations/s3queue)
- [ClickHouse Materialized Views](https://clickhouse.com/docs/en/guides/developer/cascading-materialized-views)
- [S3 Integration Best Practices](https://clickhouse.com/docs/en/integrations/s3)
