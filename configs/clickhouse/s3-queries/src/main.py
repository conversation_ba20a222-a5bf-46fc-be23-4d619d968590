import argparse
import glob
import os
from pathlib import Path
from datetime import datetime

def convert_to_timestamp(date_str):
    """Convert a date string in format 'yyyy-mm-dd hh:mm:ss' to Unix timestamp in seconds."""
    try:
        dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        return str(int(dt.timestamp()))
    except ValueError as e:
        raise ValueError(f"Invalid date format. Expected 'yyyy-mm-dd hh:mm:ss', got: {date_str}") from e

def render_templates(orgid: str, timestamp: str, template_dir: str, output_base_dir:str):
    out_dir = Path(output_base_dir) / orgid
    if out_dir.exists():
        print(f"⏭️  Skipping {orgid} - folder already exists in {output_base_dir}")
        return
        
    # Create the directory if it doesn't exist
    out_dir.mkdir(exist_ok=True, parents=True)
    pattern = os.path.join(template_dir, "*sql")
    
    for tpl_path in sorted(glob.glob(pattern)):
        tpl = Path(tpl_path)
        content = tpl.read_text(encoding="utf-8")
        
        rendered = (
            content.replace("{ORG_ID}", orgid).replace("{START-TIMESTAMP}", timestamp)
        )
        
        dest = out_dir/tpl.name
        dest.write_text(rendered, encoding="utf-8")
        print(f"✔️  Rendered {dest}")

def main():
    template_dir = "../query-template"
    output_base_dir = "../prod-sql"
    org_list_path = "org.txt"
    
 # Read the org.txt file
    with open(org_list_path, 'r') as file:
        for line in file:
            line = line.strip()
            if not line:  # Skip empty lines
                continue
            
            try:
                # Parse comma-separated values
                parts = line.split(',', 1)
                if len(parts) != 2:
                    print(f"Warning: Invalid line format - {line}")
                    continue
                
                orgid = parts[0].strip()
                timestamp = convert_to_timestamp(parts[1].strip())
                
                # Process this organization
                render_templates(orgid, timestamp, template_dir, output_base_dir)
            except Exception as e:
                print(f"Error processing line '{line}': {e}")

if __name__ == "__main__":
    main()
