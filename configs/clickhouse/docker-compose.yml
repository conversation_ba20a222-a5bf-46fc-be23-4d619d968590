version: "3.8"

services:
  clickhouse:
    image: clickhouse/clickhouse-server:25.4
    container_name: clickhouse-server
    restart: unless-stopped
    ports:
      - "8123:8123" # HTTP interface
      - "9000:9000" # Native ClickHouse interface
      - "9363:9363" # Prometheus       → expose /metrics for alerts
    volumes:
      - ./clickhouse-data:/var/lib/clickhouse
      - ./clickhouse-logs:/var/log/clickhouse-server
      - ./config/config.yaml:/etc/clickhouse-server/config.d/config.yaml
      - ./config/users.yaml:/etc/clickhouse-server/users.d/users.yaml
      - ./clickhouse-keeper-log:/var/log/clickhouse-keeper
      - ./clickhouse-keeper-snapshot:/var/lib/clickhouse/coordination/snapshots

    environment:
      - CLICKHOUSE_DB=analytics # First database you will query
      - CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT=1 # Enable SQL-based RBAC
      - CLICKHOUSE_USER=admin
      - CLICKHOUSE_PASSWORD=<strongpassword>
      - AWS_DEFAULT_REGION=us-east-1
    ulimits: # Raise open-file ceiling (important for even modest throughput)
      nofile:
        # <PERSON><PERSON><PERSON><PERSON> opens a FD per part/connection; default 1024 is too low even for 1 GB/day.
        soft: 262144
        hard: 262144
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "localhost:8123/ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 4
    logging: # Rotate JSON-file logs to keep the node disk tidy
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
    networks:
      - metanet1

networks:
  metanet1:
    driver: bridge

volumes:
  clickhouse-data:
    driver: local
