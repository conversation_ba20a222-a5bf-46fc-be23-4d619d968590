import csv
import socket
import dns.resolver
import requests
import concurrent.futures
from time import sleep
import os
import dotenv
from dotenv import load_dotenv

load_dotenv()
INPUT_CSV_PATH = "./logs-insights-results.csv"
OUTPUT_CSV_PATH = "./network_traffic_spam_check.csv"

# Popular spam blacklists (DNSBLs)
SPAM_BLACKLISTS = [
    "zen.spamhaus.org",
    "spam.abuse.ch",
    "cbl.abuseat.org",
    "dnsbl.sorbs.net",
    "bl.spamcop.net",
    "dnsbl.justspam.org",
    "ubl.unsubscore.com",
    "psbl.surriel.com",
    "combined.abuse.ch",
    "dnsbl-1.uceprotect.net",
    "dnsbl-2.uceprotect.net",
    "dnsbl-3.uceprotect.net",
    "dul.ru",
    "bogons.cymru.com",
    "osrs.dnsbl.net.au",
    "owfs.dnsbl.net.au",
    "pbl.spamhaus.org",
    "phishing.rbl.msrbl.net",
    "proxy.bl.gweep.ca",
    "rbl.interserver.net",
    "ricn.dnsbl.net.au",
    "rmst.dnsbl.net.au",
    "spamlist.or.kr",
    "t3direct.dnsbl.net.au",
    "ubl.lashback.com",
    "virus.rbl.jp",
    "wormrbl.imp.ch",
    "xbl.spamhaus.org",
]


def reverse_ip(ip):
    """Reverse IP address for DNSBL lookup"""
    return ".".join(reversed(ip.split(".")))


def check_ip_in_dnsbl(ip, blacklist):
    """Check if IP is listed in a specific DNSBL"""
    try:
        reversed_ip = reverse_ip(ip)
        query = f"{reversed_ip}.{blacklist}"
        dns.resolver.resolve(query, "A")
        return True  # IP is listed
    except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer, Exception):
        return False  # IP is not listed


def check_ip_spam_status(ip, max_workers=10):
    """Check IP against multiple blacklists concurrently"""
    blacklisted_on = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_bl = {
            executor.submit(check_ip_in_dnsbl, ip, bl): bl for bl in SPAM_BLACKLISTS
        }

        for future in concurrent.futures.as_completed(future_to_bl):
            bl = future_to_bl[future]
            try:
                if future.result():
                    blacklisted_on.append(bl)
            except Exception:
                pass

    return blacklisted_on


def check_abuseipdb(ip, api_key):
    """Check IP reputation using AbuseIPDB API (requires free API key)"""
    if not api_key:
        return None

    url = "https://api.abuseipdb.com/api/v2/check"
    headers = {
        "Accept": "application/json",
        "Key": os.environ.get("ABUSEIPDB_API_KEY"),
    }
    params = {"ipAddress": ip, "maxAgeInDays": "90", "verbose": ""}

    try:
        response = requests.get(url, headers=headers, params=params, timeout=5)
        if response.status_code == 200:
            data = response.json()["data"]
            return {
                "abuse_confidence_score": data["abuseConfidenceScore"],
                "usage_type": data["usageType"],
                "isp": data["isp"],
                "country": data["countryCode"],
                "total_reports": data["totalReports"],
            }
    except Exception:
        pass
    return None


def process_csv_spam_check(input_file, output_file=None, abuseipdb_key=None):
    """Process CSV and check each source IP for spam status"""
    results = []

    try:
        with open(input_file, "r") as csvfile:
            reader = csv.DictReader(csvfile)

            if "srcaddr" not in reader.fieldnames:
                print("Error: 'srcaddr' column not found in CSV")
                return

            print(f"Checking IPs from {input_file} against spam blacklists...\n")

            for i, row in enumerate(reader):
                src_ip = row["srcaddr"]
                print(f"Checking {src_ip}... ", end="", flush=True)

                # Check DNSBLs
                blacklists = check_ip_spam_status(src_ip)
                is_spam = len(blacklists) > 0

                # Add spam check results
                row["is_spam"] = "Yes" if is_spam else "No"
                row["blacklist_count"] = len(blacklists)
                row["blacklisted_on"] = ", ".join(blacklists[:5]) + (
                    "..." if len(blacklists) > 5 else ""
                )

                # Optional: Check AbuseIPDB if API key provided
                if abuseipdb_key:
                    abuse_data = check_abuseipdb(src_ip, abuseipdb_key)
                    if abuse_data:
                        row["abuse_score"] = abuse_data["abuse_confidence_score"]
                        row["isp"] = abuse_data["isp"]
                        row["country"] = abuse_data["country"]

                results.append(row)

                # Print result
                if is_spam:
                    print(f"SPAM (found on {len(blacklists)} blacklists)")
                else:
                    print("CLEAN")

                # Rate limiting to avoid overwhelming DNS servers
                if (i + 1) % 10 == 0:
                    sleep(1)

        # Write results
        if output_file and results:
            with open(output_file, "w", newline="") as csvfile:
                fieldnames = list(results[0].keys())
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(results)
                print(f"\nResults saved to {output_file}")

        return results

    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found")
    except Exception as e:
        print(f"Error processing file: {str(e)}")


def main():
    # Configuration
    input_csv = INPUT_CSV_PATH  # Your input file
    output_csv = OUTPUT_CSV_PATH  # Output file

    # Optional: Get free API key from https://www.abuseipdb.com/api
    abuseipdb_api_key = None  # Replace with your API key for enhanced checks

    # Process the CSV
    results = process_csv_spam_check(input_csv, output_csv, abuseipdb_api_key)

    # Print summary
    if results:
        total = len(results)
        spam_count = sum(1 for r in results if r["is_spam"] == "Yes")
        print(f"\n--- Summary ---")
        print(f"Total IPs checked: {total}")
        print(f"Spam IPs found: {spam_count} ({spam_count / total * 100}%)")
        print(
            f"Clean IPs: {total - spam_count} ({(total - spam_count) / total * 100}%)"
        )


if __name__ == "__main__":
    main()