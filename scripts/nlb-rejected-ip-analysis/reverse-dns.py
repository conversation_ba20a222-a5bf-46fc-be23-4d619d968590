import csv
import socket
import sys

INPUT_CSV_PATH = "./logs-insights-results.csv"
OUTPUT_CSV_PATH = "./network_traffic_spam_check.csv"

def reverse_dns_lookup(ip_address):
    """
    Perform reverse DNS lookup for an IP address.
    Returns the hostname if found, otherwise returns the original IP.
    """
    try:
        hostname, _, _ = socket.gethostbyaddr(ip_address)
        return hostname
    except (socket.herror, socket.gaierror, socket.timeout):
        # Return original IP if reverse lookup fails
        return f"{ip_address} (no PTR record)"
    except Exception as e:
        return f"{ip_address} (error: {str(e)})"


def process_csv_with_rdns(input_file, output_file=None):
    """
    Read CSV file and perform reverse DNS lookups on srcaddr column.
    """
    results = []

    try:
        with open(input_file, "r") as csvfile:
            reader = csv.DictReader(csvfile)

            # Check if srcaddr column exists
            if "srcaddr" not in reader.fieldnames:
                print("Error: 'srcaddr' column not found in CSV")
                return

            print(f"Processing {input_file}...\n")

            for row in reader:
                src_ip = row["srcaddr"]
                hostname = reverse_dns_lookup(src_ip)

                # Add hostname to the row
                row["src_hostname"] = hostname
                results.append(row)

                # Print progress
                print(f"{src_ip} -> {hostname}")

        # Write results to output file if specified
        if output_file:
            with open(output_file, "w", newline="") as csvfile:
                if results:
                    fieldnames = list(results[0].keys())
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(results)
                    print(f"\nResults saved to {output_file}")

        return results

    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found")
    except Exception as e:
        print(f"Error processing file: {str(e)}")


def main():
    # You can modify these filenames as needed
    input_csv = INPUT_CSV_PATH  # Change this to your input file name
    output_csv = OUTPUT_CSV_PATH  # Optional output file

    # If you want to use command line arguments
    if len(sys.argv) > 1:
        input_csv = sys.argv[1]
        output_csv = sys.argv[2] if len(sys.argv) > 2 else None

    # Process the CSV
    results = process_csv_with_rdns(input_csv, output_csv)

    # Print summary
    if results:
        print(f"\nProcessed {len(results)} records")

        # Count successful lookups
        successful_lookups = sum(
            1
            for r in results
            if not r["src_hostname"].endswith("(no PTR record)")
            and not "(error:" in r["src_hostname"]
        )
        print(f"Successful reverse DNS lookups: {successful_lookups}")


if __name__ == "__main__":
    main()