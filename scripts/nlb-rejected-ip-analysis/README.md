# NLB Rejected IP Analysis

This directory contains scripts for analyzing rejected IP addresses from Network Load Balancer (NLB) traffic logs. The tools help identify spam sources and perform reverse DNS lookups on rejected connections.

## Overview

The scripts in this directory are designed to analyze VPC Flow Logs to identify and investigate IP addresses that are being rejected by the NLB, particularly for email-related ports (25, 587). This is useful for:

- Identifying spam sources attempting to connect to mail servers
- Performing reverse DNS lookups to identify the origin of rejected connections
- Checking IP addresses against known spam blacklists
- Generating reports for security analysis

## Prerequisites

- Python 3.13+
- `uv` package manager
- Access to AWS CloudWatch Logs Insights
- (Optional) AbuseIPDB API key for enhanced spam detection

## Setup

1. Initialize the project and install dependencies:
```bash
uv init .
uv venv
uv install
uv sync
```

## Getting Rejected IPs from CloudWatch

To extract rejected IP addresses from VPC Flow Logs:

1. Go to CloudWatch Logs Insights in the AWS Console
2. Select the `vpc-flow-logs` log group
3. Use this query to find rejected connections on email ports:

```sql
fields srcaddr, dstaddr, action, srcport, dstport
| filter `interface-id` in ["eni-0bef6d68f5eb4abb5", "eni-06ce650eb63e2f7bf", "eni-0f3d8d304314ad195"]
| filter `flow-direction` = "ingress" and action = "REJECT" and dstport in [25, 587]
| sort @timestamp asc
| dedup srcaddr
```

4. Export the results as CSV and save as `logs-insights-results.csv` in this directory

## Scripts

### 1. reverse-dns.py

Performs reverse DNS lookups on source IP addresses to identify their hostnames.

**Features:**
- Reads CSV file with `srcaddr` column
- Performs reverse DNS lookups for each IP
- Adds `src_hostname` column to output
- Handles lookup failures gracefully

**Usage:**
```bash
uv run reverse-dns.py
```

**Default files:**
- Input: `./logs-insights-results.csv`
- Output: `./network_traffic_spam_check.csv`


### 2. ip-spam-analysis.py

Comprehensive spam analysis tool that checks IP addresses against multiple blacklists and optionally uses AbuseIPDB API.

**Features:**
- Checks IPs against 25+ popular DNS blacklists (DNSBLs)
- Concurrent processing for faster analysis
- Optional AbuseIPDB integration for enhanced reputation data
- Rate limiting to avoid overwhelming DNS servers
- Detailed reporting with spam confidence scores

**Blacklists checked:**
- Spamhaus (zen.spamhaus.org, pbl.spamhaus.org, xbl.spamhaus.org)
- SpamCop (bl.spamcop.net)
- SURBL (abuse.ch lists)
- UCEProtect networks
- And many more...

**Usage:**
```bash
uv run ip-spam-analysis.py
```

**Optional AbuseIPDB Integration:**
1. Get a free API key from [AbuseIPDB](https://www.abuseipdb.com/api)
2. Create a `.env` file:
```bash
ABUSEIPDB_API_KEY=your_api_key_here
```
3. The script will automatically use the API key if available

**Output columns:**
- All original columns from input CSV
- `is_spam`: Yes/No indicator
- `blacklist_count`: Number of blacklists the IP appears on
- `blacklisted_on`: Names of blacklists (up to 5 shown)

---

## Troubleshooting

### Common Issues

1. **"srcaddr column not found":** Ensure your CSV has the correct column name
2. **DNS timeouts:** Some IPs may not respond to reverse DNS queries
3. **Rate limiting:** If you see many failures, the script may be running too fast

### Performance Tips

- For large datasets, consider processing in batches
- Monitor network connectivity during analysis
- Some blacklists may be temporarily unavailable

## Files

- `reverse-dns.py` - Reverse DNS lookup tool
- `ip-spam-analysis.py` - Comprehensive spam analysis tool
- `pyproject.toml` - Python project configuration
- `logs-insights-results.csv` - Input file (CloudWatch export)
- `network_traffic_spam_check.csv` - Output file with analysis results
- `.env` - Environment variables (optional, for AbuseIPDB API key)
