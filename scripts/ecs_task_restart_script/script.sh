#!/bin/bash

# Path to your virtual environment
VENV_PATH="./venv"

# Path to your Python script
PYTHON_SCRIPT_PATH="./main.py"

# Activate the virtual environment
source "$VENV_PATH/bin/activate"

# Check if the virtual environment was activated successfully
if [ $? -eq 0 ]; then
    echo "Virtual environment activated successfully."
else
    echo "Failed to activate virtual environment. Exiting."
    exit 1
fi

# Run the Python script
python3 "$PYTHON_SCRIPT_PATH"

# Check if the Python script ran successfully
if [ $? -eq 0 ]; then
    echo "Python script executed successfully."
else
    echo "Python script failed. Check the logs for errors."
    exit 1
fi

# Deactivate the virtual environment
deactivate
echo "Virtual environment deactivated."
