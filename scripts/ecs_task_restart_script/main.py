import boto3

def force_deploy_ecs_service(cluster_name, service_name, region_name):
    """
    Force a new deployment of an ECS service.

    :param cluster_name: Name of the ECS cluster
    :param service_name: Name of the ECS service
    :param region_name: AWS region where the ECS service is located
    """
    # Create a boto3 client for ECS
    ecs_client = boto3.client('ecs', region_name=region_name)

    try:
        # Force a new deployment
        response = ecs_client.update_service(
            cluster=cluster_name,
            service=service_name,
            forceNewDeployment=True
        )
        print(f"Service '{service_name}' in cluster '{cluster_name}' is being redeployed.")
        print(f"New deployment ID: {response['service']['deployments'][0]['id']}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # Replace these values with your ECS cluster name, service name, and region
    CLUSTER_NAME = 'ecs-ravenclaw-cluster'
    SERVICE_NAME = 'gateway-service'
    REGION_NAME = 'ap-south-1'  # e.g., 'us-east-1'

    # Force deploy the ECS service
    force_deploy_ecs_service(CLUSTER_NAME, SERVICE_NAME, REGION_NAME)
