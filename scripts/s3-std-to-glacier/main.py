#!/usr/bin/env python3
"""
S3 to Glacier Migration Script
Migrates files from S3 Standard to Glacier storage with async and parallel processing.
"""

import argparse
import asyncio
import gzip
import tarfile
import tempfile
import os
import sys
import logging
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
from io import BytesIO
import aioboto3
import botocore
from botocore.exceptions import ClientError
import json
import threading

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Constants
MAX_CHUNK_SIZE = 1024 * 1024  # 1 GB
BATCH_SIZE = 1000  # Fetch 1000 objects at a time
GLACIER_PREFIX = "glacier"
CHUNK_EXTENSION = ".gz"
MAX_CONCURRENT_UPLOADS = 5  # Maximum parallel uploads
MAX_CONCURRENT_DOWNLOADS = 10  # Maximum parallel downloads


@dataclass
class FileInfo:
    """Information about a file in S3."""

    key: str
    size: int
    last_modified: datetime


@dataclass
class ProgressData:
    files_processed: int = 0
    files_total: int = 0
    errors_encountered: int = 0
    last_successful_chunk: str = ""
    last_chunk_date: str = ""
    active_threads: int = 0
    chunks_completed: int = 0
    chunks_failed: int = 0
    start_time: str = ""
    last_update: str = ""


class ProgressTracker:
    def __init__(self, result_file: str = "result.txt"):
        self.result_file = result_file
        self.progress = ProgressData()
        self.lock = threading.Lock()
        self.progress.start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self._write_progress()

    def update_total_files(self, total: int):
        with self.lock:
            self.progress.files_total = total
            self._write_progress()

    def increment_processed(self, count: int = 1):
        with self.lock:
            self.progress.files_processed += count
            self._write_progress()

    def increment_errors(self, count: int = 1):
        with self.lock:
            self.progress.errors_encountered += count
            self._write_progress()

    def set_last_chunk(self, chunk_name: str, chunk_date: str):
        with self.lock:
            self.progress.last_successful_chunk = chunk_name
            # Convert YYYY-MM format to YYYY-MM-DD format (using 01 as default day)
            if len(chunk_date) == 7 and "-" in chunk_date:  # Format: YYYY-MM
                self.progress.last_chunk_date = f"{chunk_date}-01"
            else:
                self.progress.last_chunk_date = chunk_date
            self.progress.chunks_completed += 1
            self._write_progress()

    def increment_failed_chunks(self, count: int = 1):
        with self.lock:
            self.progress.chunks_failed += count
            self._write_progress()

    def set_active_threads(self, count: int):
        with self.lock:
            self.progress.active_threads = count
            self._write_progress()

    def _write_progress(self):
        self.progress.last_update = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        try:
            with open(self.result_file, "w") as f:
                f.write("=" * 50 + "\n")
                f.write("S3 TO GLACIER MIGRATION PROGRESS\n")
                f.write("=" * 50 + "\n")
                f.write(f"Started: {self.progress.start_time}\n")
                f.write(f"Last Updated: {self.progress.last_update}\n")
                f.write("-" * 50 + "\n")
                f.write(
                    f"Files Processed: {self.progress.files_processed}/{
                        self.progress.files_total
                    }\n"
                )
                if self.progress.files_total > 0:
                    progress_pct = (
                        self.progress.files_processed / self.progress.files_total
                    ) * 100
                    f.write(f"Progress: {progress_pct:.1f}%\n")
                f.write(f"Errors Encountered: {self.progress.errors_encountered}\n")
                f.write(f"Chunks Completed: {self.progress.chunks_completed}\n")
                f.write(f"Chunks Failed: {self.progress.chunks_failed}\n")
                f.write(f"Active Threads/Processes: {self.progress.active_threads}\n")
                f.write("-" * 50 + "\n")
                if self.progress.last_successful_chunk:
                    f.write(
                        f"Last Successful Chunk: {
                            self.progress.last_successful_chunk
                        }\n"
                    )
                    f.write(f"Last Chunk Date: {self.progress.last_chunk_date}\n")
                else:
                    f.write("Last Successful Chunk: None\n")
                f.write("=" * 50 + "\n")
        except Exception as e:
            logger.error(f"Failed to write progress: {e}")


class S3ToGlacierMigrator:
    """Async S3 to Glacier migrator with parallel processing."""

    def __init__(
        self,
        bucket_name: str,
        max_date: datetime,
        max_workers: int = 10,
        local_chunks_dir: str = "./localchunks",
        save_local_chunks: bool = False,
        delete_objects: bool = True,
    ):
        self.bucket_name = bucket_name
        self.max_date = max_date
        self.max_workers = max_workers
        self.local_chunks_dir = local_chunks_dir
        self.save_local_chunks = save_local_chunks
        self.delete_objects = delete_objects
        self.session = aioboto3.Session()
        self.upload_semaphore = asyncio.Semaphore(MAX_CONCURRENT_UPLOADS)
        self.download_semaphore = asyncio.Semaphore(MAX_CONCURRENT_DOWNLOADS)
        self.progress_tracker = ProgressTracker()
        self.chunk_counters = {}  # Track chunk numbers per year-month
        self.startup_timestamp = datetime.now().strftime(
            "%Y%m%d_%H%M%S"
        )  # Timestamp for glacier path

        # Create local chunks directory if it doesn't exist and we're saving chunks locally
        if self.save_local_chunks:
            os.makedirs(self.local_chunks_dir, exist_ok=True)

    async def migrate_to_glacier(self) -> None:
        """Main migration process."""
        logger.info(f"Starting migration for bucket: {self.bucket_name}")
        logger.info(
            f"Processing files up to date: {self.max_date.strftime('%d-%m-%Y')}"
        )

        # Process files in batches to avoid loading everything into memory
        batch_size = 50000  # Process up to 50,000 files at a time
        continuation_token = None
        total_files_processed = 0

        while True:
            # Fetch a batch of files
            files_batch, continuation_token = await self.list_files_batch(
                continuation_token
            )

            if not files_batch:
                logger.info("No more files to process")
                break

            logger.info(f"Processing batch of {len(files_batch)} files")
            total_files_processed += len(files_batch)
            self.progress_tracker.update_total_files(total_files_processed)

            # Sort files by last modified date (ascending order)
            files_batch.sort(key=lambda f: f.last_modified)

            # Group files by month
            monthly_groups = self.group_files_by_month(files_batch)

            # Process chunks with parallel execution
            tasks = []
            for year_month, month_files in monthly_groups.items():
                task = asyncio.create_task(
                    self.process_year_month_async(year_month, month_files)
                )
                tasks.append(task)

            # Wait for all year-month tasks to complete before fetching next batch
            await asyncio.gather(*tasks)

            if not continuation_token:
                break

        logger.info(
            f"Migration completed! Processed {total_files_processed} files in total"
        )

    async def process_year_month_async(
        self, year_month: str, files: List[FileInfo]
    ) -> None:
        """Process all files for a given year-month."""
        logger.info(f"Processing files for {year_month} ({len(files)} files)")

        # Group files by month/day chunks (each chunk = max 100MB or 1000 files)
        chunks = self.create_chunks(files)

        # Track active threads
        self.progress_tracker.set_active_threads(len(chunks))

        # Initialize or get current chunk counter for this year-month
        if year_month not in self.chunk_counters:
            self.chunk_counters[year_month] = 0

        # Process chunks concurrently
        tasks = []
        for chunk_files in chunks:
            # Increment chunk counter and format as 9-digit zero-padded string
            self.chunk_counters[year_month] += 1
            chunk_name = f"{self.chunk_counters[year_month]:09d}"

            task = asyncio.create_task(
                self.process_chunk_async(chunk_files, year_month, chunk_name)
            )
            tasks.append(task)

        # Wait for all chunks to complete and track results
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results and update tracking
        successful = 0
        failed = 0
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Chunk processing failed with exception: {result}")
                failed += 1
                self.progress_tracker.increment_errors()
            elif result is True:
                successful += 1
            else:
                failed += 1
                self.progress_tracker.increment_failed_chunks()

        # Reset active threads count
        self.progress_tracker.set_active_threads(0)

        logger.info(
            f"Completed {year_month}: {successful} successful, {failed} failed chunks"
        )

    async def list_files_batch(
        self, continuation_token: Optional[str] = None
    ) -> Tuple[List[FileInfo], Optional[str]]:
        """Fetch a batch of files from S3, up to 1GB in size.

        Args:
            continuation_token: Token for paginating through S3 results

        Returns:
            Tuple of (list of FileInfo objects, next continuation token or None if no more files)
        """
        files = []
        total_size = 0

        async with self.session.client("s3") as s3:
            # Configure pagination parameters
            pagination_config = {
                "Bucket": self.bucket_name,
                "MaxKeys": 1000,  # S3 max page size
            }

            # Add continuation token if provided for pagination
            if continuation_token:
                pagination_config["ContinuationToken"] = continuation_token

            paginator = s3.get_paginator("list_objects_v2")

            # Process pages until we hit our size limit
            async for page in paginator.paginate(**pagination_config):
                if "Contents" in page:
                    for obj in page["Contents"]:
                        # Skip objects under glacier/ prefix (already migrated)
                        if obj["Key"].startswith("glacier/"):
                            continue

                        # Filter by max_date if specified
                        if (
                            self.max_date
                            and obj["LastModified"].replace(tzinfo=None) > self.max_date
                        ):
                            continue

                        # Add file to batch
                        file_info = FileInfo(
                            key=obj["Key"],
                            size=obj["Size"],
                            last_modified=obj["LastModified"].replace(tzinfo=None),
                        )

                        # If adding this file would exceed the 1GB limit, return current batch
                        if files and (total_size + file_info.size) > MAX_CHUNK_SIZE:
                            # Get the next continuation token if there are more results
                            next_token = page.get("NextContinuationToken")
                            return files, next_token

                        files.append(file_info)
                        total_size += file_info.size

                # Check if there are more results after this page
                if not page.get("IsTruncated", False):
                    break

        # If we get here, we've processed all files
        return files, None

    async def list_all_files(self) -> List[FileInfo]:
        """List all files from S3 bucket asynchronously."""
        all_files = []
        continuation_token = None

        while True:
            files_batch, continuation_token = await self.list_files_batch(
                continuation_token
            )
            all_files.extend(files_batch)

            if not continuation_token:
                break

        return all_files

    def group_files_by_month(self, files: List[FileInfo]) -> Dict[str, List[FileInfo]]:
        """Group files by their modification month."""
        groups = {}

        for file in files:
            year_month = file.last_modified.strftime("%Y-%m")
            if year_month not in groups:
                groups[year_month] = []
            groups[year_month].append(file)

        return groups

    def create_chunks(self, files: List[FileInfo]) -> List[List[FileInfo]]:
        """Create chunks with a size limit of 1GB per chunk.

        Args:
            files: List of FileInfo objects to be chunked

        Returns:
            List of chunks, where each chunk is a list of FileInfo objects
            with total size not exceeding MAX_CHUNK_SIZE (1GB)
        """
        chunks = []
        current_chunk = []
        current_size = 0

        for file in files:
            # If adding this file would exceed the chunk size, finalize current chunk
            if current_chunk and (current_size + file.size) > MAX_CHUNK_SIZE:
                chunks.append(current_chunk)
                current_chunk = []
                current_size = 0

            # Add the file to the current chunk
            current_chunk.append(file)
            current_size += file.size

        # Add the last chunk if it has files
        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    async def process_chunk_async(
        self, files: List[FileInfo], year_month: str, chunk_name: str
    ) -> bool:
        """Process a chunk of files asynchronously."""
        try:
            # Create temporary file for the chunk
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=".tar.gz"
            ) as temp_file:
                temp_path = temp_file.name

            try:
                # Create the archive with parallel file downloading
                await self.create_archive_async(files, temp_path)

                # Upload to glacier
                glacier_key = self.build_glacier_key(year_month, chunk_name)
                await self.upload_to_glacier_async(temp_path, glacier_key)

                # Save chunk locally if flag is enabled
                if self.save_local_chunks:
                    await self.save_chunk_locally(temp_path, year_month, chunk_name)

                # Delete original S3 objects after successful upload
                if self.delete_objects:
                    await self.delete_original_objects(files)

                logger.info(
                    f"Successfully processed chunk: {glacier_key} ({len(files)} files)"
                )
                if self.delete_objects:
                    logger.info(f"Deleted {len(files)} original objects from S3")
                self.progress_tracker.set_last_chunk(chunk_name, year_month)
                self.progress_tracker.increment_processed(len(files))
                return True

            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except Exception as e:
            logger.error(f"Failed to process chunk {chunk_name}: {str(e)}")
            self.progress_tracker.increment_errors()
            return False

    async def save_chunk_locally(
        self, temp_path: str, year_month: str, chunk_name: str
    ) -> None:
        """Save chunk locally with proper gzip format and directory structure.

        Directory structure: localchunks/<bucket-name>/<timestamp>/<year>/<month>/<chunk_name>.gz
        """
        # Create localchunks/bucket-name/timestamp/year/month directory structure
        year, month = year_month.split("-")
        local_dir = os.path.join(
            "localchunks", self.bucket_name, self.startup_timestamp, year, month
        )
        os.makedirs(local_dir, exist_ok=True)

        # Preserve .gz extension
        chunk_filename = f"{chunk_name}.gz"
        chunk_path = os.path.join(local_dir, chunk_filename)

        # Copy the temporary gzipped file to local location
        os.rename(temp_path, chunk_path)
        logger.info(f"Saved chunk locally: {chunk_path}")

    async def create_archive_async(
        self, files: List[FileInfo], archive_path: str
    ) -> None:
        """Create gzipped tar archive with parallel file downloads."""
        # Download files in parallel
        download_tasks = []
        for file in files:
            task = self.download_file_content(file)
            download_tasks.append(task)

        # Execute downloads with controlled concurrency
        file_contents = await asyncio.gather(*download_tasks)

        # Create archive in a thread to avoid blocking
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=1) as executor:
            await loop.run_in_executor(
                executor, self._create_archive_sync, files, file_contents, archive_path
            )

    def _create_archive_sync(
        self, files: List[FileInfo], file_contents: List[bytes], archive_path: str
    ) -> None:
        """Create archive synchronously (runs in thread)."""
        with gzip.open(archive_path, "wb") as gz_file:
            with tarfile.open(fileobj=gz_file, mode="w") as tar:
                for file_info, content in zip(files, file_contents):
                    if content is not None:
                        # Create tar info
                        tar_info = tarfile.TarInfo(name=file_info.key)
                        tar_info.size = len(content)
                        tar_info.mtime = int(file_info.last_modified.timestamp())
                        tar_info.mode = 0o644

                        # Add file to archive
                        tar.addfile(tar_info, fileobj=BytesIO(content))

    async def download_file_content(self, file_info: FileInfo) -> Optional[bytes]:
        """Download file content from S3 asynchronously."""
        async with self.download_semaphore:
            try:
                async with self.session.client("s3") as s3_client:
                    response = await s3_client.get_object(
                        Bucket=self.bucket_name, Key=file_info.key
                    )

                    # Read content asynchronously
                    content = await response["Body"].read()
                    return content

            except ClientError as e:
                logger.error(f"Failed to download {file_info.key}: {str(e)}")
                return None

    def build_glacier_key(self, year_month: str, chunk_name: str) -> str:
        """Build the glacier key path.

        Format: glacier/<timestamp>/<year>/<month>/<chunk_name>.gz
        """
        year, month = year_month.split("-")
        return (
            f"{GLACIER_PREFIX}/{self.startup_timestamp}/{year}/{month}/{chunk_name}.gz"
        )

    async def upload_to_glacier_async(self, file_path: str, glacier_key: str) -> None:
        """Upload file to glacier storage class asynchronously."""
        async with self.upload_semaphore:
            try:
                logger.info(f"Starting Glacier upload: {glacier_key}")
                file_size = os.path.getsize(file_path)
                logger.info(f"File size: {file_size} bytes")

                async with self.session.client("s3") as s3_client:
                    with open(file_path, "rb") as file:
                        logger.info(
                            f"Uploading to S3 bucket '{
                                self.bucket_name
                            }' with Glacier storage class..."
                        )
                        await s3_client.put_object(
                            Bucket=self.bucket_name,
                            Key=glacier_key,
                            Body=file,
                            StorageClass="DEEP_ARCHIVE",
                        )
                        logger.info(f"Successfully uploaded to Glacier: {glacier_key}")

                        # Verify the object was created with correct storage class
                        try:
                            response = await s3_client.head_object(
                                Bucket=self.bucket_name, Key=glacier_key
                            )
                            storage_class = response.get("StorageClass", "STANDARD")
                            logger.info(
                                f"Verified upload - Storage class: {storage_class}"
                            )
                        except Exception as verify_error:
                            logger.warning(
                                f"Could not verify storage class for {glacier_key}: {
                                    verify_error
                                }"
                            )

            except ClientError as e:
                logger.error(f"Failed to upload {glacier_key}: {str(e)}")
                raise  # Re-raise to prevent deletion of original objects

    async def delete_original_objects(self, files: List[FileInfo]) -> None:
        """Delete original S3 objects after successful Glacier upload."""
        try:
            async with self.session.client("s3") as s3_client:
                # Delete objects in batches for efficiency
                batch_size = 1000  # S3 delete limit

                for i in range(0, len(files), batch_size):
                    batch = files[i : i + batch_size]

                    # Prepare delete request
                    delete_objects = {"Objects": [{"Key": file.key} for file in batch]}

                    # Execute batch delete
                    response = await s3_client.delete_objects(
                        Bucket=self.bucket_name, Delete=delete_objects
                    )

                    # Log any errors
                    if "Errors" in response:
                        for error in response["Errors"]:
                            logger.error(
                                f"Failed to delete {error['Key']}: {error['Message']}"
                            )

                    # Log successful deletions
                    deleted_count = len(response.get("Deleted", []))
                    logger.info(
                        f"Deleted {deleted_count} objects from batch {
                            i // batch_size + 1
                        }"
                    )

        except Exception as e:
            logger.error(f"Failed to delete original objects: {str(e)}")
            raise  # Re-raise as this is critical for the migration process


def parse_date(date_str: str) -> datetime:
    """Parse date string in DD-MM-YYYY format."""
    try:
        return datetime.strptime(date_str, "%d-%m-%Y")
    except ValueError:
        raise ValueError(f"Invalid date format: {date_str}. Expected DD-MM-YYYY")


def get_default_max_date() -> datetime:
    """Get default max date (3 months ago from today)."""
    today = datetime.now()
    # Calculate 3 months ago
    if today.month <= 3:
        three_months_ago = today.replace(year=today.year - 1, month=today.month + 9)
    else:
        three_months_ago = today.replace(month=today.month - 3)

    return three_months_ago


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="S3 to Glacier Migration Script")
    parser.add_argument("bucket_name", help="S3 bucket name")
    parser.add_argument(
        "--last-date", help="Process files up to this date (DD-MM-YYYY)", default=None
    )
    parser.add_argument("--local", action="store_true", help="Save chunks locally")
    parser.add_argument(
        "--delete",
        type=int,
        choices=[0, 1],
        default=1,
        help="Delete original S3 objects after migration (1=delete, 0=keep)",
    )

    args = parser.parse_args()

    # Parse max date (default to 3 months ago)
    if args.last_date:
        try:
            max_date = parse_date(args.last_date)
        except ValueError as e:
            logger.error(f"Date parsing error: {e}")
            sys.exit(1)
    else:
        max_date = get_default_max_date()
        logger.info(
            f"Using default max date (3 months ago): {max_date.strftime('%d-%m-%Y')}"
        )

    # Create migrator and run migration
    migrator = S3ToGlacierMigrator(
        args.bucket_name,
        max_date,
        save_local_chunks=args.local,
        delete_objects=bool(args.delete),
    )

    try:
        await migrator.migrate_to_glacier()
        print("Migration completed successfully!")
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
