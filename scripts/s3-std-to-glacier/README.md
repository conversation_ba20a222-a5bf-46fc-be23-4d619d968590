# S3 to Glacier Migration Script

This Python script migrates files from S3 Standard storage to Glacier storage within the same S3 bucket, organizing them into monthly chunks with **async and parallel processing** for optimal performance.

## Flow

```mermaid
flowchart TD
    A[Start: main] --> B[Parse Arguments]
    B --> C{Date Provided?}
    C -->|Yes| D[Parse Date]
    C -->|No| E[Use Default Date<br/>3 months ago]
    D --> F[Create S3ToGlacierMigrator]
    E --> F
    F --> G[Initialize Progress Tracker]
    G --> H[Start migrate_to_glacier]
    
    H --> I[Initialize Batch Processing]
    I --> J[list_files_batch]
    J --> K{More Files?}
    K -->|No| Z[Migration Complete]
    K -->|Yes| L[Filter Files<br/>Skip glacier prefix<br/>Filter by max_date]
    
    L --> M[Sort Files by Date]
    M --> N[Group Files by Month]
    N --> O[Create Async Tasks<br/>for Each Month]
    O --> P[process_year_month_async]
    
    P --> Q[Create Chunks<br/>Max 1GB per chunk]
    Q --> R[Process Chunks Concurrently]
    R --> S[process_chunk_async]
    
    S --> T[Create Temp File]
    T --> U[create_archive_async]
    U --> V[Download Files in Parallel<br/>with Semaphore Control]
    V --> W[Create Gzipped Tar Archive]
    W --> X[Upload to Glacier<br/>DEEP_ARCHIVE storage class]
    X --> Y{Save Local?}
    Y -->|Yes| AA[Save Chunk Locally]
    Y -->|No| BB{Delete Objects?}
    AA --> BB
    BB -->|Yes| CC[Delete Original S3 Objects]
    BB -->|No| DD[Update Progress]
    CC --> DD
    DD --> EE[Clean Up Temp File]
    EE --> FF{More Chunks?}
    FF -->|Yes| S
    FF -->|No| GG[Update Progress Tracker]
    GG --> HH{More Months?}
    HH -->|Yes| P
    HH -->|No| II[Continue to Next Batch]
    II --> J
    
    
    
    style A fill:#e1f5fe
    style Z fill:#c8e6c9
    style S fill:#fff3e0
    style U fill:#f3e5f5
    style X fill:#ffebee
```

## Features

- **Async & Parallel Processing**: High-performance concurrent operations for faster migration
- **Monthly Grouping**: Files are grouped by their last modified date into monthly batches
- **Size-Limited Chunks**: Each chunk is limited to 1GB maximum size
- **Compressed Archives**: Files are stored in gzipped tar format while preserving original directory structure
- **Ordered Processing**: Files are processed in ascending order by modification date
- **Batch Processing**: Processes 1000 files at a time for efficient memory usage
- **Unique Naming**: Chunks are named sequentially starting from `000000000001.gz`
- **Concurrent Downloads**: Multiple files downloaded simultaneously for faster archive creation
- **Controlled Concurrency**: Configurable limits to prevent overwhelming S3 or system resources

## Directory Structure

### Glacier Storage
Files are stored in Glacier with the following structure, including a timestamp of when the migration run started:
```
glacier/<timestamp>/YYYY/MM/<chunk_name>.gz
```

### Local Storage
When saving chunks locally (with `--local` flag), they are stored in:
```
localchunks/<bucket-name>/<timestamp>/YYYY/MM/<chunk_name>.gz
```

Example paths:
```
# Glacier path
glacier/20250627_143045/2024/01/000000001.gz

# Local path (with bucket 'my-bucket')
localchunks/my-bucket/20250627_143045/2024/01/000000001.gz
```

**Note**: The timestamp (e.g., `20250627_143045`) is generated when the migration starts and remains consistent for all chunks in that run, making it easy to identify related chunks from the same migration.

## Prerequisites

- Python 3.8 or later
- AWS credentials configured (via AWS CLI, environment variables, or IAM roles)
- Appropriate S3 permissions for the target bucket

## Installation

1. Clone or download the script files
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

Run the script with your S3 bucket name and optionally specify a last date:

```bash
python main.py <bucket-name> [last-date-DD-MM-YYYY]
```

### Parameters

- `bucket-name`: S3 bucket name (required)
- `last-date`: Optional date in DD-MM-YYYY format. Only files modified up to this date will be processed
  - **Default**: 3 months ago from today

### Examples

```bash
# Use default date (3 months ago)
python main.py my-s3-bucket

# Process files up to a specific date
python main.py my-s3-bucket 15-03-2024

# Process files up to January 1st, 2024
python main.py my-s3-bucket 01-01-2024
```

### Date Filtering

The script includes intelligent date filtering to process only files that were last modified up to the specified date. This is useful for:

- **Incremental Migration**: Process files in batches by date ranges
- **Safe Migration**: Avoid moving recently modified files that might still be in use
- **Compliance**: Meet data retention policies by processing older files first
- **Performance**: Reduce processing time by limiting the scope

**Default Behavior**: If no date is specified, the script automatically processes files that are at least 3 months old, ensuring recently active files remain in standard storage.

## Performance Configuration

The script includes several configurable constants in `main.py`:

- `MAX_CHUNK_SIZE`: Maximum size per chunk (default: 1GB)
- `BATCH_SIZE`: Number of objects to fetch at once (default: 1000)
- `MAX_CONCURRENT_UPLOADS`: Maximum parallel uploads (default: 5)
- `MAX_CONCURRENT_DOWNLOADS`: Maximum parallel downloads (default: 10)
- `GLACIER_PREFIX`: Prefix for glacier storage (default: "glacier")
- `local_chunks_dir`: Base directory for local chunk storage (default: "./localchunks")

## AWS Permissions Required

The script requires the following S3 permissions:
- `s3:ListBucket` - To list objects in the bucket
- `s3:GetObject` - To read files from S3
- `s3:PutObject` - To upload chunks to Glacier storage class

## How It Works

1. **List Files**: Asynchronously retrieves all objects from the S3 bucket (excluding those already in glacier prefix)
2. **Sort Files**: Orders files by last modified date in ascending order
3. **Group by Month**: Groups files by their modification month (YYYY-MM format)
4. **Create Chunks**: Splits monthly groups into chunks not exceeding 1GB
5. **Parallel Processing**: Downloads multiple files concurrently for each chunk
6. **Archive Files**: Creates gzipped tar archives maintaining original file paths
7. **Upload to Glacier**: Uploads chunks to S3 with Glacier storage class using controlled concurrency

## Async & Parallel Features

- **Concurrent File Downloads**: Downloads multiple files simultaneously when creating archives
- **Parallel Chunk Processing**: Multiple chunks can be processed concurrently
- **Semaphore-Controlled Operations**: Prevents overwhelming S3 with too many concurrent requests
- **Non-blocking I/O**: Uses async/await for efficient resource utilization
- **Thread Pool Integration**: CPU-intensive tasks (compression) run in separate threads

## File Preservation

- Original file paths are preserved within the tar archives
- File modification times are maintained
- Directory structure is kept intact when extracting

## Monitoring

The script provides progress output including:
- Total files found for migration
- Files processed per month
- Successful chunk uploads with file counts

## Error Handling

The script includes comprehensive error handling for:
- AWS credential issues
- S3 access problems
- File processing errors
- Upload failures

If an error occurs, the script will stop and display the error message.

## Example Output

```
2024-06-27 14:30:45,123 - INFO - Starting migration for bucket: my-s3-bucket
2024-06-27 14:30:45,124 - INFO - Migration timestamp: 20250627_143045
2024-06-27 14:30:47,456 - INFO - Found 15423 files to migrate
2024-06-27 14:30:47,457 - INFO - Processing files for 2024-01 (8932 files)
2024-06-27 14:30:47,458 - INFO - Processing files for 2024-02 (6491 files)
2024-06-27 14:31:30,123 - INFO - Successfully created and uploaded chunk: glacier/20250627_143045/2024/01/000000001.gz (1247 files)
2024-06-27 14:31:32,234 - INFO - Saved chunk locally: localchunks/my-s3-bucket/20250627_143045/2024/01/000000001.gz
2024-06-27 14:31:35,567 - INFO - Successfully created and uploaded chunk: glacier/20250627_143045/2024/01/000000002.gz (1198 files)
2024-06-27 14:31:37,890 - INFO - Saved chunk locally: localchunks/my-s3-bucket/20250627_143045/2024/01/000000002.gz
...
2024-06-27 14:35:40,789 - INFO - Migration completed! Successful: 15, Failed: 0
Migration completed successfully!
```
