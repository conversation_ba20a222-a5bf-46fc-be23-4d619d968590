import boto3
import sys
import yaml
import os
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(name)s -  %(message)s"
)
logger = logging.getLogger(__name__)


def load_config(environment):
    SCRIPT_PATH = os.environ["RUN_GATEWAY_SCRIPT_PATH"]
    with open(f"{SCRIPT_PATH}/values.yaml", "r") as file:
        config = yaml.safe_load(file)
    return config[environment]


def get_latest_revision(task_definition_name, region):
    ecs_client = boto3.client("ecs", region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response["taskDefinition"]["revision"]


def get_container_name(task_definition_name, region):
    ecs_client = boto3.client("ecs", region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response["taskDefinition"]["containerDefinitions"][0]["name"]


def get_subnet_ids(vpc_name, region, subnet_names):
    ec2_client = boto3.client("ec2", region_name=region)
    response = ec2_client.describe_vpcs(
        Filters=[{"Name": "tag:Name", "Values": [vpc_name]}]
    )

    if not response["Vpcs"]:
        raise ValueError(f"VPC with name {vpc_name} not found")

    vpc_id = response["Vpcs"][0]["VpcId"]

    response = ec2_client.describe_subnets(
        Filters=[
            {"Name": "vpc-id", "Values": [vpc_id]},
        ]
    )
    subnet_ids = [subnet["SubnetId"] for subnet in response["Subnets"]]

    private_subnet_ids = []
    for subnet in response["Subnets"]:
        subnet_name = next(
            (tag["Value"] for tag in subnet.get("Tags", []) if tag["Key"] == "Name"),
            None,
        )
        if subnet_name in subnet_names:
            private_subnet_ids.append(subnet["SubnetId"])

    return private_subnet_ids


def get_security_group_ids(vpc_name, region, security_group_names):
    ec2_client = boto3.client("ec2", region_name=region)

    response = ec2_client.describe_vpcs(
        Filters=[{"Name": "tag:Name", "Values": [vpc_name]}]
    )
    if not response["Vpcs"]:
        raise ValueError(f"VPC with name {vpc_name} not found")
    vpc_id = response["Vpcs"][0]["VpcId"]

    response = ec2_client.describe_security_groups(
        Filters=[
            {"Name": "vpc-id", "Values": [vpc_id]},
            {"Name": "group-name", "Values": security_group_names},
        ]
    )

    security_group_ids = [sg["GroupId"] for sg in response["SecurityGroups"]]

    if len(security_group_ids) != len(security_group_names):
        missing_groups = set(security_group_names) - set(
            sg["GroupName"] for sg in response["SecurityGroups"]
        )
        logging.error(
            f"Warning: Not all security groups were found. Missing: {missing_groups}"
        )

    return security_group_ids


def run_task(environment, commandList):
    config = load_config(environment)
    logging.info("config loaded")
    vpc_name = config["vpc_name"]
    region = config["region"]
    cluster_name = config["cluster_name"]
    subnet_names = config["private_subnet_names"]
    security_group_names = config["security_group_names"]

    ecs_client = boto3.client("ecs", region_name=region)
    latest_revision = get_latest_revision("gateway-subcommand", region)
    container_name = get_container_name("gateway-subcommand", region)
    subnet_ids = get_subnet_ids(vpc_name, region, subnet_names)
    security_group_ids = get_security_group_ids(vpc_name, region, security_group_names)
    response = ecs_client.run_task(
        cluster=cluster_name,
        taskDefinition=f"gateway-subcommand:{latest_revision}",
        count=1,
        launchType="FARGATE",
        networkConfiguration={
            "awsvpcConfiguration": {
                "subnets": subnet_ids,
                "securityGroups": security_group_ids,
                "assignPublicIp": "DISABLED",
            }
        },
        overrides={
            "containerOverrides": [{"name": container_name, "command": commandList}]
        },
        tags=[
            {"key": "Name", "value": f"ravenclaw-gateway-subcommand"},
            {"key": "Product", "value": "ravenclaw"},
            {"key": "Team", "value": "engineering"},
            {"key": "Application", "value": "go"},
            {"key": "Environment", "value": environment},
        ],
    )

    logging.info("Task started:", response)


if __name__ == "__main__":
    commandList = ["gateway"]
    gatewayCommand = sys.argv[1].lower()
    gatewayCommandList = ["subscribe", "sync-employees", "sync-employee-relationships"]

    if gatewayCommand not in gatewayCommandList:
        logging.error(
            "Usage: python gateway.py <Command> <Environment> [arguments] [flags]"
        )
        logging.error(f"<Command> should be any of {gatewayCommandList}")
        sys.exit(1)
    commandList.append(gatewayCommand)

    environment = sys.argv[2].lower()
    if environment not in ["dev", "prod"]:
        logging.error("Invalid environment. Use 'dev' or 'prod'.")
        sys.exit(1)

    if gatewayCommand == "sync-employee-relationships":
        arg_idx = 3

        syncEmployeeCommandFlag = sys.argv[3]
        syncEmployeeCommandFlagList = ["-o", "-b"]
        for i in range(len(syncEmployeeCommandFlagList)):
            flag = sys.argv[arg_idx]
            if flag not in syncEmployeeCommandFlagList:
                logging.error(
                    f"Usage: Flags for sync-employees command must be any of {
                        syncEmployeeCommandFlagList
                    }"
                )
                sys.exit(1)
            commandList.append(flag)
            syncEmployeeCommandFlagList.remove(flag)

            flagValue = sys.argv[arg_idx + 1]
            commandList.append(flagValue)
            arg_idx += 2

    if gatewayCommand == "subscribe" or gatewayCommand == "renew":
        subscribeSubcommand = sys.argv[3]
        subscribeSubcommandList = ["create", "renew", "stop"]
        if subscribeSubcommand not in subscribeSubcommandList:
            logging.error(
                f"Usage: subcommand for subscribe should be any of {
                    subscribeSubcommandList
                }"
            )
            sys.exit(1)
        commandList.append(subscribeSubcommand)

        if subscribeSubcommand == "create":
            arg_idx = 4

            subscribeSubcommandFlag = sys.argv[4]
            subscribeSubcommandFlagList = ["-e", "-o"]
            for i in range(len(subscribeSubcommandFlagList)):
                flag = sys.argv[arg_idx]
                if flag not in subscribeSubcommandFlagList:
                    logging.error(
                        f"Usage: Flags for create subcommand should be any of {
                            subscribeSubcommandFlagList
                        }"
                    )
                    sys.exit(1)
                commandList.append(flag)
                subscribeSubcommandFlagList.remove(flag)

                flagValue = sys.argv[arg_idx + 1]
                commandList.append(flagValue)

                arg_idx += 2

        if subscribeSubcommand == "stop":
            arg_idx = 4

            subscribeSubcommandFlag = sys.argv[4]
            subscribeSubcommandFlagList = ["-o"]
            for i in range(len(subscribeSubcommandFlagList)):
                flag = sys.argv[arg_idx]
                if flag not in subscribeSubcommandFlagList:
                    logging.error(
                        f"Usage: Flags for create subcommand should be any of {
                            subscribeSubcommandFlagList
                        }"
                    )
                    sys.exit(1)
                commandList.append(flag)
                subscribeSubcommandFlagList.remove(flag)

                flagValue = sys.argv[arg_idx + 1]
                commandList.append(flagValue)

                arg_idx += 2

    if gatewayCommand == "sync-employees":
        arg_idx = 3

        syncEmployeeCommandFlag = sys.argv[3]
        syncEmployeeCommandFlagList = ["--on-conflict", "-o"]
        for i in range(len(syncEmployeeCommandFlagList)):
            flag = sys.argv[arg_idx]
            if flag not in syncEmployeeCommandFlagList:
                logging.error(
                    f"Usage: Flags for sync-employees command must be any of {
                        syncEmployeeCommandFlagList
                    }"
                )
                sys.exit(1)
            commandList.append(flag)
            syncEmployeeCommandFlagList.remove(flag)

            flagValue = sys.argv[arg_idx + 1]
            commandList.append(flagValue)

            arg_idx += 2
    print(commandList)
    run_task(environment, commandList)
