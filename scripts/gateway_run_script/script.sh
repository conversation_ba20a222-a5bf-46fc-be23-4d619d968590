run_gateway() {
    # Get the absolute path of this script
    SCRIPT_DIR="$RUN_GATEWAY_SCRIPT_PATH"

    # Set the path to the Python script and virtual environment relative to the script's location
    SCRIPT_PATH="$SCRIPT_DIR/main.py"
    VENV_PATH="$SCRIPT_DIR/venv"

    if [ -d "$VENV_PATH" ]; then
        source "$VENV_PATH/bin/activate"
    fi

    # Run the Python script with all passed arguments
    python3 "$SCRIPT_PATH" "$@"

    # Deactivate virtual environment if it was activated
    if [ -n "$VIRTUAL_ENV" ]; then
        deactivate
    fi
}
