import boto3
import sys
import yaml
import os
import argparse

def load_config(environment):
    SCRIPT_PATH = os.environ['RUN_SETUP_SCRIPT_PATH']
    with open(f'{SCRIPT_PATH}/values.yaml', 'r') as file:
        config = yaml.safe_load(file)
    return config[environment]

def get_latest_revision(task_definition_name, region):
    ecs_client = boto3.client('ecs', region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response['taskDefinition']['revision']

def get_container_name(task_definition_name, region):
    ecs_client = boto3.client('ecs', region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response['taskDefinition']['containerDefinitions'][0]['name']

def get_subnet_ids(vpc_name, region, subnet_names):
    ec2_client = boto3.client('ec2', region_name=region)
    response = ec2_client.describe_vpcs(Filters=[{'Name': 'tag:Name', 'Values': [vpc_name]}])


    if not response['Vpcs']:
        raise ValueError(f"VPC with name {vpc_name} not found")

    vpc_id = response['Vpcs'][0]['VpcId']

    response = ec2_client.describe_subnets(Filters=[
        {'Name': 'vpc-id', 'Values': [vpc_id]},
    ])
    subnet_ids = [subnet['SubnetId'] for subnet in response['Subnets']]

    private_subnet_ids = []
    for subnet in response['Subnets']:
        subnet_name = next((tag['Value'] for tag in subnet.get('Tags', []) if tag['Key'] == 'Name'), None)
        if subnet_name in subnet_names:
            private_subnet_ids.append(subnet['SubnetId'])

    return private_subnet_ids

def get_security_group_ids(vpc_name, region, security_group_names):
    ec2_client = boto3.client('ec2', region_name=region)

    response = ec2_client.describe_vpcs(Filters=[{'Name': 'tag:Name', 'Values': [vpc_name]}])
    if not response['Vpcs']:
        raise ValueError(f"VPC with name {vpc_name} not found")
    vpc_id = response['Vpcs'][0]['VpcId']

    response = ec2_client.describe_security_groups(Filters=[
        {'Name': 'vpc-id', 'Values': [vpc_id]},
        {'Name': 'group-name', 'Values': security_group_names}
    ])

    security_group_ids = [sg['GroupId'] for sg in response['SecurityGroups']]

    if len(security_group_ids) != len(security_group_names):
        missing_groups = set(security_group_names) - set(sg['GroupName'] for sg in response['SecurityGroups'])
        print(f"Warning: Not all security groups were found. Missing: {missing_groups}")


    return security_group_ids

def run_task(command, org_id, user_id, realm, environment):
    config = load_config(environment)
    vpc_name = config['vpc_name']
    region = config['region']
    cluster_name = config['cluster_name']
    subnet_names = config['private_subnet_names']
    security_group_names = config['security_group_names']

    ecs_client = boto3.client('ecs', region_name=region)
    latest_revision = get_latest_revision('setup', region)
    container_name = get_container_name('setup', region)
    subnet_ids = get_subnet_ids(vpc_name, region, subnet_names)
    security_group_ids = get_security_group_ids(vpc_name, region, security_group_names)

    task_command = ['setup', command]

    if command == 'keycloak':
        task_command += ['--orgId', org_id, '--userId', user_id, '--realm', realm]

    response = ecs_client.run_task(
        cluster=cluster_name,
        taskDefinition=f'setup:{latest_revision}',
        count=1,
        launchType='FARGATE',
        networkConfiguration={
            'awsvpcConfiguration': {
                'subnets': subnet_ids,
                'securityGroups': security_group_ids,
                'assignPublicIp': 'DISABLED'
            }
        },
        overrides={
            'containerOverrides': [{
                'name': container_name,
                'command': task_command
            }]
        },
        tags=[
            {'key': 'Name', 'value': f'ravenclaw-setup'},
            {'key': 'Product', 'value': 'ravenclaw'},
            {'key': 'Team', 'value': 'engineering'},
            {'key': 'Application', 'value': 'go'},
            {'key': 'Environment', 'value': environment}
        ]
    )

    print('Task started:', response)


if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="ravenclaw setup [tool]")
    parser.add_argument("environment", choices=["dev", "prod"], nargs="?", default="dev", help="Environment (default: dev)")
    parser.add_argument("command", choices=["keycloak", "nats", "vault"], help="ravenclaw setup [Environment] [command]")
    parser.add_argument("-o", "--orgId",  help="Organization ID for Keycloak", default=None)
    parser.add_argument("-u", "--userId", help="User ID (Required for keycloak)", default=None)
    parser.add_argument("-r", "--realm", help="Keycloak realm name (Required for keycloak)", default=None)
    args = parser.parse_args()

    if args.command == "keycloak" and (not args.orgId or not args.userId or not args.realm):
        parser.error("The keycloak command requires --orgId/-o, --userId/-u and --realm/-r flags.")

    run_task(args.command, args.orgId, args.userId, args.realm, args.environment)
