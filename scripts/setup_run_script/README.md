# Run `Setup`

- A python script `main.py` is created to run the `setup` task from the task definition deployed on ECS
- `script.sh` contains a bash function which creates a virtual environment, run the python script and then deactivates the virtual environment
- All the related IAM permissions have been provided to Bastion EC2 instance
- An alias has been created to run the setup with minimal effort

## How to run

- If running from bastion instance, always use `dev` infra bastion for running tasks in dev and prod bastion to run in prod infra.

- Following below alias command to run

```bash
setup [environment] [command] -o <orgID> -u <userId> -r <realm>
OR
setup [environment] [command] --orgId <orgID> --userId <userId> --realm <realm>
```

- [environment] takes value from {dev, prod}
   - default is dev

- [command] takes value from {nats, vault, ravenclaw}
   - if command is given as `ravenclaw`
     - then orgId, userId and realm are required

---

### Run and make changes locally

1. Create a virtual environment
```bash
cd ops/scripts/setup_run_script
python3 -m venv venv
source venv/bin/activate
```

- To deactivate the environment
```bash
deactivate
```

2. install all the dependencies
```bash
pip3 install -r requirements.txt
```

3. Load the `script.sh` into `.bashrc` or `.zshrc`

> NOTE: Before moving forward, edit the `script.sh` and add correct path to your main.py and virtual env respectively


```bash
# make script.sh executable
chmod +x script.sh

# add below lines at the end of your `.bashrc` or `.zshrc` file
export RUN_SETUP_SCRIPT_PATH=<path_to_script_folder>/scripts/setup_run_script
source <absolute_paht>/script.sh
alias setup=run_setup
```
