import boto3
import sys
import yaml
import os
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s -  %(message)s')
logger = logging.getLogger(__name__)

def load_config(environment):
    SCRIPT_PATH = os.environ['RUN_INGESTION_SCRIPT_PATH']
    with open(f'{SCRIPT_PATH}/values.yaml', 'r') as file:
        config = yaml.safe_load(file)
    return config[environment]

def get_task_definition(task_definition_name, region):
    ecs_client = boto3.client('ecs', region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response['taskDefinition']

def get_container_name(task_definition_name, region):
    ecs_client = boto3.client('ecs', region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response['taskDefinition']['containerDefinitions'][0]['name']

def get_subnet_ids(vpc_name, region, subnet_names):
    ec2_client = boto3.client('ec2', region_name=region)
    response = ec2_client.describe_vpcs(Filters=[{'Name': 'tag:Name', 'Values': [vpc_name]}])


    if not response['Vpcs']:
        raise ValueError(f"VPC with name {vpc_name} not found")

    vpc_id = response['Vpcs'][0]['VpcId']

    response = ec2_client.describe_subnets(Filters=[
        {'Name': 'vpc-id', 'Values': [vpc_id]},
    ])
    # subnet_ids = [subnet['SubnetId'] for subnet in response['Subnets']]

    private_subnet_ids = []
    for subnet in response['Subnets']:
        subnet_name = next((tag['Value'] for tag in subnet.get('Tags', []) if tag['Key'] == 'Name'), None)
        if subnet_name in subnet_names:
            private_subnet_ids.append(subnet['SubnetId'])

    return private_subnet_ids

def get_security_group_ids(vpc_name, region, security_group_names):
    ec2_client = boto3.client('ec2', region_name=region)

    response = ec2_client.describe_vpcs(Filters=[{'Name': 'tag:Name', 'Values': [vpc_name]}])
    if not response['Vpcs']:
        raise ValueError(f"VPC with name {vpc_name} not found")
    vpc_id = response['Vpcs'][0]['VpcId']

    response = ec2_client.describe_security_groups(Filters=[
        {'Name': 'vpc-id', 'Values': [vpc_id]},
        {'Name': 'group-name', 'Values': security_group_names}
    ])

    security_group_ids = [sg['GroupId'] for sg in response['SecurityGroups']]

    if len(security_group_ids) != len(security_group_names):
        missing_groups = set(security_group_names) - set(sg['GroupName'] for sg in response['SecurityGroups'])
        logging.error(f"Warning: Not all security groups were found. Missing: {missing_groups}")


    return security_group_ids

def register_new_task_revision(task_definition, new_command, region):
    ecs_client = boto3.client('ecs', region_name=region)

    # Remove unnecessary fields
    keys_to_remove = ['status', 'revision', 'taskDefinitionArn', 'registeredAt', 'registeredBy', 'compatibilities', 'requiresAttributes']
    for key in keys_to_remove:
        task_definition.pop(key, None)

    # Update the container's command
    task_definition['containerDefinitions'][0]['command'] = new_command

    # Register new task definition revision
    response = ecs_client.register_task_definition(**task_definition)
    new_task_revision = response['taskDefinition']['revision']
    return new_task_revision
    
def update_ecs_service(cluster_name, service_name, task_definition_name, new_command, region):
    ecs_client = boto3.client('ecs', region_name=region)

    # Fetch current task definition
    task_definition = get_task_definition(task_definition_name, region)

    # Register a new revision with the updated command
    new_revision = register_new_task_revision(task_definition, new_command, region)

    # Update the ECS service with the new task definition
    ecs_client.update_service(
        cluster=cluster_name,
        service=service_name,
        taskDefinition=f"{task_definition_name}:{new_revision}",
        forceNewDeployment=True,  # Ensures running tasks restart with new definition
        desiredCount=1
    )

    logger.info(f"ECS Service '{service_name}' updated with new task definition revision {new_revision}")

def update_service(environment, new_command):
    config = load_config(environment)
    logger.info("Config loaded")

    region = config['region']
    cluster_name = config['cluster_name']
    service_name = "analytics-engine"
    task_definition_name = "analytics-engine"

    update_ecs_service(cluster_name, service_name, task_definition_name, new_command, region)

if __name__ == "__main__":
    if len(sys.argv)<=3:
        # Environment means either dev or prod
        # Default is set to dev
        logging.error("Usage: python main.py <Environment> [arguments] [flags]")
        sys.exit(1)

    commandList = ["analytics"]

    
    environment = sys.argv[1].lower()
    if environment not in ['dev', 'prod']:
        logging.error("Invalid environment. Use 'dev' or 'prod'.")
        sys.exit(1)
        
    argument = sys.argv[2]
    if argument not in ['-r']:
        logging.error("Invalid command: Use proper argument")
        sys.exit(1)

    commandList.append(argument)

    arg_value = sys.argv[3]
    commandList.append(arg_value)


    print(commandList)
    update_service(environment, commandList)
