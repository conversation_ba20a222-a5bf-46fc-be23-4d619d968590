import boto3
import sys
import yaml
import os
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(name)s -  %(message)s"
)
logger = logging.getLogger(__name__)


def load_config(environment):
    SCRIPT_PATH = os.environ["RUN_INGESTION_SCRIPT_PATH"]
    with open(f"{SCRIPT_PATH}/values.yaml", "r") as file:
        config = yaml.safe_load(file)
    return config[environment]


def get_latest_revision(task_definition_name, region):
    ecs_client = boto3.client("ecs", region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response["taskDefinition"]["revision"]


def get_container_name(task_definition_name, region):
    ecs_client = boto3.client("ecs", region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response["taskDefinition"]["containerDefinitions"][0]["name"]


def get_subnet_ids(vpc_name, region, subnet_names):
    ec2_client = boto3.client("ec2", region_name=region)
    response = ec2_client.describe_vpcs(
        Filters=[{"Name": "tag:Name", "Values": [vpc_name]}]
    )

    if not response["Vpcs"]:
        raise ValueError(f"VPC with name {vpc_name} not found")

    vpc_id = response["Vpcs"][0]["VpcId"]

    response = ec2_client.describe_subnets(
        Filters=[
            {"Name": "vpc-id", "Values": [vpc_id]},
        ]
    )
    subnet_ids = [subnet["SubnetId"] for subnet in response["Subnets"]]

    private_subnet_ids = []
    for subnet in response["Subnets"]:
        subnet_name = next(
            (tag["Value"] for tag in subnet.get("Tags", []) if tag["Key"] == "Name"),
            None,
        )
        if subnet_name in subnet_names:
            private_subnet_ids.append(subnet["SubnetId"])

    return private_subnet_ids


def get_security_group_ids(vpc_name, region, security_group_names):
    ec2_client = boto3.client("ec2", region_name=region)

    response = ec2_client.describe_vpcs(
        Filters=[{"Name": "tag:Name", "Values": [vpc_name]}]
    )
    if not response["Vpcs"]:
        raise ValueError(f"VPC with name {vpc_name} not found")
    vpc_id = response["Vpcs"][0]["VpcId"]

    response = ec2_client.describe_security_groups(
        Filters=[
            {"Name": "vpc-id", "Values": [vpc_id]},
            {"Name": "group-name", "Values": security_group_names},
        ]
    )

    security_group_ids = [sg["GroupId"] for sg in response["SecurityGroups"]]

    if len(security_group_ids) != len(security_group_names):
        missing_groups = set(security_group_names) - set(
            sg["GroupName"] for sg in response["SecurityGroups"]
        )
        logging.error(
            f"Warning: Not all security groups were found. Missing: {missing_groups}"
        )

    return security_group_ids


def run_task(environment, commandList):
    config = load_config(environment)
    logging.info("config loaded")
    vpc_name = config["vpc_name"]
    region = config["region"]
    cluster_name = config["cluster_name"]
    subnet_names = config["private_subnet_names"]
    security_group_names = config["security_group_names"]

    ecs_client = boto3.client("ecs", region_name=region)
    latest_revision = get_latest_revision("ingestion", region)
    container_name = get_container_name("ingestion", region)
    subnet_ids = get_subnet_ids(vpc_name, region, subnet_names)
    security_group_ids = get_security_group_ids(vpc_name, region, security_group_names)
    response = ecs_client.run_task(
        cluster=cluster_name,
        taskDefinition=f"ingestion:{latest_revision}",
        count=1,
        launchType="FARGATE",
        networkConfiguration={
            "awsvpcConfiguration": {
                "subnets": subnet_ids,
                "securityGroups": security_group_ids,
                "assignPublicIp": "DISABLED",
            }
        },
        overrides={
            "containerOverrides": [{"name": container_name, "command": commandList}]
        },
        tags=[
            {"key": "Name", "value": f"ravenclaw-ingestion"},
            {"key": "Product", "value": "ravenclaw"},
            {"key": "Team", "value": "engineering"},
            {"key": "Application", "value": "go"},
            {"key": "Environment", "value": environment},
        ],
    )

    logging.info("Task started:", response)


if __name__ == "__main__":
    if len(sys.argv) <= 3:
        # Environment means either dev or prod
        # Default is set to dev
        logging.error(
            "Usage: python ingestion.py <Command> <Environment> [arguments] [flags]"
        )
        logging.error("<Command> can be historical or migrate")
        logging.error("[arguments] [flags] is passed depending on the <Command>")
        sys.exit(1)

    commandList = ["ingestion"]

    command = sys.argv[1]
    if command not in ["migrate", "historical", "vendorDetection"]:
        logging.error("Invalid command: Use proper ingestion command")
        sys.exit(1)

    commandList.append(command)

    environment = sys.argv[2].lower()
    if environment not in ["dev", "prod"]:
        logging.error("Invalid environment. Use 'dev' or 'prod'.")
        sys.exit(1)

    if command == "historical":
        flagList = ["-o", "-p"]
        arg_idx = 3
        for i in range(len(flagList)):
            flag = sys.argv[arg_idx]

            if flag not in flagList:
                logging.error(
                    "Usage: python ingestion.py historical <dev/prod> -o <orgId> -p <parallelEmployeeIngestions>"
                )
                sys.exit(1)

            flagValue = sys.argv[arg_idx + 1]
            if flag == "-d":
                commandList.append(f"--detectVendor={flagValue}")
                flagList.remove(flag)
                arg_idx += 2
            else:
                commandList.append(flag)
                commandList.append(flagValue)
                flagList.remove(flag)
                arg_idx += 2

    if command == "migrate":
        migrateCommand = None
        batchSizeFlag = None
        batchSize = None

        if len(sys.argv) == 4 or len(sys.argv) <= 10:
            migrateCommand = sys.argv[3]
            if migrateCommand not in [
                "internetmsgid",
                "sentat",
                "remoteId",
                "backfill_sentat_orgid_emails",
                "backfill_single_attachment_invoices",
                "backfill_multi_attachment_invoices",
            ]:
                logging.error(
                    "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -o <org-id> -b <batchSize> -d <timeout for s3 operation> -p <parallelization limit>"
                )
                logging.error(
                    "Options for <migrateCommand> are internetmsgid, senderid, sentat. backfill_sentat_orgid_emails and remoteId"
                )
                logging.error(
                    "senderid do not require -b/-d/-p flag and respective value"
                )
                logging.error("org-id is required only for remoteId command")
                sys.exit(1)

            if migrateCommand == "senderid" and len(sys.argv) == 4:
                commandList.append(migrateCommand)
            elif migrateCommand == "senderid" and len(sys.argv) != 4:
                logging.error(
                    "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -o <org-id> -b <batchSize> -d <timeout for s3 operation> -p <parallelization limit>"
                )
                logging.error(
                    "Options for <migrateCommand> are internetmsgid, senderid, backfill_sentat_orgid_emails, sentat and remoteId"
                )
                logging.error(
                    "senderid do not require -b/-d/-p flag and respective value"
                )
                logging.error("org-id is required only for remoteId command")
                sys.exit(1)

            if migrateCommand == "remoteId":
                commandList.append(migrateCommand)
                flagList = ["-o", "-s"]
                arg_idx = 4
                for i in range(len(flagList)):
                    flag = sys.argv[arg_idx]

                    if flag not in flagList:
                        logging.error(
                            "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -o <orgId> -s <true/false>"
                        )
                        sys.exit(1)

                    if flag != "-s":
                        commandList.append(flag)
                        flagList.remove(flag)
                        flagValue = sys.argv[arg_idx + 1]
                        commandList.append(flagValue)
                        arg_idx += 2
                    else:
                        flagValue = sys.argv[arg_idx + 1]
                        commandList.append(f"-s={flagValue}")
                        arg_idx += 2
                        flagList.remove(flag)

            if migrateCommand == "backfill_sentat_orgid_emails" and len(sys.argv) == 8:
                commandList.append(migrateCommand)
                flagList = ["-o", "-b"]
                arg_idx = 4
                for i in range(len(flagList)):
                    flag = sys.argv[arg_idx]

                    if flag not in flagList:
                        logging.error(
                            "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -b <batchSize> -o <orgId>"
                        )
                        sys.exit(1)
                    commandList.append(flag)
                    flagList.remove(flag)

                    flagValue = sys.argv[arg_idx + 1]
                    commandList.append(flagValue)
                    arg_idx += 2

            if migrateCommand == "backfill_single_attachment_invoices":
                commandList.append(migrateCommand)
                flagList = ["-o"]
                arg_idx = 4
                for i in range(len(flagList)):
                    flag = sys.argv[arg_idx]

                    if flag not in flagList:
                        logging.error(
                            "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -o <orgId>"
                        )
                        sys.exit(1)
                    commandList.append(flag)
                    flagList.remove(flag)

                    flagValue = sys.argv[arg_idx + 1]
                    commandList.append(flagValue)
                    arg_idx += 2

            if migrateCommand == "backfill_multi_attachment_invoices":
                commandList.append(migrateCommand)
                flagList = ["-o"]
                arg_idx = 4
                for i in range(len(flagList)):
                    flag = sys.argv[arg_idx]

                    if flag not in flagList:
                        logging.error(
                            "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -o <orgId>"
                        )
                        sys.exit(1)
                    commandList.append(flag)
                    flagList.remove(flag)
                    print(commandList)

                    flagValue = sys.argv[arg_idx + 1]
                    commandList.append(flagValue)
                    arg_idx += 2

            elif (
                migrateCommand == "backfill_sentat_orgid_emails" and len(sys.argv) != 8
            ):
                logging.error(
                    "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -o <org-id> -b <batchSize> -d <timeout for s3 operation> -p <parallelization limit>"
                )
                logging.error(
                    "Options for <migrateCommand> are internetmsgid, senderid, sentat, backfill_sentat_orgid_emails and remoteId"
                )
                logging.error(
                    "senderid do not require -b/-d/-p flag and respective value"
                )
                logging.error("org-id is required only for remoteId command")
                sys.exit(1)

            if migrateCommand not in [
                "senderid",
                "remoteId",
                "backfill_sentat_orgid_emails",
                "backfill_single_attachment_invoices",
                "backfill_multi_attachment_invoices",
            ]:
                commandList.append(migrateCommand)

                flagList = ["-b", "-p", "-d"]
                arg_idx = 4
                for i in range(len(flagList)):
                    flag = sys.argv[arg_idx]

                    if flag not in flagList:
                        logging.error(
                            "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -b <batchSize> -d <timeout for s3 operation> -p <parallelization limit>"
                        )
                        sys.exit(1)
                    commandList.append(flag)
                    flagList.remove(flag)

                    flagValue = sys.argv[arg_idx + 1]

                    if flagValue.isdigit():
                        size = int(flagValue)
                    else:
                        logging.error(
                            "Please enter an integer value for batchsize/timeout/parallelization"
                        )
                        sys.exit(1)
                    commandList.append(flagValue)
                    arg_idx += 2

    if command == "vendorDetection":
        vendorDetectionSubCommand = sys.argv[3]
        print(vendorDetectionSubCommand)
        if vendorDetectionSubCommand not in ["cleanup", "retry", "start"]:
            logging.error("Enter correct vendorDetection subcommand")
            sys.exit(1)
        commandList.append(vendorDetectionSubCommand)
        if vendorDetectionSubCommand == "cleanup":
            flagList = ["-o", "-f"]
            arg_idx = 4
            for i in range(len(flagList)):
                flag = sys.argv[arg_idx]

                if flag not in flagList:
                    logging.error("Enter correct arguments for vendorDetection")
                    sys.exit(1)
                flagValue = sys.argv[arg_idx + 1]
                print(flagValue)
                if flag == "-f":
                    commandList.append(f"--forceCleanup={flagValue}")
                    flagList.remove(flag)
                    arg_idx += 1
                else:
                    commandList.append(flag)
                    commandList.append(flagValue)
                    flagList.remove(flag)
                    arg_idx += 2

        elif vendorDetectionSubCommand == "start":
            flagList = ["-o", "-b", "-c", "-r"]
            arg_idx = 4
            for i in range(len(flagList)):
                flag = sys.argv[arg_idx]

                if flag not in flagList:
                    logging.error("Enter correct arguments for vendorDetection")
                    sys.exit(1)
                flagValue = sys.argv[arg_idx + 1]
                commandList.append(flag)
                commandList.append(flagValue)
                flagList.remove(flag)
                arg_idx += 2

        elif vendorDetectionSubCommand == "retry":
            flagList = ["-o", "-p"]
            arg_idx = 4
            for i in range(len(flagList)):
                flag = sys.argv[arg_idx]
                if flag not in flagList:
                    logging.error("Enter correct arguments for vendorDetection")
                    sys.exit(1)
                flagValue = sys.argv[arg_idx + 1]
                commandList.append(flag)
                commandList.append(flagValue)
                flagList.remove(flag)
                arg_idx += 2

        else:
            logging.error("Error: Wrong number of arguments passed for <migrate>")
            logging.error(
                "Usage: python ingestion.py migrate <dev/prod> <migrateCommand> -o <org-id> -b <batchSize> -d <timeout for s3 operation> -p <parallelization limit>"
            )
            logging.error(
                "Options for <migrateCommand> are internetmsgid, senderid, sentat, backfill_sentat_orgid_emails and remoteId"
            )
            logging.error("senderid do not require -b/-d/-p flag and respective value")
            logging.error("org-id is required only for remoteId command")
            sys.exit(1)
    print(commandList)
    run_task(environment, commandList)
