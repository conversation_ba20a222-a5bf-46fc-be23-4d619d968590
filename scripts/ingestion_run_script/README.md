# Run `Historical Ingestion`

- A python script `main.py` is created to run the `ingestion` task from the task definition deployed on ECS
- `script.sh` contains a bash function which creates a virtual environment, run the python script and then deactivates the virtual environment
- All the related IAM permissions have been provided to Bastion EC2 instance
- An alias has been created to run the ingestion with minimal effort

## How to run

- If running from bastion instance, always use `dev` infra bastion for running tasks in both dev and prod infra.

- Following below alias command to run

```bash
ingestion <command> <environment> <orgID>
```

### Run in dev

- To run in dev infra,

```bash
ingestion historical dev <orgID>
ingestion migrate dev
```

### Run in prod
- To run in prod infra,

```bash
ingestion historical prod <orgID>
ingestion migrate prod
```

### Run and make changes locally

1. Create a virtual environment
```bash
cd ops/scripts/ingestion_run_script
python3 -m venv venv
source venv/bin/activate
```

- To deactivate the environment
```bash
deactivate
```

2. install all the dependencies
```bash
pip3 install -r requirements.txt
```

3. Load the `script.sh` into `.bashrc` or `.zshrc`

> NOTE: Before moving forward, edit the `script.sh` and add correct path to your main.py and virtual env respectively


```bash
# make script.sh executable
chmod +x script.sh

# add below lines at the end of your `.bashrc` or `.zshrc` file
export RUN_INGESTION_SCRIPT_PATH=<path_to_script_folder>/scripts/ingestion_run_script
source <absolute_paht>/script.sh
alias ingestion=run_ingestion
```
