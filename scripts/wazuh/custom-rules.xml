<group name="amazon,aws,cloudtrail">

  <!-- <PERSON><PERSON> wodle -->
  <rule id="180200" level="0">
    <decoded_as>json</decoded_as>
    <field name="integration">aws</field>
    <options>no_full_log</options>
    <description>AWS alert.</description>
  </rule>

  <!-- Cloudtrail -->

  <!-- Filter by eventName: etc/lists/amazon/aws-eventnames -->
  <rule id="180202" level="3">
    <if_sid>180200</if_sid>
    <field name="aws.source">cloudtrail</field>
    <list field="aws.eventName" lookup="match_key">etc/lists/amazon/aws-eventnames</list>
    <options>no_full_log</options>
    <description>AWS Cloudtrail: $(aws.eventSource) - $(aws.eventName).</description>
    <group>aws_cloudtrail,gdpr_IV_35.7.d,hipaa_164.312.b,nist_800_53_AU.6,pci_dss_10.6.1,tsc_CC7.2,tsc_CC7.3,</group>
  </rule>

  <!-- If there is an error code: increase the level and change description -->
  <rule id="180203" level="12">
    <if_sid>180202</if_sid>
    <decoded_as>json</decoded_as>
    <field name="aws.errorCode">^AccessDenied$</field>
    <options>no_full_log</options>
    <description>AWS Cloudtrail: $(aws.eventSource) - $(aws.eventName). Error: $(aws.errorCode).</description>
    <group>amazon-error,aws_cloudtrail,gdpr_IV_35.7.d,hipaa_164.312.b,nist_800_53_AU.6,pci_dss_10.6.1,tsc_CC7.2,tsc_CC7.3,</group>
  </rule>
  
  <rule id="180204" level="12">
    <if_sid>180202</if_sid>
    <decoded_as>json</decoded_as>
    <field name="aws.eventName">^AssumeRole$|^CreateUser$|^DeleteUser$</field>
    <options>no_full_log</options>
    <description>AWS CloudTrail: Unauthorized access or user modification detected.</description>
  </rule>
  
  <!-- Rule for S3 - put or remove policy -->
  <rule id="180205" level="12">
    <if_sid>180202</if_sid>
    <decoded_as>json</decoded_as>
    <field name="aws.eventName">^PutBucketPolicy$|^DeleteBucketPolicy$</field>
    <options>no_full_log</options>
    <description>AWS CloudTrail: S3 bucket policy changed. Bucket - $(aws.requestParameters.bucketName)</description>
  </rule>
  
  <!-- Rule for CloudTrail log deletion -->
  <rule id="180206" level="12">
    <if_sid>180202</if_sid>
    <decoded_as>json</decoded_as>
    <field name="aws.eventName">^DeleteTrail$</field>
    <options>no_full_log</options>
    <description>AWS CloudTrail: CloudTrail logs deleted.</description>
  </rule>
  
  <!-- Rule for instance creation burst -->
  <rule id="180207" level="12">
    <if_sid>180202</if_sid>
    <decoded_as>json</decoded_as>
    <field name="aws.eventName">^RunInstances$</field>
    <options>no_full_log</options>
    <description>AWS CloudTrail: Sudden burst of instance creation detected: $(aws.sourceIPAddress)</description>
  </rule>
  
  <!-- Rule for disabled logging -->
  <rule id="180208" level="12">
    <if_sid>180202</if_sid>
    <decoded_as>json</decoded_as>
    <field name="aws.eventName">^StopLogging$|^DeleteBucketLogging$</field>
    <options>no_full_log</options>
    <description>AWS CloudTrail: Logging disabled for a resource - $(aws.eventSource)</description>
  </rule>
  
  <!-- VPC Flow -->
  <!-- Documentation: https://docs.aws.amazon.com/AmazonVPC/latest/UserGuide/flow-logs.html -->
  <rule id="180209" level="0">
    <if_sid>180200</if_sid>
    <field name="aws.source">vpc</field>
    <options>no_full_log</options>
    <description>AWS VPC flow alert.</description>
    <group>aws_vpcflow,</group>
  </rule>

  <rule id="180210" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.action">ACCEPT</field>
    <options>no_full_log</options>
    <description>AWS VPC Flow: [$(aws.action)] - Interface: $(aws.interface_id) - Protocol: $(aws.protocol).</description>
    <group>aws_vpcflow,</group>
  </rule>

  <rule id="180211" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.action">REJECT</field>
    <options>no_full_log</options>
    <description>AWS VPC Flow: [$(aws.action)] - Interface: $(aws.interface_id) - Protocol: $(aws.protocol).</description>
    <group>aws_vpcflow,</group>
  </rule>
  
  <!--Security Groups -->
  
  <rule id="180212" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">^AuthorizeSecurityGroupIngress$</field>
    <field name="aws.requestParameters.ipPermissions.items.cidrIp">0.0.0.0/0</field>
    <description>Overly permissive Security Group rule added: $(aws.requestParameters.ipPermissions.items)</description>
    <options>no_full_log</options>
    <group>aws_sg,</group>
  </rule>
  
  <rule id="180213" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">^AuthorizeSecurityGroup$|^RevokeSecurityGroup$|^ModifySecurityGroup$</field>
    <field name="aws.requestParameters.groupName">^default$</field>
    <description>Modification to DEFAULT Security Group detected: $(aws.eventName)</description>
    <options>no_full_log</options>
    <group>aws_sg,</group>
  </rule>
  
  <rule id="180214" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">AuthorizeSecurityGroupIngress</field>
    <field name="aws.requestParameters.ipPermissions.items.fromPort">^22$|^3389$|^3306$</field>
    <field name="aws.requestParameters.ipPermissions.items.cidrIp">0.0.0.0/0</field>
    <description>High-risk port opened publicly: $(aws.requestParameters.ipPermissions.items.fromPort)</description>
    <options>no_full_log</options>
  </rule>
  
    <!--Rule 4: Network ACL Policy Changes -->
  <rule id="180215" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">^CreateNetworkAclEntry$|^ReplaceNetworkAclEntry$|^DeleteNetworkAclEntry$</field>
    <description>Network ACL policy modified: $(aws.eventName)</description>
    <options>no_full_log</options>
  </rule>
  
  <rule id="180216" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">AuthorizeSecurityGroupIngress</field>
    <field name="aws.requestParameters.ipPermissions.items.cidrIpv6">::/0</field>
    <description>Public IPv6 access granted: $(aws.requestParameters.ipPermissions.items)</description>
    <options>no_full_log</options>
  </rule>
  
  <!-- AWS Config -->
  <!-- Documentation: https://docs.aws.amazon.com/config/latest/developerguide/WhatIsConfig.html -->
  <rule id="180217" level="0">
    <if_sid>180200</if_sid>
    <field name="aws.source">config</field>
    <options>no_full_log</options>
    <description>AWS config alert.</description>
    <group>aws_config,</group>
  </rule>

  <!-- ConfigHistory vs ConfigSnapshot -->
  <rule id="180218" level="0">
    <if_sid>180217</if_sid>
    <field name="aws.log_info.log_file">\.+ConfigHistory</field>
    <options>no_full_log</options>
    <description>AWS config - history.</description>
    <group>aws_config,aws_config_history,</group>
  </rule>

  <rule id="180219" level="0">
    <if_sid>180217</if_sid>
    <field name="aws.log_info.log_file">\.+ConfigSnapshot</field>
    <options>no_full_log</options>
    <description>AWS config - snapshot.</description>
    <group>aws_config,aws_config_snapshot,</group>
  </rule>

  <!-- AWS Config -->
  <!-- Config Snapshot -->
  <rule id="180220" level="3">
    <if_sid>180219</if_sid>
    <options>no_full_log</options>
    <description>AWS config - snapshot [$(aws.awsAccountId) $(aws.awsRegion)] [$(aws.resourceType)]: $(aws.resourceId) ($(aws.configurationItemStatus)).</description>
    <group>aws_config,aws_config_snapshot,</group>
  </rule>

  <rule id="180221" level="6">
    <if_sid>180220</if_sid>
    <field name="aws.configuration.complianceType">NON_COMPLIANT</field>
    <field name="aws.configuration.configRuleList.configRuleName">encrypted-volumes</field>
    <options>no_full_log</options>
    <description>AWS config - snapshot compliance [$(aws.awsAccountId) $(aws.awsRegion)] [$(aws.resourceType)] [$(aws.configuration.configRuleList.configRuleName)]: $(aws.resourceId) ($(aws.configurationItemStatus)) $(aws.configuration.complianceType).</description>
    <group>aws_config,aws_config_snapshot,aws_config_snapshot_compliance,</group>
  </rule>
  
  <rule id="180222" level="6">
    <if_sid>180220</if_sid>
    <field name="aws.configuration.complianceType">NON_COMPLIANT</field>
    <field name="aws.configuration.configRuleList.configRuleName">rds-instance-public-access-check</field>
    <options>no_full_log</options>
    <description>AWS config - snapshot compliance [$(aws.awsAccountId) $(aws.awsRegion)] [$(aws.resourceType)] [$(aws.configuration.configRuleList.configRuleName)]: $(aws.resourceId) ($(aws.configurationItemStatus)) $(aws.configuration.complianceType).</description>
    <group>aws_config,aws_config_snapshot,aws_config_snapshot_compliance,</group>
  </rule>
  
  <rule id="180223" level="6">
    <if_sid>180220</if_sid>
    <field name="aws.configuration.complianceType">NON_COMPLIANT</field>
    <field name="aws.configuration.configRuleList.configRuleName">iam-user-mfa-enabled</field>
    <options>no_full_log</options>
    <description>AWS config - snapshot compliance [$(aws.awsAccountId) $(aws.awsRegion)] [$(aws.resourceType)] [$(aws.configuration.configRuleList.configRuleName)]: $(aws.resourceId) ($(aws.configurationItemStatus)) $(aws.configuration.complianceType).</description>
    <group>aws_config,aws_config_snapshot,aws_config_snapshot_compliance,</group>
  </rule>
  
  <!--IAM -->
   <rule id="180224" level="6">
    <if_sid>180220</if_sid>
    <field name="aws.configuration.complianceType">NON_COMPLIANT</field>
    <field name="aws.configuration.configRuleList.configRuleName">access-keys-rotated|iam-user-unused-credentials-check</field>
    <options>no_full_log</options>
    <description>AWS config - snapshot compliance [$(aws.awsAccountId) $(aws.awsRegion)] [$(aws.resourceType)] [$(aws.configuration.configRuleList.configRuleName)]: $(aws.resourceId) ($(aws.configurationItemStatus)) $(aws.configuration.complianceType).</description>
    <group>aws_config,aws_config_snapshot,aws_config_snapshot_compliance,</group>
  </rule>
  
  <rule id="180225" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">^DeactivateMFADevice$</field>
    <description>MFA device deactivated for an IAM user.</description>
    <options>no_full_log</options>
  </rule>
  
  <rule id="180226" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">^ConsoleLogin$</field> 
    <field name="aws.additionalEventData.MFAUsed">No</field>
    <description>Console login without MFA detected</description>
    <options>no_full_log</options>
  </rule>
  
  <rule id="180227" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">^CreateAccessKey$|^UpdateAccessKey$|^DeleteAccessKey$|^CreateAssumeRolePolicy$|^UpdateAssumeRolePolicy$|^DeleteAssumeRolePolicy$</field> 
    <description>Unexpected IAM role/access key modification detected.</description>
    <options>no_full_log</options>
  </rule>
  
  <rule id="180228" level="12">
    <if_sid>180200</if_sid>
    <field name="aws.eventName">^PutRolePolicy$|^PutUserPolicy$|^PutGroupPolicy$</field>
    <regex type="pcre2">"Action":\s*"\\*"|"Resource":\s*"\\*"</regex>
    <description>Unexpected IAM role/access key modification detected.</description>
    <options>no_full_log</options>
  </rule>
  
  <!-- AWS ELB -->
  <rule id="180229" level="0">
    <if_sid>180200</if_sid>
    <field name="aws.source">alb</field>
    <options>no_full_log</options>
    <description>AWS ALB alert.</description>
    <group>aws_alb,</group>
  </rule>

  <rule id="180230" level="3">
    <if_sid>180229</if_sid>
    <field name="aws.elb_status_code">^5\d{2}$</field>
    <options>no_full_log</options>
    <description>AWS ALB: Status error: $(data.aws.error_reason) - $(aws.action_executed) [ELB: $(aws.elb)].</description>
    <group>aws_alb,</group>
  </rule>

  <rule id="180231" level="5">
    <if_sid>180229</if_sid>
    <field name="aws.elb_status_code">^4\d{2}$</field>
    <options>no_full_log</options>
    <description>AWS ALB: Status error: $(data.aws.error_reason) - $(aws.action_executed) - $(aws.user_agent) [ELB: $(aws.elb)].</description>
    <group>aws_alb,</group>
  </rule>
  
  <!--AWS WAF-->
  <rule id="180232" level="0">
    <if_sid>180200</if_sid>
    <field name="aws.source">waf</field>
    <options>no_full_log</options>
    <description>AWS WAF alert.</description>
    <group>aws_waf,</group>
  </rule>

  <rule id="180233" level="0">
    <if_sid>180232</if_sid>
    <field name="aws.action">ALLOW</field>
    <options>no_full_log</options>
    <description>AWS WAF - Allowed request.</description>
    <group>aws_waf,aws_waf_allow,</group>
  </rule>

  <rule id="180234" level="3">
    <if_sid>180232</if_sid>
    <field name="aws.action">BLOCK</field>
    <options>no_full_log</options>
    <description>AWS WAF - Blocked request.</description>
    <group>aws_waf,aws_waf_block,</group>
  </rule>

  <rule id="180235" level="10" frequency="8" timeframe="120" ignore="60">
    <if_matched_sid>180234</if_matched_sid>
    <same_field>aws.httpRequest.clientIp</same_field>
    <options>no_full_log</options>
    <description>AWS WAF - Multiple blocked requests.</description>
  </rule>
  
     <rule id="180236" level="12">
        <if_sid>180220</if_sid>
        <field name="aws.eventName">^CreateWebACL$|^UpdateWebACL$|^DeleteWebACL$</field>
        <match>^!(group:allowed_waf_admins)</match> <!-- Customize allowed user/group -->
      <description>Unauthorized WAF rule change detected.</description>
    </rule>
    
    <rule id="180237" level="12">
    <if_sid>180220</if_sid>
      <field name="aws.eventName">^CreateLoadBalancer$|^ModifyLoadBalancer$|^DeleteLoadBalancer$</field>
      <description>Unauthorized ELB configuration change detected.</description>
    </rule>
    
    <rule id="180238" level="7">
        <if_sid>180220</if_sid>
        <field name="aws.eventName">^PutBucketPolicy$|^PutBucketAcl$</field>
        <description>S3 Bucket policy or ACL modified.</description>
        <options>no_full_log</options>
  </rule>
  
  <!--<rule id="180239" level="7">-->
  <!--  <if_sid>180220</if_sid>-->
  <!--  <field name="aws.eventName">^PutObject$|^DeleteObject$</field>-->
  <!--  <description>Unexpected data upload or deletion in S3 bucket.</description>-->
  <!--  <options>no_full_log</options>-->
  <!--</rule>-->
  
  <rule id="180240" level="10">
    <if_sid>180220</if_sid>
    <field name="aws.sourceIPAddress">!^192\.168\.|!^10\.</field> <!-- Example: Exclude internal IPs -->
    <description>S3 bucket accessed from an unusual IP address.</description>
    <options>no_full_log</options>
  </rule>

  <rule id="180241" level="10">
    <if_sid>180220</if_sid>
    <field name="aws.awsRegion">!us-east-1|!ap-south-1</field>
    <description>S3 bucket accessed from an unusual region.</description>
    <options>no_full_log</options>
  </rule>
  
  <!--  <rule id="180242" level="5">-->
  <!--  <decoded_as>aws_lambda_logs</decoded_as>-->
  <!--  <description>AWS Lambda: Unusual execution frequency detected.</description>-->
  <!--  <field name="aws.lambda.function_name">.*</field>-->
  <!--  <field name="aws.lambda.invocation_count" type="osregex">^\d{2,}$</field> -->
  <!--  <group>aws_lambda</group>-->
  <!--</rule>-->

<!--  <rule id="180243" level="7">-->
<!--    <decoded_as>aws_lambda_logs</decoded_as>-->
<!--    <description>AWS Lambda: Unusual execution duration detected.</description>-->
<!--    <field name="aws.lambda.function_name">.*</field>-->
<!--    <field name="aws.lambda.duration" type="osregex">^\d{4,}$</field> -->
<!--    <group>aws_lambda</group>-->
<!--  </rule>-->
  
<!--  <rule id="180244" level="10">-->
<!--    <decoded_as>aws_lambda_logs</decoded_as>-->
<!--    <description>AWS Lambda: Invocation failure or error detected.</description>-->
<!--    <field name="aws.lambda.function_name">.*</field>-->
<!--    <field name="aws.lambda.status">FAILED|ERROR</field>-->
<!--    <group>aws_lambda</group>-->
<!--  </rule>-->
  
<!--  <rule id="180245" level="10">-->
<!--  <decoded_as>aws_lambda_logs2</decoded_as>-->
<!--  <description>AWS Lambda: Invocation failure or error detected.</description>-->
<!--  <field name="aws.lambda.function_name">.*</field>-->
<!--  <field name="aws.lambda.status">FAILED|ERROR</field>-->
<!--  <group>aws_lambda</group>-->
<!--</rule>-->

<rule id="180246" level="8">
  <decoded_as>aws_lambda_config</decoded_as>
  <description>AWS Lambda: Configuration change detected.</description>
  <field name="aws.lambda.function_name">.*</field>
  <field name="aws.lambda.event_type">UpdateFunctionConfiguration</field>
  <group>aws_lambda</group>
</rule>

<rule id="180247" level="10">
    <decoded_as>aws_route53_logs</decoded_as>
    <description>AWS Route 53: Unauthorized DNS record change detected.</description>
    <field name="aws.route53.event_type">ChangeResourceRecordSets</field>
    <field name="aws.route53.change_status">INSYNC</field>
    <field name="aws.route53.user_identity" type="osregex">^(?!authorized_user).+$</field> <!-- Replace with authorized user/role -->
    <group>aws_route53</group>
  </rule>
  
  <rule id="180248" level="12">
  <decoded_as>dns_query_logs</decoded_as>
  <description>Suspicious DNS query detected (possible C2 server).</description>
  <group>dns_monitoring</group>
</rule>

    <rule id="180249" level="8">
  <decoded_as>aws_route53_logs2</decoded_as>
  <description>AWS Route 53: Hosted zone configuration change detected.</description>
  <field name="aws.route53.event_type">UpdateHostedZone</field>
  <group>aws_route53</group>
</rule>

 <!--<rule id="180250" level="5">-->
 <!--   <decoded_as>json</decoded_as>-->
 <!--   <field name="metric_name">CPUUtilization</field>-->
 <!--   <field name="value" type="float">>80</field>-->
 <!--   <description>High CPU usage detected on instance.</description>-->
 <!-- </rule>-->
 <!-- <rule id="180251" level="5">-->
 <!--   <decoded_as>json</decoded_as>-->
 <!--   <field name="metric_name">MemoryUtilization</field>-->
 <!--   <field name="value" type="float">>85</field>-->
 <!--   <description>High Memory usage detected on instance.</description>-->
 <!-- </rule>-->
  
  <!--<rule id="180252" level="7">-->
  <!--  <decoded_as>json</decoded_as>-->
  <!--  <field name="service">Lambda</field>-->
  <!--  <field name="metric_name">Errors</field>-->
  <!--  <field name="value" type="float">>5</field>-->
  <!--  <description>High error rate detected in AWS Lambda function.</description>-->
  <!--</rule>-->
  <!--<rule id="180253" level="7">-->
  <!--  <decoded_as>json</decoded_as>-->
  <!--  <field name="service">APIGateway</field>-->
  <!--  <field name="metric_name">5XXError</field>-->
  <!--  <field name="value" type="float">>3</field>-->
  <!--  <description>High 5XX error rate in API Gateway detected.</description>-->
  <!--</rule>-->
  
  <!--<rule id="180254" level="6">-->
  <!--  <decoded_as>json</decoded_as>-->
  <!--  <field name="aws.service">AutoScaling</field>-->
  <!--  <field name="aws.event">EC2 Instance Launched</field>-->
  <!--  <description>Unusual auto-scaling activity detected.</description>-->
  <!--</rule>-->
  
  <!--<rule id="180255" level="5">-->
  <!--  <decoded_as>json</decoded_as>-->
  <!--  <field name="aws.data.type">resource_metrics</field>-->
  <!--  <description>Resource utilization anomaly: $(data.cpu_usage_percent)% CPU / $(data.memory_usage_percent)% RAM          </description>-->
  <!--</rule>-->
  
  <!--<rule id="180256" level="3">-->
  <!--  <field name="aws.eventSource">lambda.amazonaws.com</field>-->
  <!--  <description>AWS Lambda Event: $(eventName)</description>-->
  <!--</rule>-->
  
  <!--<rule id="180257" level="12">-->
  <!--  <if_sid>180256</if_sid>-->
  <!--  <match>errorMessage|TimedOut</match>-->
  <!--  <description>High severity Lambda error: $(requestParameters.functionName)</description>-->
  <!--</rule>-->
  
  <!--<rule id="180257" level="8">-->
  <!--    <field name="aws.eventName">DescribeScalingActivities</field>-->
  <!--  <description>Auto-scaling activity detected: $(autoScalingGroupName)</description>-->
  <!--  <group>aws_autoscaling</group>-->
  <!--</rule>-->

  
</group>

<group name="syscheck,">
  <rule id="190000" level="7">
    <category>ossec</category>
    <decoded_as>ossec</decoded_as>
    <description>File modified in /var/www/html</description>
    <field name="file">/var/www/html</field>
    <options>no_full_log</options>
  </rule>

  <rule id="190001" level="7">
    <category>ossec</category>
    <decoded_as>ossec</decoded_as>
    <description>Critical configuration file modified</description>
    <field name="file">/etc/nats/*</field>
    <options>no_full_log</options>
  </rule>

  <rule id="190002" level="7">
    <category>ossec</category>
    <decoded_as>ossec</decoded_as>
    <description>Cron job or scheduled task modified</description>
    <field name="file">/etc/crontab|/etc/cron.d|/var/spool/cron</field>
    <options>no_full_log</options>
  </rule>
</group>
