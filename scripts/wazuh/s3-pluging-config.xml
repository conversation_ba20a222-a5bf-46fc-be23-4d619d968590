<wodle name="aws-s3">
  <disabled>no</disabled>
  <interval>5m</interval>
  <run_on_start>yes</run_on_start>
  <skip_on_error>yes</skip_on_error>
  <bucket type="cloudtrail">
  	<name>cloudtrail-prod-trail</name>
        <regions>ap-south-1</regions>
        <only_logs_after>2025-JAN-19</only_logs_after>
        <path>prod</path>
        <aws_organization_id>o-sezxwpleyg</aws_organization_id>
  </bucket>
  <bucket type="vpcflow">
        <name>alb-cloudfront-logs-bucket</name>
        <regions>ap-south-1</regions>
        <only_logs_after>2025-JAN-20</only_logs_after>
        <path>vpc/flowlogs</path>
  </bucket>
  <bucket type="alb">
        <name>alb-cloudfront-logs-bucket</name>
        <regions>ap-south-1</regions>
        <only_logs_after>2025-JAN-23</only_logs_after>
        <path>alb/ravenclaw/access-logs</path>
  </bucket>
  <bucket type="alb">
        <name>alb-cloudfront-logs-bucket</name>
        <regions>ap-south-1</regions>
        <only_logs_after>2025-JAN-23</only_logs_after>
        <path>alb/ravenclaw/connection-logs</path>
  </bucket>
  <bucket type="alb">
        <name>alb-cloudfront-logs-bucket</name>
        <regions>ap-south-1</regions>
        <only_logs_after>2025-JAN-23</only_logs_after>
        <path>alb/keycloak/access-logs</path>
  </bucket>
  <bucket type="alb">
        <name>alb-cloudfront-logs-bucket</name>
        <regions>ap-south-1</regions>
        <only_logs_after>2025-JAN-23</only_logs_after>
        <path>alb/keycloak/connection-logs</path>
  </bucket>
  <bucket type="alb">
        <name>alb-cloudfront-logs-bucket</name>
        <regions>ap-south-1</regions>
        <only_logs_after>2025-JAN-23</only_logs_after>
        <path>alb/internal/access-logs</path>
  </bucket>
  <bucket type="alb">
        <name>alb-cloudfront-logs-bucket</name>
        <regions>ap-south-1</regions>
        <only_logs_after>2025-JAN-23</only_logs_after>
        <path>alb/internal/connection-logs</path>
  </bucket>
  <bucket type="config">
      <name>771151923073-ap-south-1-config</name>
      <regions>ap-south-1</regions>
      <only_logs_after>2025-JAN-20</only_logs_after>
  </bucket>
  <service type="cloudwatchlogs">
    <aws_log_groups>
      /aws/cloudfront/assets/realtimelogs,
      /aws/cloudfront/assets/stdlogs,
      /aws/cloudfront/webui/realtimelogs,
      /aws/cloudfront/webui/stdlogs,
      /aws/lambda/SnsToGoogleChat,
      /aws/lambda/alb-access-logs-to-cloudwatch,
      /aws/lambda/assets-cloudfront-realtime-logs,
      /aws/lambda/assets-cloudfront-stdlogs-function,
      /aws/lambda/internal-alb-access-logs-to-cloudwatch,
      /aws/lambda/internal-alb-connection-logs-to-cloudwatch,
      /aws/lambda/keycloak-alb-access-logs-to-cloudwatch,
      /aws/lambda/keycloak-alb-connection-logs-to-cloudwatch,
      /aws/lambda/ravenclaw-alb-connection-logs-to-cloudwatch,
      /aws/lambda/sendAlert,
      /aws/lambda/send_alert,
      /aws/lambda/vpc-flow-logs-func,
      /aws/lambda/webui-cloudfront-realtime-logs,
      /aws/lambda/webui-cloudfront-stdlogs-function,
      /aws/rds/instance/keycloak-read-replica-1/postgresql,
      /aws/rds/instance/keycloak/postgresql,
      /aws/rds/instance/ravenclaw-read-replica-1/postgresql,
      /aws/rds/instance/ravenclaw/postgresql,
      /aws/rds/instance/ravenclaw1/postgresql,
      /aws/securityhub/findings,
      /ec2/nats,
      /ec2/natsNode1,
      /ec2/natsNode2,
      /ec2/natsNode3,
      /ec2/natsNode4,
      /ecs/bff,
      /ecs/gateway,
      /ecs/gateway/subcommand,
      /ecs/gotenberg,
      /ecs/ingestion,
      /ecs/ml-inference,
      /ecs/remediator,
      /ecs/setup,
      /ecs/swagger,
      /ecs/ti-go-service,
      RDSOSMetrics,
      annotation_pipeline_cron_1,
      annotations-cron-logs,
      aws-waf-logs-alb-acl,
      keycloak-ecs-logs
    </aws_log_groups>
    <regions>ap-south-1</regions>
  </service>
  <service type="cloudwatchlogs">
    <aws_log_groups>
      aws-waf-logs-cloudfront-acl
    </aws_log_groups>
    <regions>us-east-1</regions>
  </service>
 </wodle>
