<!-- Local Decoders -->

<!-- Modify it at your will. -->
<!-- Copyright (C) 2015, Wazuh Inc. -->

<!--
  - Allowed static fields:
  - location   - where the log came from (only on FTS)
  - srcuser    - extracts the source username
  - dstuser    - extracts the destination (target) username
  - user       - an alias to dstuser (only one of the two can be used)
  - srcip      - source ip
  - dstip      - dst ip
  - srcport    - source port
  - dstport    - destination port
  - protocol   - protocol
  - id         - event id
  - url        - url of the event
  - action     - event action (deny, drop, accept, etc)
  - status     - event status (success, failure, etc)
  - extra_data - Any extra data
-->

<decoder name="local_decoder_example">
    <program_name>local_decoder_example</program_name>
</decoder>

<decoder name="aws_lambda_logs">
  <prematch>^AWS Lambda</prematch>
  <regex>Function: (\S+), Invocation Count: (\d+), Duration: (\d+) ms</regex>
  <order>function_name, invocation_count, duration</order>
</decoder>

<decoder name="aws_lambda_logs2">
  <prematch>^AWS Lambda</prematch>
  <regex>Function: (\S+), Invocation Count: (\d+), Duration: (\d+) ms, Status: (\S+)</regex>
  <order>function_name, invocation_count, duration, status</order>
</decoder>

<decoder name="aws_lambda_config">
  <prematch>^AWS Lambda Config</prematch>
  <regex>Function: (\S+), Event Type: (\S+)</regex>
  <order>function_name, event_type</order>
</decoder>

<decoder name="aws_route53_logs">
  <prematch>^AWS Route 53</prematch>
  <regex>Event Type: (\S+), Change Status: (\S+), User Identity: (\S+)</regex>
  <order>event_type, change_status, user_identity</order>
</decoder>

<decoder name="dns_query_logs">
  <prematch>^DNS Query</prematch>
  <regex>Query: (\S+), Type: (\S+), Client: (\S+)</regex>
  <order>query, type, client</order>
</decoder>

<decoder name="aws_route53_logs2">
  <prematch>^AWS Route 53</prematch>
  <regex>Event Type: (\S+), Change Status: (\S+), User Identity: (\S+)</regex>
  <order>event_type, change_status, user_identity</order>
</decoder>
