# Run `Analytics Exporter Script`

- A python script `main.py` is created to update the `analytics-exporter` service with latest task definition with latest arguments that are being passed
- `script.sh` contains a bash function which creates a virtual environment, run the python script and then deactivates the virtual environment
- All the related IAM permissions have been provided to Bastion EC2 instance
- An alias has been created to run the exporter with minimal effort

## How to run

- If running from bastion instance, always use `dev` infra bastion for running tasks in both dev and prod infra.

- Following below alias command to run

```bash
analytics_exporter <environment> <flags_and_values>
```

### Supported Flags

The script supports the following flags:
- `--org-id` - Organization ID
- `--date-start` - Start date for data export
- `--date-end` - End date for data export

### Run in dev

- To run in dev infra with specific parameters:

```bash
analytics_exporter dev --org-id <org_id_value> --date-start <start_date> --date-end <end_date>
```

### Run in prod
- To run in prod infra with specific parameters:

```bash
analytics_exporter prod --org-id <org_id_value> --date-start <start_date> --date-end <end_date>
```

### Example Usage

```bash
# Export data for organization 123 from Jan 1 to Jan 31, 2024
analytics_exporter dev --org-id 123 --date-start 2024-01-01 --date-end 2024-01-31
```

### Run and make changes locally

1. Create a virtual environment
```bash
cd ops/scripts/analytics_exporter_run_script
python3 -m venv venv
source venv/bin/activate
```

- To deactivate the environment
```bash
deactivate
```

2. install all the dependencies
```bash
pip3 install -r requirements.txt
```

3. Load the `script.sh` into `.bashrc` or `.zshrc`

> NOTE: Before moving forward, edit the `script.sh` and add correct path to your main.py and virtual env respectively


```bash
# make script.sh executable
chmod +x script.sh

# add below lines at the end of your `.bashrc` or `.zshrc` file
export RUN_ANALYTICS_EXPORTER_SCRIPT_PATH=<path_to_script_folder>/scripts/analytics_exporter_run_script
source <absolute_path>/script.sh
alias analytics_exporter=run_analytics_exporter
```

## Configuration

The script uses a `values.yaml` configuration file that should contain environment-specific settings for:
- AWS region
- ECS cluster name
- VPC and subnet configurations
- Security group settings

## What the script does

1. **Load Configuration**: Reads environment-specific settings from `values.yaml`
2. **Update Task Definition**: Creates a new revision of the analytics-exporter task definition with the provided command arguments
3. **Update ECS Service**: Updates the analytics-exporter ECS service to use the new task definition revision
4. **Force Deployment**: Triggers a new deployment to ensure running tasks use the updated configuration

## Error Handling

The script validates:
- Environment parameter (must be 'dev' or 'prod')
- Required flags (must be one of: --org-id, --date-start, --date-end)
- Proper argument structure

If validation fails, the script will exit with an error message indicating the correct usage.
