import boto3
import sys
import yaml
import os
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(name)s -  %(message)s"
)
logger = logging.getLogger(__name__)


def load_config(environment):
    SCRIPT_PATH = os.environ["RUN_ANALYTICS_EXPORTER_SCRIPT_PATH"]
    with open(f"{SCRIPT_PATH}/values.yaml", "r") as file:
        config = yaml.safe_load(file)
    return config[environment]


def get_latest_revision(task_definition_name, region):
    ecs_client = boto3.client("ecs", region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response["taskDefinition"]["revision"]


def get_container_name(task_definition_name, region):
    ecs_client = boto3.client("ecs", region_name=region)
    response = ecs_client.describe_task_definition(taskDefinition=task_definition_name)
    return response["taskDefinition"]["containerDefinitions"][0]["name"]


def get_subnet_ids(vpc_name, region, subnet_names):
    ec2_client = boto3.client("ec2", region_name=region)
    response = ec2_client.describe_vpcs(
        Filters=[{"Name": "tag:Name", "Values": [vpc_name]}]
    )

    if not response["Vpcs"]:
        raise ValueError(f"VPC with name {vpc_name} not found")

    vpc_id = response["Vpcs"][0]["VpcId"]

    response = ec2_client.describe_subnets(
        Filters=[
            {"Name": "vpc-id", "Values": [vpc_id]},
        ]
    )
    subnet_ids = [subnet["SubnetId"] for subnet in response["Subnets"]]

    private_subnet_ids = []
    for subnet in response["Subnets"]:
        subnet_name = next(
            (tag["Value"] for tag in subnet.get("Tags", []) if tag["Key"] == "Name"),
            None,
        )
        if subnet_name in subnet_names:
            private_subnet_ids.append(subnet["SubnetId"])

    return private_subnet_ids


def get_security_group_ids(vpc_name, region, security_group_names):
    ec2_client = boto3.client("ec2", region_name=region)

    response = ec2_client.describe_vpcs(
        Filters=[{"Name": "tag:Name", "Values": [vpc_name]}]
    )
    if not response["Vpcs"]:
        raise ValueError(f"VPC with name {vpc_name} not found")
    vpc_id = response["Vpcs"][0]["VpcId"]

    response = ec2_client.describe_security_groups(
        Filters=[
            {"Name": "vpc-id", "Values": [vpc_id]},
            {"Name": "group-name", "Values": security_group_names},
        ]
    )

    security_group_ids = [sg["GroupId"] for sg in response["SecurityGroups"]]

    if len(security_group_ids) != len(security_group_names):
        missing_groups = set(security_group_names) - set(
            sg["GroupName"] for sg in response["SecurityGroups"]
        )
        logging.error(
            f"Warning: Not all security groups were found. Missing: {missing_groups}"
        )

    return security_group_ids


def run_task(environment, commandList):
    config = load_config(environment)
    logging.info("config loaded")
    vpc_name = config["vpc_name"]
    region = config["region"]
    cluster_name = config["cluster_name"]
    subnet_names = config["private_subnet_names"]
    security_group_names = config["security_group_names"]

    ecs_client = boto3.client("ecs", region_name=region)
    latest_revision = get_latest_revision("analytics-exporter", region)
    container_name = get_container_name("analytics-exporter", region)
    subnet_ids = get_subnet_ids(vpc_name, region, subnet_names)
    security_group_ids = get_security_group_ids(vpc_name, region, security_group_names)
    response = ecs_client.run_task(
        cluster=cluster_name,
        taskDefinition=f"analytics-exporter:{latest_revision}",
        count=1,
        launchType="FARGATE",
        networkConfiguration={
            "awsvpcConfiguration": {
                "subnets": subnet_ids,
                "securityGroups": security_group_ids,
                "assignPublicIp": "DISABLED",
            }
        },
        overrides={
            "containerOverrides": [{"name": container_name, "command": commandList}]
        },
        tags=[
            {"key": "Name", "value": f"ravenclaw-analytics-exporter"},
            {"key": "Product", "value": "ravenclaw"},
            {"key": "Team", "value": "engineering"},
            {"key": "Application", "value": "go"},
            {"key": "Environment", "value": environment},
        ],
    )

    logging.info("Task started:", response)


if __name__ == "__main__":

    commandList = ["exporter"]

    environment = sys.argv[1].lower()
    if environment not in ["dev", "prod"]:
        logging.error("Invalid environment. Use 'dev' or 'prod'.")
        sys.exit(1)
        
    commandList.append(environment)

    flags = ["--org-id", "--date-start", "--date-end"]
    arg_idx = 2

    for i in range(len(flags)):
        flag = sys.argv[arg_idx]
        if flag not in flags:
            logging.error(
            f"Usage: Flags for create subcommand should be any of {flags}")
            sys.exit(1)
        commandList.append(flag)
        flags.remove(flag)

        flagValue = sys.argv[arg_idx + 1]
        commandList.append(flagValue)
        arg_idx += 2

    print(commandList)
    run_task(environment, commandList)