name: Terraform Prod - Code Scanning

on:
  schedule:
    - cron: "0 7 * * 1,4"

permissions:
  id-token: write
  contents: read

jobs:
  trivy-prod:
    name: trivy-scan
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Show - Trivy secret scanner in fs mode
        uses: aquasecurity/trivy-action@0.28.0
        id: show-trivy
        with:
          scan-type: "config"
          scan-ref: "terraform/prod"
          scanner: "vuln, secret, misconfig, license"
          format: "table"
          exit-code: 0
          vuln-type: "os, library"
        env:
          TRIVY_DB_REPOSITORY: public.ecr.aws/aquasecurity/trivy-db latest
          TRIVY_CHECKS_BUNDLE_REPOSITORY: public.ecr.aws/aquasecurity/trivy-checks latest

      - name: Save - Trivy secret scanner in fs mode
        uses: aquasecurity/trivy-action@0.28.0
        id: save-trivy
        with:
          scan-type: "config"
          scan-ref: "terraform/prod"
          scanner: "vuln, secret, misconfig, license"
          format: "json"
          exit-code: 0
          vuln-type: "os, library"
          output: "${COMMIT_HASH}.json"
          skip-setup-trivy: true
        env:
          COMMIT_HASH: ${{ github.sha }}
          TRIVY_DB_REPOSITORY: public.ecr.aws/aquasecurity/trivy-db latest
          TRIVY_CHECKS_BUNDLE_REPOSITORY: public.ecr.aws/aquasecurity/trivy-checks latest

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: ${{ secrets.AWS_S3_GITHUB_OIDC_ROLE }}
          aws-region: ap-south-1

      - name: Upload json file to S3
        if: always()
        env:
          COMMIT_HASH: ${{ github.sha }}
        run: |
          FILE_NAME="${COMMIT_HASH}-$(date +%Y%m%d%H%M%S).json"
          aws s3 cp ${COMMIT_HASH}.json s3://${{ vars.CODE_SCAN_FILES_S3_BUCKET }}/terraform/prod/$FILE_NAME

      - name: Notify on google chat
        env:
          CODESCAN_NOTIFICATION_GOOGLE_WEBHOOK: ${{ secrets.CODESCAN_NOTIFICATION_GOOGLE_WEBHOOK }}
        run: |
          WORKFLOW_STATUS="${{ job.status }}"
          ACTION_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          MESSAGE="*App:* Terraform (Prod)\n\n*See details:* ${ACTION_URL}"
              
          curl -X POST -H "Content-Type: application/json" \
          -d "{\"text\": \"${MESSAGE}\"}" \
          "${CODESCAN_NOTIFICATION_GOOGLE_WEBHOOK}"
