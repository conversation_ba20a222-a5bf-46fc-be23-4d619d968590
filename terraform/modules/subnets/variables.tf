variable "region" {
  type = string
}

variable "vpc_id" {
  type = string
}

variable "availability_zone" {
  type = string
}

variable "assign_ipv6_address_on_creation" {
  type    = bool
  default = false
}

variable "cidr_block" {
  type = string
}

variable "enable_dns64" {
  type    = bool
  default = false
}

variable "enable_resource_name_dns_a_record_on_launch" {
  type    = bool
  default = false
}

variable "enable_resource_name_dns_aaaa_record_on_launch" {
  type    = bool
  default = false
}

variable "ipv6_native" {
  type    = bool
  default = false
}

variable "map_public_ip_on_launch" {
  type    = bool
  default = false
}

variable "private_dns_hostname_type_on_launch" {
  type    = string
  default = "ip-name"
}

variable "tags" {
  type    = map(string)
  default = {}
}