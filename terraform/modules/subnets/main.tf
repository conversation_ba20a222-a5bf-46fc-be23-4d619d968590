resource "aws_subnet" "main" {
  assign_ipv6_address_on_creation                = var.assign_ipv6_address_on_creation
  cidr_block                                     = var.cidr_block
  enable_dns64                                   = var.enable_dns64
  enable_resource_name_dns_a_record_on_launch    = var.enable_resource_name_dns_a_record_on_launch
  enable_resource_name_dns_aaaa_record_on_launch = var.enable_resource_name_dns_aaaa_record_on_launch
  ipv6_native                                    = var.ipv6_native
  map_public_ip_on_launch                        = var.map_public_ip_on_launch
  private_dns_hostname_type_on_launch            = var.private_dns_hostname_type_on_launch
  vpc_id                                         = var.vpc_id
  availability_zone = var.availability_zone

  tags = var.tags
}
