variable "region" {
  type = string
}

variable "subnet_description" {
  type    = string
  default = ""
}

variable "subnet_name" {
  type = string
}

variable "subnet_ids" {
  type = list(string)
}

variable "at_rest_encryption_enabled" {
  type    = bool
  default = false
}

variable "replication_group_auto_minor_version_upgrade" {
  type    = bool
  default = true
}

variable "automatic_failover_enabled" {
  type    = bool
  default = false
}

variable "data_tiering_enabled" {
  type    = bool
  default = false
}

variable "description" {
  type    = string
  default = " "
}

variable "engine" {
  type    = string
  default = "redis"
}

variable "engine_version" {
  type    = number
  default = 7.1
}

variable "ip_discovery" {
  type    = string
  default = "ipv4"
}

variable "slow_log_destination" {
  type = string
}

variable "slow_log_destination_type" {
  type    = string
  default = "cloudwatch-logs"
}

variable "slow_log_format" {
  type    = string
  default = "json"
}

variable "slow_log_type" {
  type    = string
  default = "slow-log"
}

variable "engine_log_destination" {
  type = string
}

variable "engine_log_destination_type" {
  type    = string
  default = "cloudwatch-logs"
}

variable "engine_log_format" {
  type    = string
  default = "json"
}

variable "engine_log_type" {
  type    = string
  default = "engine-log"
}

variable "multi_az_enabled" {
  type    = bool
  default = false
}

variable "num_cache_clusters" {
  type    = number
  default = 2
}

variable "num_node_groups" {
  type    = number
  default = 1
}


variable "node_type" {
  type    = string
  default = "cache.t2.small"
}

variable "parameter_group_name" {
  type    = string
  default = "default.redis7"
}

variable "port" {
  type    = number
  default = 6379
}

variable "replicas_per_node_group" {
  type    = number
  default = 1
}

variable "replication_group_id" {
  type = string
}

variable "security_group_ids" {
  type = list(string)
}

variable "snapshot_retention_limit" {
  type    = number
  default = 1
}

variable "snapshot_window" {
  type    = string
  default = "07:30-08:30"
}

variable "replication_group_transit_encryption_enabled" {
  type    = bool
  default = false
}

variable "network_type" {
  type    = string
  default = "ipv4"
}

# variable "service_discovery_service_name" {
#   type = string
# }

# variable "namespace_id" {
#   type = string
# }

# variable "dns_records_ttl" {
#   type    = number
#   default = 10
# }

# variable "dns_records_type" {
#   type    = string
#   default = "A"
# }

# variable "routing_policy" {
#   type    = string
#   default = "MULTIVALUE"
# }

# variable "instance_id" {
#   type = string
# }

variable "subnet_group_tags" {
  type    = map(string)
  default = {}
}

variable "elasticache_tags" {
  type    = map(string)
  default = {}
}

# variable "sd_tags" {
#   type    = map(string)
#   default = {}
# }
