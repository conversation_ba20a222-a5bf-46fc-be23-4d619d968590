resource "aws_elasticache_subnet_group" "main" {
  description = var.subnet_description
  name        = var.subnet_name
  subnet_ids  = var.subnet_ids

  lifecycle {
    ignore_changes = [
      description
    ]
  }

  tags = var.subnet_group_tags
}

resource "aws_elasticache_replication_group" "main" {
  at_rest_encryption_enabled = var.at_rest_encryption_enabled
  auto_minor_version_upgrade = var.replication_group_auto_minor_version_upgrade
  automatic_failover_enabled = var.automatic_failover_enabled
  data_tiering_enabled       = var.data_tiering_enabled
  description                = var.description
  engine                     = var.engine
  engine_version             = var.engine_version
  ip_discovery               = var.ip_discovery

  log_delivery_configuration {
    destination      = var.slow_log_destination
    destination_type = var.slow_log_destination_type
    log_format       = var.slow_log_format
    log_type         = var.slow_log_type
  }

  log_delivery_configuration {
    destination      = var.engine_log_destination
    destination_type = var.engine_log_destination_type
    log_format       = var.engine_log_format
    log_type         = var.engine_log_type
  }

  multi_az_enabled = var.multi_az_enabled
  # num_cache_clusters         = var.num_cache_clusters
  network_type               = var.network_type
  node_type                  = var.node_type
  num_node_groups            = var.num_node_groups
  parameter_group_name       = var.parameter_group_name
  port                       = var.port
  replicas_per_node_group    = var.replicas_per_node_group
  replication_group_id       = var.replication_group_id
  security_group_ids         = var.security_group_ids
  snapshot_retention_limit   = var.snapshot_retention_limit
  snapshot_window            = var.snapshot_window
  subnet_group_name          = aws_elasticache_subnet_group.main.name
  transit_encryption_enabled = var.replication_group_transit_encryption_enabled

  tags = var.elasticache_tags
}

# data "dns_a_record_set" "ip" {
#   host = aws_elasticache_replication_group.main.primary_endpoint_address
# }

# resource "aws_service_discovery_service" "main" {
#   name = var.service_discovery_service_name

#   dns_config {
#     namespace_id = var.namespace_id

#     dns_records {
#       ttl  = var.dns_records_ttl
#       type = var.dns_records_type
#     }

#     routing_policy = var.routing_policy
#   }

#   tags = var.sd_tags
# }

# resource "aws_service_discovery_instance" "main" {
#   instance_id = var.instance_id
#   service_id  = aws_service_discovery_service.main.id

#   attributes = {
#     AWS_INSTANCE_IPV4 = data.dns_a_record_set.ip.addrs[0]
#   }
# }
