variable "region" {
  type = string
}

variable "associate_public_ip_address" {
  type    = bool
  default = false
}

variable "availability_zone" {
  type = string
}


variable "key_name" {
  type = string
}

variable "capacity_reservation_preference" {
  type    = string
  default = "open"
}

variable "cpu_credits" {
  type    = string
  default = "standard"
}

variable "disable_api_stop" {
  type    = bool
  default = false
}

variable "disable_api_termination" {
  type    = bool
  default = false
}

variable "ebs_optimized" {
  type    = bool
  default = false
}

variable "iam_instance_profile" {
  type    = string
  default = ""
}

variable "enclave_options_enabled" {
  type    = bool
  default = false
}

variable "get_password_data" {
  type    = bool
  default = false
}

variable "hibernation" {
  type    = bool
  default = false
}

variable "instance_initiated_shutdown_behavior" {
  type    = string
  default = "stop"
}

variable "instance_type" {
  type = string
}

variable "ipv6_address_count" {
  type    = number
  default = 0
}

variable "auto_recovery" {
  type    = string
  default = "default"
}

variable "http_endpoint" {
  type    = string
  default = "enabled"
}

variable "http_protocol_ipv6" {
  type    = string
  default = "disabled"
}

variable "http_put_response_hop_limit" {
  type    = number
  default = 2
}

variable "http_tokens" {
  type    = string
  default = "required"
}

variable "instance_metadata_tags" {
  type    = string
  default = "disabled"
}

variable "monitoring" {
  type    = bool
  default = false
}

variable "placement_partition_number" {
  type    = number
  default = 0
}

variable "enable_resource_name_dns_a_record" {
  type    = bool
  default = false
}

variable "enable_resource_name_dns_aaaa_record" {
  type    = bool
  default = false
}

variable "hostname_type" {
  type    = string
  default = "ip-name"
}

variable "root_block_device_delete_on_termination" {
  type    = bool
  default = true
}

variable "root_block_device_encrypted" {
  type    = bool
  default = false
}

variable "root_block_device_iops" {
  type    = number
  default = 3000
}

variable "root_block_device_throughput" {
  type    = number
  default = 125
}

variable "root_block_device_volume_size" {
  type    = number
  default = 10
}

variable "root_block_device_volume_type" {
  type    = string
  default = "gp3"
}

variable "source_dest_check" {
  type    = bool
  default = true
}

variable "subnet_id" {
  type = string
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "tenancy" {
  type    = string
  default = "default"
}

variable "vpc_security_group_ids" {
  type = list(string)
}

variable "user_data" {
  type    = string
  default = ""
}

variable "service_discovery_service_name" {
  type    = string
  default = ""
}

variable "namespace_id" {
  type    = string
  default = ""
}

variable "dns_records_ttl" {
  type    = number
  default = 10
}

variable "dns_records_type" {
  type    = string
  default = "A"
}

variable "routing_policy" {
  type    = string
  default = "MULTIVALUE"
}

variable "ami" {
  type    = string
  default = ""
}

variable "attach_external_disk" {
  type    = bool
  default = false
}

variable "external_volume_size" {
  type    = number
  default = 10
}

variable "external_volume_type" {
  type    = string
  default = "gp3"
}

variable "external_volume_encrypted" {
  type    = bool
  default = true
}

variable "external_volume_device_name" {
  type    = string
  default = "/dev/sdf"
}

variable "external_volume_iops" {
  type    = number
  default = 3000
}

variable "external_volume_throughput" {
  type    = number
  default = 125
}

variable "root_block_device_tags" {
  type    = map(string)
  default = {}
}
