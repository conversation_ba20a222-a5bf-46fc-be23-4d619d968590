data "aws_ami" "main" {
  most_recent = true
  owners      = ["amazon"]
  filter {
    name   = "architecture"
    values = ["x86_64"]
  }
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*"]
  }
}

resource "aws_instance" "main" {
  ami                         = var.ami == "" ? data.aws_ami.main.id : var.ami
  associate_public_ip_address = var.associate_public_ip_address
  availability_zone           = var.availability_zone
  key_name                    = var.key_name


  capacity_reservation_specification {
    capacity_reservation_preference = var.capacity_reservation_preference
  }

  credit_specification {
    cpu_credits = var.cpu_credits
  }

  disable_api_stop        = var.disable_api_stop
  disable_api_termination = var.disable_api_termination
  ebs_optimized           = var.ebs_optimized

  iam_instance_profile = var.iam_instance_profile == "" ? null : var.iam_instance_profile

  enclave_options {
    enabled = var.enclave_options_enabled
  }

  get_password_data                    = var.get_password_data
  hibernation                          = var.hibernation
  instance_initiated_shutdown_behavior = var.instance_initiated_shutdown_behavior
  instance_type                        = var.instance_type
  ipv6_address_count                   = var.ipv6_address_count

  maintenance_options {
    auto_recovery = var.auto_recovery
  }

  metadata_options {
    http_endpoint               = var.http_endpoint
    http_protocol_ipv6          = var.http_protocol_ipv6
    http_put_response_hop_limit = var.http_put_response_hop_limit
    http_tokens                 = var.http_tokens
    instance_metadata_tags      = var.instance_metadata_tags
  }

  monitoring                 = var.monitoring
  placement_partition_number = var.placement_partition_number

  private_dns_name_options {
    enable_resource_name_dns_a_record    = var.enable_resource_name_dns_a_record
    enable_resource_name_dns_aaaa_record = var.enable_resource_name_dns_aaaa_record
    hostname_type                        = var.hostname_type
  }

  root_block_device {
    delete_on_termination = var.root_block_device_delete_on_termination
    encrypted             = var.root_block_device_encrypted
    iops                  = var.root_block_device_iops
    throughput            = var.root_block_device_throughput
    volume_size           = var.root_block_device_volume_size
    volume_type           = var.root_block_device_volume_type
    tags = merge(
      var.tags,
      var.root_block_device_tags,
      { Name = "${var.tags["Name"]}-volume" }
    )
  }

  user_data = var.user_data != "" ? var.user_data : null

  source_dest_check = var.source_dest_check
  subnet_id         = var.subnet_id

  lifecycle {
    ignore_changes = [
      user_data,
      ami
    ]
  }

  tags = var.tags

  tags_all               = var.tags
  tenancy                = var.tenancy
  vpc_security_group_ids = var.vpc_security_group_ids
}

resource "aws_service_discovery_service" "main" {
  count = length(var.service_discovery_service_name) > 0 ? 1 : 0
  name  = var.service_discovery_service_name

  dns_config {
    namespace_id = var.namespace_id

    dns_records {
      ttl  = var.dns_records_ttl
      type = var.dns_records_type
    }

    routing_policy = var.routing_policy
  }

  tags = merge(
    var.tags,
    { Name = "${var.tags["Name"]}-sd" }
  )

}

resource "aws_service_discovery_instance" "main" {
  count       = length(var.service_discovery_service_name) > 0 ? 1 : 0
  instance_id = "${var.service_discovery_service_name}-service-discovery"
  service_id  = aws_service_discovery_service.main[0].id

  attributes = {
    AWS_INSTANCE_IPV4 = aws_instance.main.private_ip
  }
}

resource "aws_ebs_volume" "main" {
  count             = var.attach_external_disk ? 1 : 0
  availability_zone = var.availability_zone
  size              = var.external_volume_size
  type              = var.external_volume_type
  encrypted         = var.external_volume_encrypted
  iops              = var.external_volume_iops
  throughput        = var.external_volume_throughput

  tags = merge(
    var.tags,
    { Name = "${var.tags["Name"]}-external-volume" }
  )
}

resource "aws_volume_attachment" "main" {
  count       = var.attach_external_disk ? 1 : 0
  device_name = var.external_volume_device_name
  volume_id   = aws_ebs_volume.main[0].id
  instance_id = aws_instance.main.id
}
