resource "aws_appautoscaling_target" "main" {
  max_capacity = var.max_capacity
  min_capacity = var.min_capacity
  resource_id = "service/${var.ecs_cluster_name}/${var.ecs_service_name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace = "ecs"

  tags = var.appautoscaling_target_tags
}

resource "aws_appautoscaling_policy" "main_memory_autoscaling" {
  name               = "${var.ecs_service_name}_memory_autoscaling"
  policy_type        = var.policy_type
  resource_id        = aws_appautoscaling_target.main.resource_id
  scalable_dimension = aws_appautoscaling_target.main.scalable_dimension
  service_namespace  = aws_appautoscaling_target.main.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }

    target_value       = var.memory_autoscale_target_value
  }
}

resource "aws_appautoscaling_policy" "main_cpu_autoscaling" {
  name = "${var.ecs_service_name}_cpu_autoscaling"
  policy_type = "TargetTrackingScaling"
  resource_id = aws_appautoscaling_target.main.resource_id
  scalable_dimension = aws_appautoscaling_target.main.scalable_dimension
  service_namespace = aws_appautoscaling_target.main.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }

    target_value = var.cpu_autoscale_target_value
  }
}