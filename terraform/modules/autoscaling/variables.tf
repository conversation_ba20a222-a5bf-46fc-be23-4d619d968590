variable "region" {
    type = string
}

variable "max_capacity" {
    type = number
}

variable "min_capacity" {
    type = number
}

variable "ecs_cluster_name" {
    type = string
}

variable "ecs_service_name" {
    type = string
}

variable "policy_type" {
    type = string
    default = "TargetTrackingScaling"

}

variable "memory_autoscale_target_value" {
    type = number
}

variable "cpu_autoscale_target_value" {
    type = number
}

variable "appautoscaling_target_tags" {
    type = map(string)
}