variable "region" {
  description = "AWS region to deploy into"
  type        = string
  default     = "us-east-1"
}

variable "name" {
  description = "Name of the security group"
  type        = string
}

variable "description" {
  description = "Description of the security group"
  type        = string
  default     = null
}

variable "vpc_id" {
  description = "VPC ID where to create the security group"
  type        = string
}

variable "tags" {
  description = "Tags to apply to security group and rules"
  type        = map(string)
  default     = {}
}

# Standard ingress inputs
variable "ingress_ipv4_cidrs" {
  description = "List of IPv4 CIDRs for ingress rules"
  type        = list(string)
  default     = []
}

variable "ingress_ipv6_cidrs" {
  description = "List of IPv6 CIDRs for ingress rules"
  type        = list(string)
  default     = []
}

variable "ingress_prefix_list_ids" {
  description = "List of AWS prefix-list IDs for ingress rules"
  type        = list(string)
  default     = []
}

variable "ingress_referenced_security_group_ids" {
  description = "List of other security-group IDs for ingress (source)"
  type        = list(string)
  default     = []
}

variable "ingress_from_port" {
  description = "Default from_port for standard ingress lists"
  type        = number
  default     = 0
}

variable "ingress_to_port" {
  description = "Default to_port for standard ingress lists"
  type        = number
  default     = 0
}

variable "ingress_protocol" {
  description = "Default protocol for standard ingress lists"
  type        = string
  default     = "-1"
}

# Custom ingress rules for anything else
variable "ingress_custom_rules" {
  description = "List of custom ingress rule objects"
  type = list(object({
    cidr_ipv4                    = list(string)
    cidr_ipv6                    = list(string)
    prefix_list_id               = optional(string)
    referenced_security_group_id = optional(string)
    from_port                    = number
    to_port                      = number
    ip_protocol                  = string
    description                  = optional(string)
    })
  )
  default = [{
    cidr_ipv4                    = []
    cidr_ipv6                    = []
    prefix_list_id               = null
    referenced_security_group_id = null
    from_port                    = 0
    to_port                      = 0
    ip_protocol                  = "-1"
    description                  = ""
  }]
}

# Egress inputs follow same pattern
variable "egress_ipv4_cidrs" {
  description = "List of IPv4 CIDRs for egress rules"
  type        = list(string)
  default     = []
}

variable "egress_ipv6_cidrs" {
  description = "List of IPv6 CIDRs for egress rules"
  type        = list(string)
  default     = []
}

variable "egress_prefix_list_ids" {
  description = "List of AWS prefix-list IDs for egress rules"
  type        = list(string)
  default     = []
}

variable "egress_referenced_security_group_ids" {
  description = "List of other security-group IDs for egress"
  type        = list(string)
  default     = []
}

variable "egress_from_port" {
  description = "Default from_port for standard egress lists"
  type        = number
  default     = 0
}

variable "egress_to_port" {
  description = "Default to_port for standard egress lists"
  type        = number
  default     = 0
}

variable "egress_protocol" {
  description = "Default protocol for standard egress lists"
  type        = string
  default     = "-1"
}

variable "egress_custom_rules" {
  description = "List of custom egress rule objects"
  type = list(object({
    cidr_ipv4                    = list(string)
    cidr_ipv6                    = list(string)
    prefix_list_id               = optional(string)
    referenced_security_group_id = optional(string)
    from_port                    = number
    to_port                      = number
    ip_protocol                  = string
    description                  = optional(string)
  }))
  default = [
    {
      cidr_ipv4                    = []
      cidr_ipv6                    = []
      prefix_list_id               = null
      referenced_security_group_id = null
      from_port                    = 0
      to_port                      = 0
      ip_protocol                  = "-1"
      description                  = ""
    }
  ]
}

variable "ingress_self" {
  description = "self-referencing security group id for ingress rules"
  type        = bool
  default     = false
}

variable "egress_self" {
  description = "self-referencing security group id for egress rules"
  type        = bool
  default     = false
}


