# 1. Security Group
resource "aws_security_group" "this" {
  name        = var.name
  description = var.description
  vpc_id      = var.vpc_id
  tags        = var.tags
}

# 2. Build combined ingress rules
locals {

  custom_ingress_expanded = flatten([
    for rule in var.ingress_custom_rules : concat([
      # one entry per IPv4 CIDR
      for cidr4 in rule.cidr_ipv4 : {
        cidr_ipv4                    = cidr4
        cidr_ipv6                    = null
        prefix_list_id               = try(rule.prefix_list_id, null)
        referenced_security_group_id = try(rule.referenced_security_group_id, null)
        from_port                    = rule.ip_protocol == "-1" ? null : rule.from_port
        to_port                      = rule.ip_protocol == "-1" ? null : rule.to_port
        ip_protocol                  = rule.ip_protocol
        description                  = try(rule.description, "")
      }
      ]
      , [
        # one entry per IPv6 CIDR
        for cidr6 in rule.cidr_ipv6 : {
          cidr_ipv4                    = null
          cidr_ipv6                    = cidr6
          prefix_list_id               = try(rule.prefix_list_id, null)
          referenced_security_group_id = try(rule.referenced_security_group_id, null)
          from_port                    = rule.ip_protocol == "-1" ? null : rule.from_port
          to_port                      = rule.ip_protocol == "-1" ? null : rule.to_port
          ip_protocol                  = rule.ip_protocol
          description                  = try(rule.description, "")
        }
    ])
  ])

  ingress_rules = concat(
    [for cidr in var.ingress_ipv4_cidrs : {
      cidr_ipv4 = cidr
      # if to_protocol is "-1", then from_port and to_port are ignored
      # change the below code
      from_port   = var.ingress_protocol == "-1" ? null : var.ingress_from_port
      to_port     = var.ingress_protocol == "-1" ? null : var.ingress_to_port
      ip_protocol = var.ingress_protocol
      description = ""
    }],
    [for cidr in var.ingress_ipv6_cidrs : {
      cidr_ipv6   = cidr
      from_port   = var.ingress_protocol == "-1" ? null : var.ingress_from_port
      to_port     = var.ingress_protocol == "-1" ? null : var.ingress_to_port
      ip_protocol = var.ingress_protocol
      description = ""
    }],
    [for pl in var.ingress_prefix_list_ids : {
      prefix_list_id = pl
      from_port      = var.ingress_protocol == "-1" ? null : var.ingress_from_port
      to_port        = var.ingress_protocol == "-1" ? null : var.ingress_to_port
      ip_protocol    = var.ingress_protocol
      description    = ""
    }],
    [for sg in var.ingress_referenced_security_group_ids : {
      referenced_security_group_id = sg
      from_port                    = var.ingress_protocol == "-1" ? null : var.ingress_from_port
      to_port                      = var.ingress_protocol == "-1" ? null : var.ingress_to_port
      ip_protocol                  = var.ingress_protocol
      description                  = ""
    }],
    local.custom_ingress_expanded,
    var.ingress_self ? [{
      referenced_security_group_id = aws_security_group.this.id
      from_port                    = var.ingress_protocol == "-1" ? null : var.ingress_from_port
      to_port                      = var.ingress_protocol == "-1" ? null : var.ingress_to_port
      ip_protocol                  = var.ingress_protocol
      description                  = "self-reference"
    }] : []
  )
}

resource "aws_vpc_security_group_ingress_rule" "ingress" {
  for_each = { for idx, rule in local.ingress_rules : idx => rule }

  security_group_id            = aws_security_group.this.id
  ip_protocol                  = try(each.value.ip_protocol, null)
  from_port                    = try(each.value.from_port, null)
  to_port                      = try(each.value.to_port, null)
  cidr_ipv4                    = try(each.value.cidr_ipv4, null)
  cidr_ipv6                    = try(each.value.cidr_ipv6, null)
  prefix_list_id               = try(each.value.prefix_list_id, null)
  referenced_security_group_id = try(each.value.referenced_security_group_id, null)

  description = try(each.value.description, null)

  depends_on = [aws_security_group.this]
}

# 3. Build combined egress rules
locals {
  custom_egress_expanded = flatten([
    for rule in var.egress_custom_rules : concat([
      # one entry per IPv4 CIDR
      for cidr4 in rule.cidr_ipv4 : {
        cidr_ipv4                    = cidr4
        cidr_ipv6                    = null
        prefix_list_id               = try(rule.prefix_list_id, null)
        referenced_security_group_id = try(rule.referenced_security_group_id, null)
        from_port                    = rule.ip_protocol == "-1" ? null : rule.from_port
        to_port                      = rule.ip_protocol == "-1" ? null : rule.to_port
        ip_protocol                  = rule.ip_protocol
        description                  = try(rule.description, "")
      }
      ], [
      # one entry per IPv6 CIDR
      for cidr6 in rule.cidr_ipv6 : {
        cidr_ipv4                    = null
        cidr_ipv6                    = cidr6
        prefix_list_id               = try(rule.prefix_list_id, null)
        referenced_security_group_id = try(rule.referenced_security_group_id, null)
        from_port                    = rule.ip_protocol == "-1" ? null : rule.from_port
        to_port                      = rule.ip_protocol == "-1" ? null : rule.to_port
        ip_protocol                  = rule.ip_protocol
        description                  = try(rule.description, "")
      }
    ])
  ])

  egress_rules = concat(
    [for cidr in var.egress_ipv4_cidrs : {
      cidr_ipv4   = cidr
      from_port   = var.egress_protocol == "-1" ? null : var.egress_from_port
      to_port     = var.egress_protocol == "-1" ? null : var.egress_to_port
      ip_protocol = var.egress_protocol
      description = ""
    }],
    [for cidr in var.egress_ipv6_cidrs : {
      cidr_ipv6   = cidr
      from_port   = var.egress_protocol == "-1" ? null : var.egress_from_port
      to_port     = var.egress_protocol == "-1" ? null : var.egress_to_port
      ip_protocol = var.egress_protocol
      description = ""
    }],
    [for pl in var.egress_prefix_list_ids : {
      prefix_list_id = pl
      from_port      = var.egress_protocol == "-1" ? null : var.egress_from_port
      to_port        = var.egress_protocol == "-1" ? null : var.egress_to_port
      ip_protocol    = var.egress_protocol == "-1" ? null : var.egress_protocol
      description    = ""
    }],
    [for sg in var.egress_referenced_security_group_ids : {
      referenced_security_group_id = sg
      from_port                    = var.egress_protocol == "-1" ? null : var.egress_from_port
      to_port                      = var.egress_protocol == "-1" ? null : var.egress_to_port
      ip_protocol                  = var.egress_protocol == "-1" ? null : var.egress_protocol
      description                  = ""
    }],
    local.custom_egress_expanded,
    var.egress_self ? [{
      referenced_security_group_id = aws_security_group.this.id
      from_port                    = var.egress_protocol == "-1" ? null : var.egress_from_port
      to_port                      = var.egress_protocol == "-1" ? null : var.egress_to_port
      ip_protocol                  = var.egress_protocol == "-1" ? null : var.egress_protocol
      description                  = "self-reference"
    }] : []
  )
}

resource "aws_vpc_security_group_egress_rule" "egress" {
  for_each = { for idx, rule in local.egress_rules : idx => rule }

  security_group_id            = aws_security_group.this.id
  ip_protocol                  = try(each.value.ip_protocol, null)
  from_port                    = try(each.value.from_port, null)
  to_port                      = try(each.value.to_port, null)
  cidr_ipv4                    = try(each.value.cidr_ipv4, null)
  cidr_ipv6                    = try(each.value.cidr_ipv6, null)
  prefix_list_id               = try(each.value.prefix_list_id, null)
  referenced_security_group_id = try(each.value.referenced_security_group_id, null)
  description                  = try(each.value.description, null)

  depends_on = [aws_security_group.this]
}
