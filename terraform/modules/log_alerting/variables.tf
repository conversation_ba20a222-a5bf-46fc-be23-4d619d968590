variable "region" {
  type = string
}

variable "log_filter_name" {
  type = string
}

variable "log_group_name" {
  type = string
}

variable "log_pattern" {
  type = string
}

variable "metric_transformation_name" {
  type = string
}

variable "metric_transformation_namespace" {
  type    = string
  default = "LogMetrics"
}

variable "metric_transformation_value" {
  type    = string
  default = "1"
}

variable "alarm_name" {
  type = string
}

variable "alarm_description" {
  type    = string
  default = ""
}

variable "treat_missing_data" {
  type    = string
  default = "missing"
}

variable "alarm_metric_threshold" {
  type    = number
  default = "1"
}

variable "alarm_metric_statistic" {
  type    = string
  default = "Sum"
}

variable "alarm_metric_comparison_operator" {
  type    = string
  default = "GreaterThanOrEqualToThreshold"
}

variable "alarm_metric_datapoints_to_alarm" {
  type    = string
  default = "1"
}

variable "alarm_metric_evaluation_periods" {
  type    = string
  default = "1"
}

variable "alarm_metric_period" {
  type    = string
  default = "60"
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "sns_topic_arn" {
  type = string
}

