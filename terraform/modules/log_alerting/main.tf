resource "aws_cloudwatch_log_metric_filter" "main" {
  name           = var.log_filter_name
  log_group_name = var.log_group_name
  pattern        = var.log_pattern
  metric_transformation {
    name      = "${var.metric_transformation_name}"
    namespace = "${var.metric_transformation_namespace}_${var.log_group_name}"
    value     = var.metric_transformation_value
  }
}

resource "aws_cloudwatch_metric_alarm" "main" {

  alarm_name         = var.alarm_name
  alarm_description  = var.alarm_description
  alarm_actions      = [var.sns_topic_arn]
  # treat_missing_data = var.treat_missing_data

  metric_name         = lookup(aws_cloudwatch_log_metric_filter.main.metric_transformation[0], "name")
  threshold           = var.alarm_metric_threshold
  statistic           = var.alarm_metric_statistic
  comparison_operator = var.alarm_metric_comparison_operator
  # datapoints_to_alarm = var.alarm_metric_datapoints_to_alarm
  evaluation_periods  = var.alarm_metric_evaluation_periods
  period              = var.alarm_metric_period
  namespace           = lookup(aws_cloudwatch_log_metric_filter.main.metric_transformation[0], "namespace")

  tags = {
    Name = var.alarm_name
    Environment = "prod"
    Team = "infra"
    Product = "ravenclaw"
  }
}
