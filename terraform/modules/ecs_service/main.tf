resource "aws_service_discovery_service" "main" {
  name = var.service_discovery_service_name

  dns_config {
    namespace_id = var.namespace_id

    dns_records {
      ttl  = var.dns_records_ttl
      type = var.dns_records_type
    }

    routing_policy = var.routing_policy
  }

  tags = var.service_discovery_svc_tags
}


resource "aws_ecs_service" "main" {
  # alarms {
  #   enable   = var.alarms_enable
  #   rollback = var.alarms_rollback
  #   alarm_names = var.alarm_names
  # }

  dynamic "capacity_provider_strategy" {
    for_each = var.capacity_provider_strategies
    content {
      capacity_provider = capacity_provider_strategy.value.capacity_provider
      base              = lookup(capacity_provider_strategy.value, "base", 0)
      weight            = lookup(capacity_provider_strategy.value, "weight", 1)
    }
  }
  # capacity_provider_strategy {
  #   base              = var.capacity_provider_strategy_base
  #   capacity_provider = var.capacity_provider
  #   weight            = var.capacity_provider_strategy_weight
  # }

  cluster = var.cluster

  deployment_circuit_breaker {
    enable   = var.deployment_circuit_breaker_enable
    rollback = var.deployment_circuit_breaker_rollback
  }

  deployment_controller {
    type = var.deployment_controller_type
  }

  deployment_maximum_percent         = var.deployment_maximum_percent
  deployment_minimum_healthy_percent = var.deployment_minimum_healthy_percent
  desired_count                      = var.desired_count
  enable_ecs_managed_tags            = var.enable_ecs_managed_tags
  enable_execute_command             = var.enable_execute_command
  health_check_grace_period_seconds  = var.health_check_grace_period_seconds
  name                               = var.name
  propagate_tags                     = var.propagate_tasks

  network_configuration {
    assign_public_ip = var.assign_public_ip
    security_groups  = var.security_groups_id
    subnets          = var.subnets_id
  }

  platform_version              = var.platform_version
  scheduling_strategy           = var.scheduling_strategy
  availability_zone_rebalancing = var.availability_zone_rebalancing

  service_registries {
    container_port = var.service_registries_container_port
    port           = var.service_registries_port
    registry_arn   = aws_service_discovery_service.main.name != null ? aws_service_discovery_service.main.arn : null
  }

  dynamic "load_balancer" {
    for_each = var.target_group_arn != "" ? [1] : []

    content {
      target_group_arn = var.target_group_arn
      container_name   = var.container_name
      container_port   = var.container_port
    }
  }


  # dynamic "ordered_placement_strategy" {
  #   for_each = var.spread ? [1] : []
  #   content {
  #     type  = "spread"
  #     field = "attribute:ecs.availability-zone"
  #   }
  # }

  lifecycle {
    ignore_changes = [
      desired_count,
      platform_version,
      capacity_provider_strategy
    ]
  }

  task_definition = var.task_definition_arn

  tags = var.ecs_service_tags
}
