variable "region" {
  type = string
}

variable "service_discovery_service_name" {
  type    = string
  default = null
}

variable "namespace_id" {
  type = string
}

variable "dns_records_ttl" {
  type    = number
  default = 10
}

variable "dns_records_type" {
  type    = string
  default = "A"
}

variable "routing_policy" {
  type    = string
  default = "MULTIVALUE"
}

variable "alarms_enable" {
  type    = bool
  default = false
}

variable "alarms_rollback" {
  type    = bool
  default = false
}

variable "alarm_names" {
  type    = list(string)
  default = []
}

variable "capacity_provider_strategies" {
  description = "List of capacity provider strategies for the ECS service"
  type = list(object({
    capacity_provider = string
    base              = optional(number, 0)
    weight            = optional(number, 1)
  }))
  default = [
    {
      capacity_provider = "FARGATE"
      base              = 0
      weight            = 1
  }]
}

# variable "capacity_provider_strategy_base" {
#   type    = number
#   default = 0
# }
#
# variable "capacity_provider" {
#   type    = string
#   default = "FARGATE"
# }
#
# variable "capacity_provider_strategy_weight" {
#   type    = number
#   default = 1
# }

variable "cluster" {
  type = string
}

variable "deployment_circuit_breaker_enable" {
  type    = bool
  default = true
}

variable "deployment_circuit_breaker_rollback" {
  type    = bool
  default = true
}

variable "deployment_controller_type" {
  type    = string
  default = "ECS"
}

variable "deployment_maximum_percent" {
  type    = number
  default = 200
}

variable "deployment_minimum_healthy_percent" {
  type    = number
  default = 100
}

variable "desired_count" {
  type = number
}

variable "enable_ecs_managed_tags" {
  type    = bool
  default = true
}

variable "enable_execute_command" {
  type    = bool
  default = false
}

variable "health_check_grace_period_seconds" {
  type    = number
  default = 0
}

variable "name" {
  type = string
}

variable "assign_public_ip" {
  type    = bool
  default = false
}

variable "security_groups_id" {
  type = list(string)
}

variable "subnets_id" {
  type = list(string)
}

variable "platform_version" {
  type    = string
  default = "LATEST"
}

variable "scheduling_strategy" {
  type    = string
  default = "REPLICA"
}

variable "service_registries_container_port" {
  type    = number
  default = 0
}

variable "service_registries_port" {
  type    = number
  default = 0
}

variable "task_definition_arn" {
  type = string
}

variable "target_group_arn" {
  type    = string
  default = ""
}

variable "container_name" {
  type    = string
  default = ""
}

variable "container_port" {
  type    = number
  default = 0
}

variable "service_discovery_svc_tags" {
  type    = map(string)
  default = {}
}

variable "ecs_service_tags" {
  type    = map(string)
  default = {}
}

variable "propagate_tasks" {
  type    = string
  default = "SERVICE"
}

variable "spread" {
  type    = bool
  default = false
}

variable "availability_zone_rebalancing" {
  type    = string
  default = "DISABLED"
}
