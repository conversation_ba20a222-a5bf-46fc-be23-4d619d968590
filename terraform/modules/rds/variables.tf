variable "region" {
  type = string
}

variable "vpc_name" {
  type = string
}

variable "subnet_ids" {
  type = list(string)
}

variable "db_name" {
  type = string
}

variable "allocated_storage" {
  type = number
}

variable "auto_minor_version_upgrade" {
  type    = bool
  default = false
}

variable "availability_zone" {
  type = string
}

variable "backup_retention_period" {
  type    = number
  default = 7
}

variable "backup_target" {
  type    = string
  default = "region"
}

variable "backup_window" {
  type    = string
  default = "18:01-18:31"
}

variable "copy_tags_to_snapshot" {
  type    = bool
  default = true
}

variable "customer_owned_ip_enabled" {
  type    = bool
  default = false
}

variable "dedicated_log_volume" {
  type    = bool
  default = false
}

variable "deletion_protection" {
  type    = bool
  default = true
}

variable "engine" {
  type    = string
  default = "postgres"
}

variable "engine_version" {
  type    = number
  default = 16.3
}

variable "iam_database_authentication_enabled" {
  type    = bool
  default = false
}

variable "identifier" {
  type = string
}

variable "apply_immediately" {
  type    = bool
  default = true
}

variable "instance_class" {
  type    = string
  default = "db.t3.micro"
}

variable "iops" {
  type    = number
  default = 0
}

variable "license_model" {
  type    = string
  default = "postgresql-license"
}

variable "maintenance_window" {
  type    = string
  default = "sat:03:00-sat:03:30"
}

variable "max_allocated_storage" {
  type    = number
  default = 1000
}

variable "monitoring_interval" {
  type    = number
  default = 0
}

variable "monitoring_role_arn" {
  type    = string
  default = ""
}

variable "multi_az" {
  type    = bool
  default = false
}

variable "network_type" {
  type    = string
  default = "IPV4"
}

variable "option_group_name" {
  type    = string
  default = "default:postgres-16"
}

variable "parameter_group_name" {
  type    = string
  default = "default.postgres16"
}

variable "performance_insights_enabled" {
  type    = bool
  default = true
}

variable "performance_insights_retention_period" {
  type    = number
  default = 7
}

variable "port" {
  type    = number
  default = 5432
}

variable "publicly_accessible" {
  type    = bool
  default = false
}

variable "storage_encrypted" {
  type    = bool
  default = true
}

variable "storage_throughput" {
  type    = number
  default = 0
}

variable "storage_type" {
  type    = string
  default = "gp2"
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "env" {
  type    = string
  default = "dev"
}

variable "username" {
  type    = string
  default = "ravenclaw"
}

variable "password" {
  type = string
}

variable "manage_master_user_password" {
  type    = bool
  default = false
}

variable "vpc_security_group_ids" {
  type = list(string)
}

variable "skip_final_snapshot" {
  type    = bool
  default = false
}

variable "instance_tags" {
  type    = map(string)
  default = {}
}

variable "subnet_group_tags" {
  type    = map(string)
  default = {}
}

variable "kms_tags" {
  type    = map(string)
  default = {}
}

variable "create_read_replica" {
  type    = bool
  default = false
}

variable "read_replica_azs" {
  type    = list(string)
  default = []
}


variable "replica_instance_tags" {
  type    = map(string)
  default = {}
}

variable "db_subnet_group_name" {
  type = string
}

variable "enabled_cloudwatch_logs_exports" {
  type    = list(string)
  default = ["postgresql", "upgrade"]
}

variable "read_replica_instance_class" {
  type    = string
  default = ""
}

variable "read_replica_multi_az" {
  type    = bool
  default = false
}

variable "read_replica_engine_version" {
  type    = number
  default = null
}
