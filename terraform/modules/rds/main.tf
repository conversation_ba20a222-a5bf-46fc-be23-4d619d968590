resource "aws_kms_key" "main" {
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  enable_key_rotation      = "true"
  is_enabled               = "true"
  key_usage                = "ENCRYPT_DECRYPT"
  multi_region             = "false"
  # rotation_period_in_days  = 90
  # description              = "A symmetric encryption KMS key for ${var.db_name} RDS"
  tags = var.kms_tags
}

resource "aws_db_instance" "main" {
  db_name                               = var.db_name
  allocated_storage                     = var.allocated_storage
  auto_minor_version_upgrade            = var.auto_minor_version_upgrade
  availability_zone                     = var.availability_zone
  backup_retention_period               = var.backup_retention_period
  backup_target                         = var.backup_target
  backup_window                         = var.backup_window
  copy_tags_to_snapshot                 = var.copy_tags_to_snapshot
  customer_owned_ip_enabled             = var.customer_owned_ip_enabled
  db_subnet_group_name                  = var.db_subnet_group_name
  dedicated_log_volume                  = var.dedicated_log_volume
  deletion_protection                   = var.deletion_protection
  engine                                = var.engine
  engine_version                        = var.engine_version
  iam_database_authentication_enabled   = var.iam_database_authentication_enabled
  identifier                            = var.identifier
  apply_immediately                     = var.apply_immediately
  instance_class                        = var.instance_class
  iops                                  = var.iops
  license_model                         = var.license_model
  maintenance_window                    = var.maintenance_window
  max_allocated_storage                 = var.max_allocated_storage
  monitoring_interval                   = var.monitoring_interval
  monitoring_role_arn                   = var.monitoring_role_arn
  multi_az                              = var.multi_az
  network_type                          = var.network_type
  option_group_name                     = var.option_group_name
  parameter_group_name                  = var.parameter_group_name
  performance_insights_enabled          = var.performance_insights_enabled
  performance_insights_retention_period = var.performance_insights_retention_period
  port                                  = var.port
  publicly_accessible                   = var.publicly_accessible
  storage_encrypted                     = var.storage_encrypted
  storage_throughput                    = var.storage_throughput
  storage_type                          = var.storage_type
  final_snapshot_identifier             = "${var.db_name}-snapshot"
  skip_final_snapshot                   = var.skip_final_snapshot
  enabled_cloudwatch_logs_exports       = var.enabled_cloudwatch_logs_exports
  delete_automated_backups              = false
  # kms_key_id                            = aws_kms_key.main.arn

  tags = var.instance_tags

  username = var.username
  password = var.password
  # manage_master_user_password   = var.manage_master_user_password
  vpc_security_group_ids = var.vpc_security_group_ids
}


resource "aws_db_instance" "read_replica" {
  for_each = var.create_read_replica ? { for idx, az in var.read_replica_azs : idx => az } : {}

  replicate_source_db                   = aws_db_instance.main.identifier
  instance_class                        = var.read_replica_instance_class == "" ? var.instance_class : var.read_replica_instance_class
  availability_zone                     = each.value
  publicly_accessible                   = var.publicly_accessible
  storage_type                          = var.storage_type
  vpc_security_group_ids                = var.vpc_security_group_ids
  auto_minor_version_upgrade            = var.auto_minor_version_upgrade
  performance_insights_enabled          = var.performance_insights_enabled
  performance_insights_retention_period = var.performance_insights_retention_period
  identifier                            = "${var.identifier}-read-replica-${each.key + 1}"
  deletion_protection                   = var.deletion_protection
  enabled_cloudwatch_logs_exports       = var.enabled_cloudwatch_logs_exports
  skip_final_snapshot                   = var.skip_final_snapshot
  final_snapshot_identifier             = "${var.identifier}-read-replica-${each.key + 1}-snapshot"
  storage_encrypted                     = var.storage_encrypted
  # kms_key_id                            = aws_kms_key.main.arn
  apply_immediately = true
  multi_az          = var.read_replica_multi_az
  engine_version    = var.read_replica_engine_version == null ? var.engine_version : var.read_replica_engine_version

  tags = merge(
    var.replica_instance_tags,
    {
      Name = "${var.replica_instance_tags["Name"]}-${each.key + 1}"
    }
  )

  lifecycle {
    ignore_changes = [
      # apply_immediately,
      character_set_name,
      customer_owned_ip_enabled,
      domain_dns_ips,
      domain_fqdn,
      iam_database_authentication_enabled,
      identifier_prefix,
      latest_restorable_time,
      master_user_secret_kms_key_id,
      max_allocated_storage,
      monitoring_role_arn,
      nchar_character_set_name,
      performance_insights_kms_key_id,
      replica_mode,
      snapshot_identifier,
      timezone,
    ]
  }
}
