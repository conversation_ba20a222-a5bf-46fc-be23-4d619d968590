resource "aws_ecs_task_definition" "main" {

  container_definitions = var.container_definitions_json

  cpu                      = var.cpu
  execution_role_arn       = var.execution_role_arn
  family                   = var.family
  memory                   = var.memory
  network_mode             = var.network_mode
  requires_compatibilities = var.requires_compatibilities

  dynamic "volume" {
    for_each = var.volume_name != "" ? [var.volume_name] : []
    content {
      name = var.volume_name
      efs_volume_configuration {
      file_system_id     = var.file_system_id
      root_directory     = var.root_directory
      transit_encryption = var.transit_encryption
      
      authorization_config {
        access_point_id = var.access_point_id
        iam             = var.iam
      }
    }
    }
  }

  runtime_platform {
    cpu_architecture        = var.cpu_architecture
    operating_system_family = var.operating_system_family
  }

  task_role_arn = var.task_role_arn
  track_latest  = var.track_latest

  tags = var.tags
}
