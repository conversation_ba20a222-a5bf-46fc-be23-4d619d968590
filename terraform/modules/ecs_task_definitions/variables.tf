variable "region" {
  type = string
}

variable "container_definitions_json" {
  type = string
}

variable "cpu" {
  type = number
}

variable "execution_role_arn" {
  type = string
}

variable "family" {
  type = string
}

variable "memory" {
  type = number
}

variable "network_mode" {
  type    = string
  default = "awsvpc"
}

variable "requires_compatibilities" {
  type    = list(string)
  default = ["FARGATE"]
}

variable "cpu_architecture" {
  type    = string
  default = "ARM64"
}

variable "operating_system_family" {
  type    = string
  default = "LINUX"
}

variable "task_role_arn" {
  type = string
}

variable "track_latest" {
  type    = bool
  default = false
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "volume_name" {
  type    = string
  default = ""
}

variable "file_system_id" {
  type    = string
  default = ""
}
  
variable "root_directory" {
  type    = string
  default = "/"
}
  
variable "transit_encryption" {
  type    = string
  default = "ENABLED"
}
  
variable "access_point_id" {
  type    = string
  default = ""
}
  
variable "iam" {
  type    = string
  default = "ENABLED"
}