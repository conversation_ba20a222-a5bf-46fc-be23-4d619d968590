variable "region" {
  type = string
}

variable "service_discovery_private_dns_namespace" {
  type = string
}

variable "service_discovery_private_dns_description" {
  type    = string
  default = ""
}

variable "vpc_id" {
  type = string
}

variable "logging" {
  type    = string
  default = "DEFAULT"
}

variable "name" {
  type = string
}

variable "setting_name" {
  type    = string
  default = "containerInsights"
}

variable "setting_value" {
  type    = string
  default = "enhanced"
}

variable "namespace_tags" {
  type = map(string)
}

variable "ecs_cluster_tags" {
  type    = map(string)
  default = {}
}
