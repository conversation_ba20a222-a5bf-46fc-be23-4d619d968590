resource "aws_service_discovery_private_dns_namespace" "main" {
  name        = var.service_discovery_private_dns_namespace
  description = var.service_discovery_private_dns_description
  vpc         = var.vpc_id

  tags = var.namespace_tags
}


resource "aws_ecs_cluster" "main" {
  configuration {
    execute_command_configuration {
      logging = var.logging
    }
  }

  name = var.name

  service_connect_defaults {
    namespace = aws_service_discovery_private_dns_namespace.main.arn
  }

  setting {
    name  = var.setting_name
    value = var.setting_value
  }

  tags = var.ecs_cluster_tags
}
