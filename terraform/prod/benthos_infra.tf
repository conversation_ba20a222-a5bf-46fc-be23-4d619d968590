resource "aws_secretsmanager_secret" "BENTHOS_NATS_URLS" {
  name                    = "BENTHOS_NATS_URLS"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "benthos-nats-urls-secret"
    Environment = "prod"
    Team        = "infra"
    Product     = "benthos"
  }
}

resource "aws_secretsmanager_secret_version" "BENTHOS_NATS_URLS" {
  secret_id     = aws_secretsmanager_secret.BENTHOS_NATS_URLS.arn
  secret_string = var.BENTHOS_NATS_URLS
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

module "benthos_sg" {
  source      = "../modules/security_groups"
  region      = var.region
  name        = "benthos-sg"
  description = "Security group for Benthos service"
  vpc_id      = module.vpc_main.vpc_id

  tags = {
    Name        = "benthos-sg"
    Environment = "prod"
    Team        = "infra"
    Product     = "benthos"
  }

  # Egress rules - allow all outbound traffic
  egress_ipv4_cidrs = ["0.0.0.0/0"]
  egress_protocol   = "-1"

  # Allow internal HTTP access for the Benthos API
  ingress_custom_rules = [{
    cidr_ipv4                    = [var.vpc_cidr_block]
    cidr_ipv6                    = []
    from_port                    = 4195
    to_port                      = 4195
    ip_protocol                  = "tcp"
    description                  = "Benthos HTTP API"
    prefix_list_id               = null
    referenced_security_group_id = null
  }]
}


# Benthos variables
variable "benthos_docker_image_name" {
  type        = string
  description = "Name of the Benthos Docker image"
  default     = "benthos"
}

variable "benthos_image_uri" {
  type        = string
  description = "URI for the Benthos Docker image"
}

variable "benthos_logs_group" {
  type        = string
  description = "CloudWatch log group for Benthos"
  default     = "/ecs/benthos"
}

variable "benthos_cpu" {
  type        = number
  description = "CPU units for Benthos task"
  default     = 512
}

variable "benthos_memory" {
  type        = number
  description = "Memory for Benthos task (in MiB)"
  default     = 1024
}

variable "BENTHOS_NATS_URLS" {
  type = string
}

module "benthos_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "secrets" : [{
      "name" : "NATS_URLS",
      "valueFrom" : aws_secretsmanager_secret.BENTHOS_NATS_URLS.arn
      }
    ],
    "environment" : [
      {
        "name" : "BUCKET_REGION",
        "value" : "ap-south-1"
      },
      {
        "name" : "NATS_SUBJECT",
        "value" : "dlq.>"
      },
      {
        "name" : "NATS_STREAM",
        "value" : "EVENTS_DLQ"
      },
      {
        "name" : "NATS_CONSUMER_DELIVER",
        "value" : "all"
      },
      {
        "name" : "NATS_CONSUMER_DURABLE",
        "value" : "s3-prod-archiver"
      },
      {
        "name" : "AWS_S3_BUCKET",
        "value" : "nats-s3-archival-bucket"
      },
      {
        "name" : "AWS_S3_PATH",
        "value" : "prod/EVENTS_DLQ/with-id-data"
      }
    ],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.benthos_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : "/ecs/benthos",
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : "benthos",
    "portMappings" : [
      {
        "containerPort" : 4195,
        "hostPort" : 4195,
        "protocol" : "tcp"
      }
    ],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.benthos_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "benthos"
  memory             = var.benthos_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = {
    Name        = "benthos-task-definition"
    Environment = "prod"
    Team        = "infra"
    Product     = "benthos"
  }
}

module "benthos_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = "benthos"
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [module.benthos_sg.security_group_id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = "benthos-service"
  desired_count                  = 1
  task_definition_arn            = module.benthos_task_definition.arn

  enable_execute_command = true

  service_discovery_svc_tags = {
    Name        = "benthos-service-sd"
    Team        = "infra"
    Product     = "benthos"
    Environment = "prod"
  }

  ecs_service_tags = {
    Name        = "benthos-service"
    Team        = "infra"
    Product     = "benthos"
    Environment = "prod"
  }
}


resource "aws_cloudwatch_metric_alarm" "benthos_ecs_cpu_utilization" {
  alarm_name          = "Benthos_Service_ECS_CPU_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Benthos Service - Alarm when ECS CPUUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = "benthos-service"
  }

  tags = {
    Name        = "Benthos_Service_ECS_CPU_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "benthos_service_ecs_memory_utilization" {
  alarm_name          = "benthos_Service_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Benthos Service - Alarm when ECS Memory Utilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = "benthos-service"
  }

  tags = {
    Name        = "Benthos_Service_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

module "benthos_service_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = "benthos-service"
  max_capacity                  = "3"
  min_capacity                  = "1"
  memory_autoscale_target_value = "80"
  cpu_autoscale_target_value    = "80"

  appautoscaling_target_tags = {
    Name        = "benthos-service"
    Team        = "infra"
    Product     = "benthos"
    Environment = "prod"
  }
}

module "benthos_error_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = "/ecs/benthos"
  log_filter_name            = "BENTHOS_ERROR_LOG_FILTER"
  log_pattern                = "\"level=error\""
  alarm_name                 = "BENTHOS_ERROR_LOG_ALARM"
  sns_topic_arn              = aws_sns_topic.main.arn
  metric_transformation_name = "BENTHOS_ERROR_LOG_METRIC"
}

module "benthos_fatal_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = "/ecs/benthos"
  log_filter_name            = "BENTHOS_FATAL_LOG_FILTER"
  log_pattern                = "\"level=fatal\""
  alarm_name                 = "BENTHOS_FATAL_LOG_ALARM"
  sns_topic_arn              = aws_sns_topic.main.arn
  metric_transformation_name = "BENTHOS_FATAL_LOG_METRIC"
}
