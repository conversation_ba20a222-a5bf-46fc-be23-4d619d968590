variable "keycloak_db" {
  type    = string
  default = "postgres"
}

variable "keycloak_db_port" {
  type    = string
  default = "5432"
}

variable "keycloak_hostname" {
  type = string
}

variable "keycloak_image_uri" {
  type    = string
  default = "771151923073.dkr.ecr.us-east-1.amazonaws.com/keycloak:1.0.0"
}

variable "keycloak_logs_group" {
  type    = string
  default = "keycloak-ecs-logs"
}

variable "keycloak_docker_image_name" {
  type    = string
  default = "keycloak"
}

variable "keycloak_cpu" {
  type    = number
  default = "512"
}

variable "keycloak_memory" {
  type    = number
  default = 1024
}

variable "keycloak_service_discovery_name" {
  type = string
}

variable "keycloak_service_name" {
  type = string
}

variable "keycloak_desired_count" {
  type    = number
  default = 1
}

# ALB
variable "keycloak_lb_name" {
  type = string
}

variable "keycloak_lb_internal" {
  type    = string
  default = false
}

variable "keycloak_lb_type" {
  type    = string
  default = "application"
}

variable "keycloak_lb_deletion_protection" {
  type    = string
  default = true
}

variable "keycloak_lb_target_group_name" {
  type = string
}

variable "keycloak_lb_target_group_target_type" {
  type    = string
  default = "ip"
}

variable "keycloak_lb_target_group_port" {
  type    = number
  default = 443
}

variable "keycloak_lb_target_group_protocol" {
  type    = string
  default = "HTTPS"
}

variable "keycloak_lb_target_group_health_check_path" {
  type    = string
  default = "/health"
}

variable "keycloak_lb_listener_port" {
  type    = number
  default = 443
}

variable "keycloak_lb_listener_protocol" {
  type    = string
  default = "HTTPS"
}

variable "keycloak_lb_listener_ssl_policy" {
  type    = string
  default = "ELBSecurityPolicy-TLS13-1-2-2021-06"
}

variable "keycloak_certificate_arn" {
  type = string
}

variable "kc_db_url_db" {
  type    = string
  default = "keycloak"
}

variable "kc_log_level" {
  type    = string
  default = "INFO"
}

variable "kc_proxy_headers" {
  type    = string
  default = "xforwarded"
}

variable "kc_container_port" {
  type    = number
  default = 8443
}

variable "kc_alb_health_check_port" {
  type    = number
  default = 9000
}

variable "kc_alb_health_check_protocol" {
  type    = string
  default = "HTTPS"
}

variable "kc_alb_health_check_matcher" {
  type    = string
  default = "200"
}

variable "kc_alb_listener_default_action_type" {
  type    = string
  default = "forward"
}

variable "kc_alb_http_listener_port" {
  type    = number
  default = 80
}

variable "kc_alb_http_listener_protocol" {
  type    = string
  default = "HTTP"
}

variable "kc_alb_http_listener_default_action_type" {
  type    = string
  default = "redirect"
}

variable "kc_alb_http_listener_default_action_redirect_port" {
  type    = string
  default = 443
}

variable "kc_alb_http_listener_default_action_redirect_protocol" {
  type    = string
  default = "HTTPS"
}

variable "kc_alb_http_listener_default_action_redirect_status_code" {
  type    = string
  default = "HTTP_301"
}

variable "kc_alb_https_redirect_action_type" {
  type    = string
  default = "redirect"
}

variable "kc_alb_https_redirect_action_redirect_port" {
  type    = number
  default = 443
}

variable "kc_alb_https_redirect_action_redirect_protocol" {
  type    = string
  default = "HTTPS"
}

variable "kb_alb_https_redirect_action_redirect_status_code" {
  type    = string
  default = "HTTP_301"
}

variable "kc_alb_listener_rule_100_action_type" {
  type    = string
  default = "forward"
}

variable "kc_alb_listener_rule_100_condition_path_pattern" {
  type    = list(string)
  default = ["/*"]
}

variable "kc_cache" {
  type    = string
  default = "ispn"
}

variable "kc_cache_config_file" {
  type    = string
  default = "cache-ispn-jdbc-ping.xml"
}

variable "keycloak_db_name" {
  type    = string
  default = "keycloak"
}

variable "keycloak_rds_allocated_storage" {
  type    = number
  default = 10
}

variable "keycloak_rds_availability_zone" {
  type    = string
  default = "ap-south-1b"
}

variable "keycloak_identifier" {
  type    = string
  default = "keycloak"
}

variable "KEYCLOAK_PG_PASSWORD" {
  type = string
}

variable "keycloak_rds_multi_az" {
  type    = bool
  default = false
}

variable "keycloak_rds_instance_class" {
  type = string
}

variable "keycloak_rds_read_replica_azs" {
  type = list(string)
}

variable "keycloak_rds_create_read_replica" {
  type    = bool
  default = true
}
