# resource "aws_sns_topic" "cloudtrail_alerts" {
#   name = "CloudtrailAlertsSNS"
#   tags = {
#     Name        = "CloudtrailAlerts"
#     Environment = "prod"
#     Team        = "infra"
#     Product     = "compliance"
#   }
# }

# resource "aws_lambda_function" "cloudtrail_alerts" {
#   filename         = "lambda_function_cloudtrail.zip"
#   function_name    = "CloudtrailAlerts"
#   handler          = var.lambda_handler
#   runtime          = var.lambda_runtime
#   role             = aws_iam_role.lambda_exec.arn
#   source_code_hash = filebase64sha256("lambda_function_cloudtrail.zip")
#   environment {
#     variables = {
#       GOOGLE_CHAT_WEBHOOK_URL = var.CLOUDTRAIL_GOOGLE_CHAT_WEBHOOK_URL
#     }
#   }
#
#   tags = {
#     AlertFamily = "CloudtrailAlerts"
#     Environment = "prod"
#     Team        = "infra"
#     Product     = "compliance"
#   }
# }

# resource "aws_lambda_permission" "cloudtrail_alerts" {
#   statement_id  = var.lambda_permission_statement_id
#   action        = var.lambda_permission_action
#   function_name = aws_lambda_function.cloudtrail_alerts.function_name
#   principal     = var.lambda_permission_principal
#   source_arn    = aws_sns_topic.cloudtrail_alerts.arn
# }

# resource "aws_sns_topic_subscription" "cloudtrail_alerts" {
#   topic_arn = aws_sns_topic.cloudtrail_alerts.arn
#   protocol  = var.sns_topic_subscription_protocol
#   endpoint  = aws_lambda_function.cloudtrail_alerts.arn
# }

variable "alarm_namespace" {
  type    = string
  default = "LogMetrics_/aws/cloudtrail/logs"
}

variable "cloudtrail_alarm_tags" {
  type = map(string)
  default = {
    AlertFamily = "Cloudtrail-Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "compliance"
  }
}

variable "cloudtrail_log_group" {
  type    = string
  default = "/aws/cloudtrail/logs"
}

variable "disable_assumed_role_login_alerts" {
  type    = bool
  default = false
}

################# Unauthorized API Calls ##################
# resource "aws_cloudwatch_log_metric_filter" "unauthorized_api_calls" {
#
#   name    = "UnauthorizedAPICalls"
#   pattern = "{ ($.errorCode = \"*UnauthorizedOperation\") || ($.errorCode = \"AccessDenied*\") }"
#   #pattern = "{ ($.errorCode = \"*\") }"
#   log_group_name = "/aws/cloudtrail/logs"
#
#   metric_transformation {
#     name      = "UnauthorizedAPICalls"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "unauthorized_api_calls" {
#   alarm_name                = "UnauthorizedAPICalls"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.unauthorized_api_calls.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "10"
#   alarm_description         = "Monitoring unauthorized API calls will help reveal application errors and may reduce time to detect malicious activity."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "UnauthorizedAPICalls" })
# }
#
# ######################## Console Login without MFA ###################3
# resource "aws_cloudwatch_log_metric_filter" "no_mfa_console_signin_assumed_role" {
#
#   name           = "NoMFAConsoleSigninAssumedRole"
#   pattern        = "{ ($.eventName = \"ConsoleLogin\") && ($.additionalEventData.MFAUsed != \"Yes\") }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "NoMFAConsoleSigninAssumedRole"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_log_metric_filter" "no_mfa_console_signin_no_assumed_role" {
#
#   name           = "NoMFAConsoleSigninNoAssumedRole"
#   pattern        = "{ ($.eventName = \"ConsoleLogin\") && ($.additionalEventData.MFAUsed != \"Yes\") && ($.userIdentity.arn != \"*assumed-role*\") }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "NoMFAConsoleSigninNoAssumedRole"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "no_mfa_console_signin" {
#
#   alarm_name                = "NoMFAConsoleSignin"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = var.disable_assumed_role_login_alerts ? aws_cloudwatch_log_metric_filter.no_mfa_console_signin_no_assumed_role.id : aws_cloudwatch_log_metric_filter.no_mfa_console_signin_assumed_role.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring for single-factor console logins will increase visibility into accounts that are not protected by MFA."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "NoMFAConsoleSignin" })
# }
#
# ######################### Root Usage #######################
# resource "aws_cloudwatch_log_metric_filter" "root_usage" {
#
#   name           = "RootUsage"
#   pattern        = "{ $.userIdentity.type = \"Root\" && $.userIdentity.invokedBy NOT EXISTS && $.eventType != \"AwsServiceEvent\" }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "RootUsage"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "root_usage" {
#
#   alarm_name                = "RootUsage"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.root_usage.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring for root account logins will provide visibility into the use of a fully privileged account and an opportunity to reduce the use of it."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "RootUsage" })
# }
#
# ############################## IAM Changes ####################
# resource "aws_cloudwatch_log_metric_filter" "iam_changes" {
#
#   name           = "IAMChanges"
#   pattern        = "{($.eventName=DeleteGroupPolicy)||($.eventName=DeleteRolePolicy)||($.eventName=DeleteUserPolicy)||($.eventName=PutGroupPolicy)||($.eventName=PutRolePolicy)||($.eventName=PutUserPolicy)||($.eventName=CreatePolicy)||($.eventName=DeletePolicy)||($.eventName=CreatePolicyVersion)||($.eventName=DeletePolicyVersion)||($.eventName=AttachRolePolicy)||($.eventName=DetachRolePolicy)||($.eventName=AttachUserPolicy)||($.eventName=DetachUserPolicy)||($.eventName=AttachGroupPolicy)||($.eventName=DetachGroupPolicy)}"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "IAMChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "iam_changes" {
#
#   alarm_name                = "IAMChanges"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.iam_changes.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to IAM policies will help ensure authentication and authorization controls remain intact."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(
#     var.cloudtrail_alarm_tags, {
#       Name = "IAMChanges"
#     }
#   )
# }
#
# ######################## Cloudtrail Configuration Changes #########################3
# resource "aws_cloudwatch_log_metric_filter" "cloudtrail_cfg_changes" {
#
#   name           = "CloudTrailCfgChanges"
#   pattern        = "{ ($.eventName = CreateTrail) || ($.eventName = UpdateTrail) || ($.eventName = DeleteTrail) || ($.eventName = StartLogging) || ($.eventName = StopLogging) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "CloudTrailCfgChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "cloudtrail_cfg_changes" {
#
#   alarm_name                = "CloudTrailCfgChanges"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.cloudtrail_cfg_changes.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to CloudTrail's configuration will help ensure sustained visibility to activities performed in the AWS account."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "CloudTrailCfgChanges" })
# }
#
# ############################## Console Signin Failures ##########################3
# resource "aws_cloudwatch_log_metric_filter" "console_signin_failures" {
#
#   name           = "ConsoleSigninFailures"
#   pattern        = "{ ($.eventName = ConsoleLogin) && ($.errorMessage = \"Failed authentication\") }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "ConsoleSigninFailures"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "console_signin_failures" {
#
#   alarm_name                = "ConsoleSigninFailures"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.console_signin_failures.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring failed console logins may decrease lead time to detect an attempt to brute force a credential, which may provide an indicator, such as source IP, that can be used in other event correlation."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "ConsoleSigninFailures" })
# }
#
# ################ Disable or deleted Customer Managed Keys #############################
#
# resource "aws_cloudwatch_log_metric_filter" "disable_or_delete_cmk" {
#
#   name           = "DisableOrDeleteCMK"
#   pattern        = "{ ($.eventSource = kms.amazonaws.com) && (($.eventName = DisableKey) || ($.eventName = ScheduleKeyDeletion)) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "DisableOrDeleteCMK"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "disable_or_delete_cmk" {
#
#   alarm_name                = "DisableOrDeleteCMK"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.disable_or_delete_cmk.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Data encrypted with disabled or deleted keys will no longer be accessible."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "DisableOrDeleteCMK" })
# }
#
# ########################### S3 Bucket Policy Changes #########################3
# resource "aws_cloudwatch_log_metric_filter" "s3_bucket_policy_changes" {
#
#   name           = "S3BucketPolicyChanges"
#   pattern        = "{ ($.eventSource = s3.amazonaws.com) && (($.eventName = PutBucketAcl) || ($.eventName = PutBucketPolicy) || ($.eventName = PutBucketCors) || ($.eventName = PutBucketLifecycle) || ($.eventName = PutBucketReplication) || ($.eventName = DeleteBucketPolicy) || ($.eventName = DeleteBucketCors) || ($.eventName = DeleteBucketLifecycle) || ($.eventName = DeleteBucketReplication)) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "S3BucketPolicyChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "s3_bucket_policy_changes" {
#
#   alarm_name                = "S3BucketPolicyChanges"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.s3_bucket_policy_changes.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to S3 bucket policies may reduce time to detect and correct permissive policies on sensitive S3 buckets."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "S3BucketPolicyChanges" })
# }
#
#
# ######################### AWS Config Changes ###########################
#
# resource "aws_cloudwatch_log_metric_filter" "aws_config_changes" {
#   name           = "AWSConfigChanges"
#   pattern        = "{ ($.eventSource = config.amazonaws.com) && (($.eventName=StopConfigurationRecorder)||($.eventName=DeleteDeliveryChannel)||($.eventName=PutDeliveryChannel)||($.eventName=PutConfigurationRecorder)) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "AWSConfigChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "aws_config_changes" {
#
#   alarm_name                = "AWSConfigChanges"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.aws_config_changes.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to AWS Config configuration will help ensure sustained visibility of configuration items within the AWS account."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "AWSConfigChanges" })
# }
#
#
#
# ####################### Security Group Changes ###########################
#
# resource "aws_cloudwatch_log_metric_filter" "security_group_changes" {
#
#   name           = "SecurityGroupChanges"
#   pattern        = "{ ($.eventName = AuthorizeSecurityGroupIngress) || ($.eventName = AuthorizeSecurityGroupEgress) || ($.eventName = RevokeSecurityGroupIngress) || ($.eventName = RevokeSecurityGroupEgress) || ($.eventName = CreateSecurityGroup) || ($.eventName = DeleteSecurityGroup)}"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "SecurityGroupChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "security_group_changes" {
#
#   alarm_name                = "SecurityGroupChanges"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.security_group_changes.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to security group will help ensure that resources and services are not unintentionally exposed."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "SecurityGroupChanges" })
# }
#
#
# ############################### NACL Changes ##############################3
#
# resource "aws_cloudwatch_log_metric_filter" "nacl_changes" {
#
#   name           = "NACLChanges"
#   pattern        = "{ ($.eventName = CreateNetworkAcl) || ($.eventName = CreateNetworkAclEntry) || ($.eventName = DeleteNetworkAcl) || ($.eventName = DeleteNetworkAclEntry) || ($.eventName = ReplaceNetworkAclEntry) || ($.eventName = ReplaceNetworkAclAssociation) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "NACLChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "nacl_changes" {
#
#   alarm_name                = "NACLChanges"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.nacl_changes.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to NACLs will help ensure that AWS resources and services are not unintentionally exposed."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "NACLChanges" })
# }
#
#
# ############################# Network Gateway Changes ###############################
#
# resource "aws_cloudwatch_log_metric_filter" "network_gw_changes" {
#
#   name           = "NetworkGWChanges"
#   pattern        = "{ ($.eventName = CreateCustomerGateway) || ($.eventName = DeleteCustomerGateway) || ($.eventName = AttachInternetGateway) || ($.eventName = CreateInternetGateway) || ($.eventName = DeleteInternetGateway) || ($.eventName = DetachInternetGateway) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "NetworkGWChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "network_gw_changes" {
#
#   alarm_name          = "NetworkGWChanges"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = "1"
#   metric_name         = aws_cloudwatch_log_metric_filter.network_gw_changes.id
#
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to network gateways will help ensure that all ingress/egress traffic traverses the VPC border via a controlled path."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "NetworkGWChanges" })
# }
#
#
# ################################# Route Table Changes ##############################
# resource "aws_cloudwatch_log_metric_filter" "route_table_changes" {
#
#   name           = "RouteTableChanges"
#   pattern        = "{ ($.eventName = CreateRoute) || ($.eventName = CreateRouteTable) || ($.eventName = ReplaceRoute) || ($.eventName = ReplaceRouteTableAssociation) || ($.eventName = DeleteRouteTable) || ($.eventName = DeleteRoute) || ($.eventName = DisassociateRouteTable) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "RouteTableChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "route_table_changes" {
#
#   alarm_name                = "RouteTableChanges"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.route_table_changes.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to route tables will help ensure that all VPC traffic flows through an expected path."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "RouteTableChanges" })
# }
#
#
# #################### VPC Changes #######################
# resource "aws_cloudwatch_log_metric_filter" "vpc_changes" {
#
#   name           = "VPCChanges"
#   pattern        = "{ ($.eventName = CreateVpc) || ($.eventName = DeleteVpc) || ($.eventName = ModifyVpcAttribute) || ($.eventName = AcceptVpcPeeringConnection) || ($.eventName = CreateVpcPeeringConnection) || ($.eventName = DeleteVpcPeeringConnection) || ($.eventName = RejectVpcPeeringConnection) || ($.eventName = AttachClassicLinkVpc) || ($.eventName = DetachClassicLinkVpc) || ($.eventName = DisableVpcClassicLink) || ($.eventName = EnableVpcClassicLink) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "VPCChanges"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "vpc_changes" {
#
#   alarm_name                = "VPCChanges"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.vpc_changes.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Monitoring changes to VPC will help ensure that all VPC traffic flows through an expected path."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "VPCChanges" })
# }
#
# ########################## EC2 Instance Event Count #######################
# resource "aws_cloudwatch_log_metric_filter" "ec2_instance_event_count" {
#
#   name           = "EC2InstanceEventCount"
#   pattern        = "{ ($.eventName = RunInstances) || ($.eventName = RebootInstances) || ($.eventName = StartInstances) || ($.eventName = StopInstances) || ($.eventName = TerminateInstances) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "EC2InstanceEventCount"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "ec2_instance_event_count" {
#
#   alarm_name                = "EC2InstanceEventCount"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.ec2_instance_event_count.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Alarms when an API call is made to create, terminate, start, stop or reboot an EC2 instance."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "EC2InstanceEventCount" })
# }
#
# ################################ EC2 Large Instance Event Count ##################
# resource "aws_cloudwatch_log_metric_filter" "ec2_large_instance_event_count" {
#
#   name           = "EC2LargeInstanceEventCount"
#   pattern        = "{ ($.eventName = RunInstances) && (($.requestParameters.instanceType = *.8xlarge) || ($.requestParameters.instanceType = *.4xlarge) || ($.requestParameters.instanceType = *.16xlarge) || ($.requestParameters.instanceType = *.10xlarge) || ($.requestParameters.instanceType = *.12xlarge) || ($.requestParameters.instanceType = *.24xlarge)) }"
#   log_group_name = var.cloudtrail_log_group
#
#   metric_transformation {
#     name      = "EC2LargeInstanceEventCount"
#     namespace = var.alarm_namespace
#     value     = "1"
#   }
# }
#
# resource "aws_cloudwatch_metric_alarm" "ec2_large_instance_event_count" {
#
#   alarm_name                = "EC2LargeInstanceEventCount"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "1"
#   metric_name               = aws_cloudwatch_log_metric_filter.ec2_large_instance_event_count.id
#   namespace                 = var.alarm_namespace
#   period                    = "300"
#   statistic                 = "Sum"
#   threshold                 = "1"
#   alarm_description         = "Alarms when an API call is made to create, terminate, start, stop or reboot a 4x-large or greater EC2 instance."
#   alarm_actions             = [aws_sns_topic.cloudtrail_alerts.arn]
#   treat_missing_data        = "notBreaching"
#   insufficient_data_actions = []
#
#   tags = merge(var.cloudtrail_alarm_tags, { Name = "EC2LargeInstanceEventCount" })
# }

################## Unexpected IP Access #########################
# variable "admin_whitelist" {
# type = "list"
# default = [
# ]
# }

# data "template_file" "ip_rule" {
#  template = "{$.userIdentity.type = IAMUser && $.sourceIPAddress != *amazonaws.com && $.sourceIPAddress != AWS* && $.sourceIPAddress != $${ips} }"
#  vars {
#    ips = "${join(" && $.sourceIPAddress != ", var.admin_whitelist)}"
#  }
# }

# resource "aws_cloudwatch_log_metric_filter" "unexpected-ip-access" {
#  name = "${var.environment_name}.unexpected-ip-access"
#  pattern = "${data.template_file.ip_rule.rendered}"
#  log_group_name = "${var.cloudtrail_log_group}"

#  metric_transformation {
#   name = "UnexpectedIPAccessEventCount"
#    namespace = "CloudTrailMetrics"
#    value = "1"
#  }
# }

# resource "aws_cloudwatch_metric_alarm" "unexpected-ip-access-alarm" {
#  alarm_name = "${var.environment_name}.unexpected-ip-access-alarm"
#  comparison_operator = "GreaterThanOrEqualToThreshold"
#  evaluation_periods = "1"
#  metric_name = "UnexpectedIPAccessEventCount"
# namespace = "CloudTrailMetrics"
#  period = "300"
#  statistic = "Sum"
#  threshold = "1"
#  alarm_description = "Alarms on access from unexpected IP addresses"
#  insufficient_data_actions = []
#  alarm_actions = ["${var.alarm_actions}"]
# }

#################### Organization Changes ####################
# {
#     name                = "OrganizationChanges"
#     description         = "Tracks organization changes made in the master AWS Account."
#     pattern             = "{ ($.eventSource = organizations.amazonaws.com) && (($.eventName = AcceptHandshake) || ($.eventName = AttachPolicy) ||($.eventName = CreateAccount) || ($.eventName = CreateOrganizationalUnit)|| ($.eventName = CreatePolicy) || ($.eventName = DeclineHandshake) ||($.eventName = DeleteOrganization) || ($.eventName = DeleteOrganizationalUnit) || ($.eventName = DeletePolicy) || ($.eventName = DetachPolicy) || ($.eventName = DisablePolicyType) || ($.eventName = EnablePolicyType) || ($.eventName = InviteAccountToOrganization) || ($.eventName = LeaveOrganization) || ($.eventName = MoveAccount) || ($.eventName = RemoveAccountFromOrganization) || ($.eventName = UpdatePolicy) || ($.eventName = UpdateOrganizationalUnit)) }"
#     evaluation_periods  = 1
#     threshold           = 1
#     period              = 300
#     comparison_operator = "GreaterThanOrEqualToThreshold"
#     statistic           = "Sum"
# }

##
