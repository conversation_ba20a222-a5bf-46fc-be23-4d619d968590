module "nat-1" {
  source        = "../modules/nat"
  region        = var.region
  subnet_id     = module.main_public_subnet_1.subnet_id
  allocation_id = aws_eip.nat1_eip.id

  tags = var.nat_gateway_1_tags
}

module "nat-2" {
  source        = "../modules/nat"
  region        = var.region
  subnet_id     = module.main_public_subnet_2.subnet_id
  allocation_id = aws_eip.nat2_eip.id
  tags          = var.nat_gateway_2_tags
}

module "nat-3" {
  source        = "../modules/nat"
  region        = var.region
  subnet_id     = module.main_public_subnet_3.subnet_id
  allocation_id = aws_eip.nat3_eip.id
  tags          = var.nat_gateway_3_tags
}
