resource "aws_security_group" "wazuh_sg" {
  description = "SG for prod wazuh instance"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    description = "Agent connection service - TCP"
    from_port   = 1514
    to_port     = 1514
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "Agent connection service - UDP"
    from_port   = 1514
    to_port     = 1514
    protocol    = "udp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "Agent enrollment service"
    from_port   = 1515
    to_port     = 1515
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "Wazuh cluster daemon"
    from_port   = 1516
    to_port     = 1516
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "Wazuh syslog collector (UDP)"
    from_port   = 514
    to_port     = 514
    protocol    = "udp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "Wazuh cluster daemon (TCP)"
    from_port   = 514
    to_port     = 514
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "wazuh server RESTful API"
    from_port   = 55000
    to_port     = 55000
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "wazuh indexer RESTful API"
    from_port   = 9200
    to_port     = 9200
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "wazuh indexer cluster communication"
    from_port   = 9300
    to_port     = 9400
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "wazuh web user interface"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "wazuh web user interface"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  name = "wazuh_sg"
  tags = {
    Name        = "wazuh-security-group"
    Team        = "infra"
    Product     = "compliance"
    Environment = "prod"
  }
  vpc_id = module.vpc_main.vpc_id
}

module "wazuh" {
  source                        = "../modules/ec2_instance"
  region                        = var.region
  associate_public_ip_address   = false
  availability_zone             = "ap-south-1c"
  instance_type                 = "c6a.2xlarge"
  root_block_device_volume_size = 100
  root_block_device_iops        = 3000
  root_block_device_throughput  = 125
  root_block_device_volume_type = "gp3"
  root_block_device_encrypted   = true
  subnet_id                     = module.main_private_subnet_3.subnet_id
  vpc_security_group_ids        = ["${aws_security_group.wazuh_sg.id}"]
  key_name                      = "wazuh-ssh-key"
  monitoring                    = true

  service_discovery_service_name = "wazuh"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  iam_instance_profile = aws_iam_instance_profile.wazuh_iam_profile.name
  tags = {
    Name        = "wazuh-ec2-instance"
    Team        = "infra"
    Product     = "compliance"
    Environment = "prod"
  }
}

resource "aws_iam_role" "wazuh_iam_role" {
  name = "WazuhProdIAMRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = [
            "ec2.amazonaws.com",
            "ecs.amazonaws.com",
            "s3.amazonaws.com",
            "cloudfront.amazonaws.com",
            "logs.amazonaws.com",
            "route53.amazonaws.com",
            "secretsmanager.amazonaws.com",
            "kms.amazonaws.com",
            "cloudtrail.amazonaws.com",
            "acm.amazonaws.com"
          ]
        }
      }
    ]
  })
}

# Create an IAM policy for the EC2 instance
resource "aws_iam_policy" "wazuh_iam_policy" {
  name        = "WazuhProdIAMPolicy"
  path        = "/"
  description = "IAM policy for prod Wazuh instance to running scripts and terraform"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:UpdateService",
          "ecs:Describe*",
          "ecs:RunTask",
          "ecs:DescribeTaskDefinition",
          "ec2:DescribeVpcs",
          "ec2:DescribeSubnets",
          "ec2:DescribeSecurityGroups",
          "ecs:TagResource"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = ["*"]
      },
      {
        Effect = "Allow"
        Action = [
          "iam:PassRole"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_policy" "wazuh_get_state_iam_policy" {
  name        = "WazuhProdReadIAMPolicy"
  path        = "/"
  description = "IAM policy for Prod Wazuh instance for read permissions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:Describe*",
          "ecs:TagResource",
          "ecs:DeregisterTaskDefinition",
          "ecs:RegisterTaskDefinition",

          # EC2
          "ec2:Describe*",

          # IAM
          "iam:Get*",
          "iam:List*",

          # Cloudfront
          "cloudfront:Get*",
          "cloudfront:List*",

          # Logs
          "logs:Describe*",
          "logs:List*",

          # Route 53
          "route53:Get*",
          "route53:List*",

          # S3
          "s3:HeadBucket",
          "s3:Get*",
          "s3:List*",

          # Secretsmanager
          "secretsmanager:Describe*",
          "secretsmanager:Get*",

          # KMS
          "kms:Describe*",
          "kms:Get*",
          "kms:List*",
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",

          # Service Discovery
          "servicediscovery:Get*",
          "servicediscovery:List*",

          # Elastic Load Balancing
          "elasticloadbalancing:Describe*",

          # RDS
          "rds:Describe*",

          # Cloudtrail
          "cloudtrail:Get*",
          "cloudtrail:List*",
          "cloudtrail:Describe*",
          "cloudtrail:LookupEvents",
          "cloudtrail:AddTags",

          # ACM
          "acm:ExportCertificate",
          "acm:Describe*",
          "acm:Get*",
          "acm:List*",

          # Cloudwatch
          "logs:*"
        ]
        Resource = "*",
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
        ]
        Resource = [
          "*"
        ]

      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          "*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "wazuh_policy_attachment" {
  policy_arn = aws_iam_policy.wazuh_iam_policy.arn
  role       = aws_iam_role.wazuh_iam_role.name
}

resource "aws_iam_role_policy_attachment" "wazuh_get_state_iam_policy_attachment" {
  policy_arn = aws_iam_policy.wazuh_get_state_iam_policy.arn
  role       = aws_iam_role.wazuh_iam_role.name
}

resource "aws_iam_instance_profile" "wazuh_iam_profile" {
  name = "WazuhProdIAMProfile"
  role = aws_iam_role.wazuh_iam_role.name
}
