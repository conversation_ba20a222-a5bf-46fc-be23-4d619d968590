resource "aws_s3_bucket" "assets" {
  bucket = "ravenmail-assets-public-prod"

  tags = {
    Name        = "assets-public-prod"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_s3_bucket_versioning" "assets" {
  bucket = aws_s3_bucket.assets.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "assets" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.assets]

  bucket = aws_s3_bucket.assets.id

  rule {
    id = "lifecycle_configuration"

    noncurrent_version_expiration {
      noncurrent_days = 90
    }

    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "assets" {
  bucket = aws_s3_bucket.assets.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "assets" {
  depends_on = [aws_s3_bucket_ownership_controls.assets]

  bucket = aws_s3_bucket.assets.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "assets" {
  bucket = aws_s3_bucket.assets.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_website_configuration" "assets" {
  bucket = aws_s3_bucket.assets.id

  # redirect_all_requests_to {
  #   host_name = "assets.ravenmail.io"
  #   protocol = "https"
  # }

  index_document {
    suffix = "logo.svg"
  }

  error_document {
    key = "logo.svg"
  }

}

resource "aws_s3_bucket_policy" "assets" {
  bucket = aws_s3_bucket.assets.id

  policy = jsonencode({
    "Version" : "2008-10-17",
    "Id" : "PolicyForCloudFrontPrivateContent",
    "Statement" : [
      {
        "Sid" : "AllowCloudFrontServicePrincipal",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudfront.amazonaws.com"
        },
        "Action" : "s3:GetObject",
        "Resource" : "${aws_s3_bucket.assets.arn}/*",
        "Condition" : {
          "StringEquals" : {
            "AWS:SourceArn" : "arn:aws:cloudfront::771151923073:distribution/E9FR69G8LQASA"
          }
        }
      }
    ]
  })
}

resource "aws_s3_bucket_server_side_encryption_configuration" "assets" {
  bucket = aws_s3_bucket.assets.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.assets.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

##################  Cloudfront #############################
resource "aws_cloudfront_origin_access_identity" "assets" {
  comment = "Assets - Cloudfront Origin Access Prod Identity"
}

resource "aws_cloudfront_cache_policy" "assets" {
  name        = "assets-s3-cache-policy"
  comment     = "Assets S3 Cache policy for cloud distribution"
  default_ttl = 50
  max_ttl     = 100
  min_ttl     = 1
  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "none"
    }
    headers_config {
      header_behavior = "whitelist"
      headers {
        items = ["Origin", "Content-Type"] # Specify the headers to whitelist
      }
    }
    query_strings_config {
      query_string_behavior = "none"
    }

    enable_accept_encoding_gzip   = true
    enable_accept_encoding_brotli = true
  }
}

resource "aws_cloudfront_distribution" "assets_s3_distribution" {
  origin {
    domain_name = aws_s3_bucket.assets.bucket_regional_domain_name

    origin_access_control_id = aws_cloudfront_origin_access_control.assets.id

    origin_id = aws_s3_bucket.assets.id
  }

  logging_config {
    include_cookies = true
    bucket          = aws_s3_bucket.logs.bucket_domain_name
    prefix          = "cloudfront/assets/stdlogs/"
  }

  enabled         = true
  is_ipv6_enabled = false
  comment         = "Assets Static Cloudfront Distribution"
  # default_root_object = "logo.svg"
  aliases = ["assets.ravenmail.io"]

  default_cache_behavior {
    allowed_methods            = ["GET", "HEAD"]
    cached_methods             = ["GET", "HEAD"]
    target_origin_id           = aws_s3_bucket.assets.id
    cache_policy_id            = aws_cloudfront_cache_policy.assets.id
    response_headers_policy_id = aws_cloudfront_response_headers_policy.assets_no_cache.id
    realtime_log_config_arn    = aws_cloudfront_realtime_log_config.assets_real_time_logs.arn

    # forwarded_values {
    #   query_string = false

    #   cookies {
    #     forward = "none"
    #   }
    #   headers = ["Origin", "Content-Type", "Accept-Encoding"]
    # }

    compress = true

    viewer_protocol_policy = "https-only"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
  }

  custom_error_response {
    error_code            = 403
    response_code         = 200
    response_page_path    = "/"
    error_caching_min_ttl = 0
  }

  custom_error_response {
    error_code            = 404
    response_code         = 200
    response_page_path    = "/"
    error_caching_min_ttl = 0
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
      locations        = []
    }
  }

  tags = {
    Name        = "cloudfront-assets"
    Team        = "infra"
    Environment = "prod"
    Product     = "ravenclaw"
  }

  price_class = "PriceClass_100"

  viewer_certificate {
    # cloudfront_default_certificate = true
    acm_certificate_arn      = "arn:aws:acm:us-east-1:771151923073:certificate/d4e63ee6-f71e-405c-b352-2fb053605c2e"
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2018"
  }

  depends_on = [aws_s3_bucket_policy.assets]

  lifecycle {
    ignore_changes = [
      default_cache_behavior[0].default_ttl,
      default_cache_behavior[0].max_ttl,
    ]
  }

  web_acl_id = "arn:aws:wafv2:us-east-1:771151923073:global/webacl/web-acl-cloudfront/f1851ce1-b043-4498-83a7-3fbe9537227b"
}

resource "aws_cloudfront_response_headers_policy" "assets_no_cache" {
  name    = "NoCacheProdPolicyForIndexAssets"
  comment = "Set Cache-Control: no-cache for index.html"

  security_headers_config {
    # Configure the HSTS header (Strict-Transport-Security)
    strict_transport_security {
      override                   = true
      access_control_max_age_sec = 63072000 # 2 years in seconds
      include_subdomains         = true
      preload                    = true
    }
    # Optionally, add additional security headers like X-Content-Type-Options, etc.
    content_type_options {
      override = true
    }
    frame_options {
      override     = true
      frame_option = "SAMEORIGIN"
    }
    referrer_policy {
      override        = true
      referrer_policy = "same-origin"
    }
    xss_protection {
      override   = true
      protection = true
      mode_block = true
    }
  }

  custom_headers_config {
    items {
      header   = "Cache-Control"
      value    = "no-cache, no-store, private, must-revalidate, pre-check=0, post-check=0, max-age=0, s-maxage=0"
      override = true
    }
    items {
      header   = "Expires"
      override = true
      value    = "0"
    }
    items {
      header   = "Pragma"
      override = true
      value    = "no-cache"
    }
  }

  remove_headers_config {
    items {
      header = "x-amz-server-side-encryption-aws-kms-key-id"
    }

    items {
      header = "X-Amz-Server-Side-Encryption"
    }

    items {
      header = "X-Amz-Server-Side-Encryption-Bucket-Key-Enabled"
    }

    items {
      header = "Etag"
    }

    items {
      header = "Server"
    }

    items {
      header = "X-Amz-Version-id"
    }
  }
}

resource "aws_cloudfront_origin_access_control" "assets" {
  name                              = "assets-prod-kms-oac"
  description                       = "Assets kms encrypted oac access"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

resource "aws_cloudfront_monitoring_subscription" "assets" {
  distribution_id = aws_cloudfront_distribution.assets_s3_distribution.id

  monitoring_subscription {
    realtime_metrics_subscription_config {
      realtime_metrics_subscription_status = "Enabled"
    }
  }
}

######################### Route 53 configuration ######################3
resource "aws_route53_zone" "assets" {
  name = "assets.ravenmail.io"

  tags = {
    Name        = "ravenclaw-prod-assets-route53"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_route53_record" "assets" {
  zone_id = aws_route53_zone.assets.zone_id
  name    = "assets.ravenmail.io"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.assets_s3_distribution.domain_name
    zone_id                = aws_cloudfront_distribution.assets_s3_distribution.hosted_zone_id
    evaluate_target_health = false
  }

}
