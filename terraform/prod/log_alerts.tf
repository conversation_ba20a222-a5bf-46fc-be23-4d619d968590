resource "aws_sns_topic" "main" {
  name = var.sns_topic_name
  tags = {
    Name        = var.sns_topic_name
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic" "inline_alerts" {
  name = "INLINE_ALERTS_SNS_TOPIC"
  tags = {
    Name        = "INLINE_ALERTS_SNS_TOPIC"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_iam_role" "lambda_exec" {
  name = "lambda_exec_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Sid    = ""
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      },
    ]
  })
}

resource "aws_iam_policy" "lambda_logs_policy" {
  name        = "lambda_logs_policy"
  description = "Policy to allow Lambda to read logs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams",
          "logs:GetLogEvents",
          "logs:FilterLogEvents"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_logs_attachment" {
  role       = aws_iam_role.lambda_exec.name
  policy_arn = aws_iam_policy.lambda_logs_policy.arn
}

resource "aws_lambda_function" "main" {
  filename         = var.lambda_filename
  function_name    = var.lambda_function_name
  handler          = var.lambda_handler
  runtime          = var.lambda_runtime
  role             = aws_iam_role.lambda_exec.arn
  timeout          = 900
  source_code_hash = filebase64sha256(var.lambda_filename)
  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.LOG_ALERTS_GCHAT_URL
    }
  }

  tags = {
    Name        = var.lambda_function_name
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_lambda_function" "inline_alerts" {
  filename         = var.lambda_filename
  function_name    = "sendInlineAlert"
  handler          = var.lambda_handler
  runtime          = var.lambda_runtime
  role             = aws_iam_role.lambda_exec.arn
  source_code_hash = filebase64sha256(var.lambda_filename)
  timeout          = 900
  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.INLINE_LOG_ALERTS_GCHAT_URL
    }
  }

  tags = {
    Name        = "sendInlineAlert"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_iam_role_policy_attachment" "lambda_policy" {
  role       = aws_iam_role.lambda_exec.name
  policy_arn = var.lambda_policy_arn
}

resource "aws_lambda_permission" "allow_cloudwatch" {
  statement_id  = var.lambda_permission_statement_id
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.main.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.main.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_inline" {
  statement_id  = var.lambda_permission_statement_id
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.inline_alerts.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.inline_alerts.arn
}

resource "aws_sns_topic_subscription" "main" {
  topic_arn = aws_sns_topic.main.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.main.arn
}

resource "aws_sns_topic_subscription" "inline_alerts" {
  topic_arn = aws_sns_topic.inline_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.inline_alerts.arn
}

module "keycloak_error_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.keycloak_logs_group
  log_filter_name            = "keycloak_error_log_alarm"
  log_pattern                = "[date, time, status=ERROR*]"
  alarm_name                 = "keycloak_error_log_alarm"
  sns_topic_arn              = aws_sns_topic.main.arn
  metric_transformation_name = "KEYCLOAK_ERROR_LOG_COUNT"
}

module "keycloak_fatal_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.keycloak_logs_group
  log_filter_name            = "keycloak_fatal_log_alarm"
  log_pattern                = "[date, time, status=FATAL*]"
  alarm_name                 = "keycloak_fatal_log_alarm"
  sns_topic_arn              = aws_sns_topic.main.arn
  metric_transformation_name = "KEYCLOAK_FATAL_LOG_COUNT"
}

module "inline_error_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.inline_log_group
  log_filter_name            = "inline_error_log_alarm"
  log_pattern                = "[timestamp, level=ERROR*]"
  alarm_name                 = "inline_error_log_alarm"
  sns_topic_arn              = aws_sns_topic.inline_alerts.arn
  metric_transformation_name = "INLINE_ERROR_LOG_COUNT"
}

module "inline_fatal_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.inline_log_group
  log_filter_name            = "inline_fatal_log_alarm"
  log_pattern                = "[timestamp, level=FATAL*]"
  alarm_name                 = "inline_fatal_log_alarm"
  sns_topic_arn              = aws_sns_topic.inline_alerts.arn
  metric_transformation_name = "INLINE_FATAL_LOG_COUNT"
}

module "inline_lambda_timedout_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = "/aws/lambda/sendInlineAlert"
  log_filter_name            = "inline_lambda_timedout_log_alarm"
  log_pattern                = "timed out"
  alarm_name                 = "inline_lambda_timedout_alarm"
  sns_topic_arn              = aws_sns_topic.inline_alerts.arn
  metric_transformation_name = "INLINE_LAMBDA_TIMEDOUT_LOG_COUNT"
}



module "waf_ravenclaw_alb_endpoint_block_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = "aws-waf-logs-ravenclaw-alb-acl"
  log_filter_name            = "waf_ravenclaw_alb_endpoint_block_alarm"
  log_pattern                = "{ $.action = \"BLOCK\" &&  ($.httpRequest.uri = \"/v0/api/*\" || $.httpRequest.uri = \"/v0/hooks/*\" ) && $.httpSourceId = \"*ecs-ravenclaw-alb*\" }"
  alarm_name                 = "waf_ravenclaw_alb_endpoint_block_alarm"
  sns_topic_arn              = aws_sns_topic.main.arn
  metric_transformation_name = "WAF_RAVENCLAW_ALB_ENDPOINT_BLOCK"
  alarm_metric_threshold     = 5
  alarm_metric_period        = 180
}

module "waf_keycloak_alb_endpoint_block_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = "aws-waf-logs-alb-acl"
  log_filter_name            = "waf_keycloak_alb_endpoint_block_alarm"
  log_pattern                = "{ $.action = \"BLOCK\" && $.httpSourceId = \"*ecs-keycloak-alb*\" && $.httpRequest.host = \"auth.ravenmail.io\"  }"
  alarm_name                 = "waf_keycloak_alb_endpoint_block_alarm"
  sns_topic_arn              = aws_sns_topic.main.arn
  metric_transformation_name = "WAF_KEYCLOAK_ALB_ENDPOINT_BLOCK"
  alarm_metric_threshold     = 5
  alarm_metric_period        = 180
}

module "waf_internal_alb_endpoint_block_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = "aws-waf-logs-alb-acl"
  log_filter_name            = "waf_internal_alb_endpoint_block_alarm"
  log_pattern                = "{ $.action = \"BLOCK\" && $.httpSourceId = \"*prod-alb*\" }"
  alarm_name                 = "waf_internal_alb_endpoint_block_alarm"
  sns_topic_arn              = aws_sns_topic.main.arn
  metric_transformation_name = "WAF_INTERNAL_ALB_ENDPOINT_BLOCK"
  alarm_metric_threshold     = 5
  alarm_metric_period        = 180

}

# module "vpc_flow_logs_nlb_reject_alerts" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/vpc/flow-logs"
#   log_filter_name            = "vpc_flow_logs_nlb_reject_alerts"
#   log_pattern                = "{ ($.interface-id = \"eni-0bef6d68f5eb4abb5\" || $.interface-id = \"eni-06ce650eb63e2f7bf\" || $.interface-id = \"eni-0f3d8d304314ad195\" ) && $.action = \"REJECT\" &&  $.flow-direction = \"ingress\"  && ( $.dstport = \"25\" || $.dstport=\"587\") }"
#   alarm_name                 = "vpc_flow_logs_nlb_reject_alerts"
#   sns_topic_arn              = aws_sns_topic.inline_alerts.arn
#   metric_transformation_name = "VPC_FLOW_LOGS_NLB_REJECT_ALERTS"
# }



# module "ecs_log_lambda_trigger_error_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/lambda/sendAlert"
#   log_filter_name            = "ecs_log_lambda_trigger_error_alarm"
#   log_pattern                = "?error ?ERROR ?Error"
#   alarm_name                 = "ecs_log_lambda_trigger_error_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "ECS_LOG_LAMBDA_TRIGGER_ERROR_ALARM"
# }

# module "ravenclaw_alb_log_lambda_trigger_error_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/lambda/alb-access-logs-to-cloudwatch"
#   log_filter_name            = "ravenclaw_alb_log_lambda_trigger_error_alarm"
#   log_pattern                = "?error ?ERROR ?Error"
#   alarm_name                 = "ravenclaw_alb_log_lambda_trigger_error_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "RAVENCLAW_ALB_LOG_LAMBDA_TRIGGER_ERROR_ALARM"
# }

# module "keycloak_alb_log_lambda_trigger_error_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/lambda/keycloak-alb-access-logs-to-cloudwatch"
#   log_filter_name            = "keycloak_alb_log_lambda_trigger_error_alarm"
#   log_pattern                = "?error ?ERROR ?Error"
#   alarm_name                 = "keycloak_alb_log_lambda_trigger_error_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "KEYCLOAK_ALB_LOG_LAMBDA_TRIGGER_ERROR_ALARM"
# }

# module "internal_alb_log_lambda_trigger_error_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/lambda/internal-alb-access-logs-to-cloudwatch"
#   log_filter_name            = "internal_alb_log_lambda_trigger_error_alarm"
#   log_pattern                = "?error ?ERROR ?Error"
#   alarm_name                 = "internal_alb_log_lambda_trigger_error_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "INTERNAL_ALB_LOG_LAMBDA_TRIGGER_ERROR_ALARM"
# }

# module "ingestion_task_panic_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = var.ingestion_logs_group
#   log_filter_name            = "ingestion_panic_log_alarm"
#   log_pattern                = "panic"
#   alarm_name                 = "ingestion_panic_log_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "INGESTION_PANIC_COUNT"
# }

# module "ti_go_service_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = var.ti_go_service_logs_group
#   log_filter_name            = var.ti_go_service_log_filter_name
#   log_pattern                = "[timestamp, level=ERROR*]"
#   alarm_name                 = var.ti_go_service_log_alarm_name
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = var.ti_go_service_metric_transformation_name
# }

# module "setup_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = var.setup_logs_group
#   log_filter_name            = var.setup_log_filter_name
#   log_pattern                = "[timestamp, level=ERROR*]"
#   alarm_name                 = var.setup_log_alarm_name
#   metric_transformation_name = var.setup_metric_transformation_name
#   sns_topic_arn              = aws_sns_topic.main.arn
# }

# module "nats_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = var.nats_logs_group
#   log_filter_name            = var.nats_log_filter_name
#   log_pattern                = var.log_pattern
#   alarm_name                 = var.nats_log_alarm_name
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = var.nats_metric_transformation_name
# }

# module "nats_node_1_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/ec2/natsNode1"
#   log_filter_name            = "nats_node_1_error_log_alarm"
#   log_pattern                = var.log_pattern
#   alarm_name                 = "nats_node_1_log_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "NATS_NODE_1_ERROR_COUNT"
# }

# module "nats_node_2_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/ec2/natsNode2"
#   log_filter_name            = "nats_node_2_error_log_alarm"
#   log_pattern                = var.log_pattern
#   alarm_name                 = "nats_node_2_log_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "NATS_NODE_2_ERROR_COUNT"
# }

# module "nats_node_3_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/ec2/natsNode3"
#   log_filter_name            = "nats_node_3_error_log_alarm"
#   log_pattern                = var.log_pattern
#   alarm_name                 = "nats_node_3_log_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "NATS_NODE_3_ERROR_COUNT"
# }

# module "nats_node_4_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/ec2/natsNode4"
#   log_filter_name            = "nats_node_4_error_log_alarm"
#   log_pattern                = var.log_pattern
#   alarm_name                 = "nats_node_4_log_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "NATS_NODE_4_ERROR_COUNT"
# }

# module "ml_inference_service_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = var.ml_inference_logs_group
#   log_filter_name            = var.ml_inference_log_filter_name
#   log_pattern                = var.log_pattern
#   alarm_name                 = var.ml_inference_log_alarm_name
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = var.ml_inference_metric_transformation_name
# }

# module "ingestion_task_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = var.ingestion_logs_group
#   log_filter_name            = var.ingestion_log_filter_name
#   log_pattern                = "[timestamp, level=ERROR*]"
#   alarm_name                 = var.ingestion_log_alarm_name
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = var.ingestion_metric_transformation_name
# }

# module "log_lambda_trigger_error_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/lambda/sendAlert"
#   log_filter_name            = "log_lambda_trigger_error_alarm"
#   log_pattern                = "?error ?ERROR ?Error"
#   alarm_name                 = "log_lambda_trigger_error_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "LOG_LAMBDA_TRIGGER_ERROR_ALARM"
# }

# module "system_metric_lambda_trigger_error_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/lambda/SnsToGoogleChat"
#   log_filter_name            = "system_metric_lambda_trigger_error_alarm"
#   log_pattern                = "?error ?ERROR ?Error"
#   alarm_name                 = "system_metric_lambda_trigger_error_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "SYSTEM_METRIC_LAMBDA_TRIGGER_ERROR_ALARM"
# }

# module "ravenclaw_rds_log_error_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/rds/instance/ravenclaw/postgresql"
#   log_filter_name            = "ravenclaw_rds_log_error_alarm"
#   log_pattern                = "?error ?ERROR ?Error"
#   alarm_name                 = "ravenclaw_rds_log_error_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "RAVENCLAW_RDS_LOG_ERROR_ALARM"
# }

# module "keycloak_rds_log_error_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = "/aws/rds/instance/keycloak/postgresql"
#   log_filter_name            = "keycloak_rds_log_error_alarm"
#   log_pattern                = "?error ?ERROR ?Error"
#   alarm_name                 = "keycloak_rds_log_error_alarm"
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = "KEYCLOAK_RDS_LOG_ERROR_ALARM"
# }

# module "remediator_log_alert" {
#   source                     = "../modules/log_alerting"
#   region                     = var.region
#   log_group_name             = var.remediator_logs_group
#   log_filter_name            = var.remediator_log_filter_name
#   log_pattern                = "[timestamp, level=ERROR*]"
#   alarm_name                 = var.remediator_log_alarm_name
#   sns_topic_arn              = aws_sns_topic.main.arn
#   metric_transformation_name = var.remediator_metric_transformation_name
# }
