# bff
variable "bff_service_max_capacity" {
  type = number
}

variable "bff_service_min_capacity" {
  type = number
}

variable "bff_service_memory_autoscale_target_value" {
  type = number
}

variable "bff_service_cpu_autoscale_target_value" {
  type = number
}

# gateway
variable "gateway_service_max_capacity" {
  type = number
}

variable "gateway_service_min_capacity" {
  type = number
}

variable "gateway_service_memory_autoscale_target_value" {
  type = number
}

variable "gateway_service_cpu_autoscale_target_value" {
  type = number
}

# remediator
variable "remediator_service_max_capacity" {
  type = number
}

variable "remediator_service_min_capacity" {
  type = number
}

variable "remediator_service_memory_autoscale_target_value" {
  type = number
}

variable "remediator_service_cpu_autoscale_target_value" {
  type = number
}

# keycloak
variable "keycloak_max_capacity" {
  type = number
}

variable "keycloak_min_capacity" {
  type = number
}

variable "keycloak_memory_autoscale_target_value" {
  type = number
}

variable "keycloak_cpu_autoscale_target_value" {
  type = number
}

variable "ml_inference_max_capacity" {
  type = number
}

variable "ml_inference_min_capacity" {
  type = number
}

variable "ml_inference_memory_autoscale_target_value" {
  type = number
}

variable "ml_inference_cpu_autoscale_target_value" {
  type = number
}

variable "ti_go_service_max_capacity" {
  type = number
}

variable "ti_go_service_min_capacity" {
  type = number
}

variable "ti_go_service_memory_autoscale_target_value" {
  type = number
}

variable "ti_go_service_cpu_autoscale_target_value" {
  type = number
}

variable "inline_service_cpu_autoscale_target_value" {
  type = number
}

variable "inline_service_memory_autoscale_target_value" {
  type = number
}

variable "inline_max_autoscaling_capacity" {
  type = number
}

variable "inline_min_autoscaling_capacity" {
  type = number
}
