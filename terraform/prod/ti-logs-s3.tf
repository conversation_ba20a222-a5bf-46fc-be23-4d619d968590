resource "aws_s3_bucket" "ti_logs_s3" {
  bucket = "logs-export-prod-ti"

  tags = {
    Name        = "logs-export-prod-ti"
    Environment = "prod"
    Team        = "engineering"
    Product     = "ravenclaw"
  }
}

resource "aws_s3_bucket_versioning" "ti_logs_s3" {
  bucket = aws_s3_bucket.ti_logs_s3.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "ti_logs_s3" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.ti_logs_s3]

  bucket = aws_s3_bucket.ti_logs_s3.id

  rule {
    id = "lifecycle_configuration"

    noncurrent_version_expiration {
      noncurrent_days = 90
    }

    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "ti_logs_s3" {
  bucket = aws_s3_bucket.ti_logs_s3.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "ti_logs_s3" {
  depends_on = [aws_s3_bucket_ownership_controls.ti_logs_s3]

  bucket = aws_s3_bucket.ti_logs_s3.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "ti_logs_s3" {
  bucket = aws_s3_bucket.ti_logs_s3.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_policy" "ti_logs_s3" {
  bucket = aws_s3_bucket.ti_logs_s3.id

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Id" : "PolicyForCloudwatchLogsTILogs",
    "Statement" : [
      {
        "Sid" : "AllowCloudwatchLogsServicePrincipal",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "logs.ap-south-1.amazonaws.com"
        },
        "Action" : ["s3:Get*", "s3:Put*", "s3:List*"],
        "Resource" : [
          "${aws_s3_bucket.ti_logs_s3.arn}", 
          "${aws_s3_bucket.ti_logs_s3.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket_server_side_encryption_configuration" "ti_logs_s3" {
  bucket = aws_s3_bucket.ti_logs_s3.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.logs_s3.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

