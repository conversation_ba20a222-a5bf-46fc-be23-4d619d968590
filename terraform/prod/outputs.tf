output "vpc_main" {
  value = module.vpc_main.vpc_id
}

output "private_subnet_1_id" {
  value = module.main_private_subnet_1.subnet_id
}

output "private_subnet_2_id" {
  value = module.main_private_subnet_2.subnet_id
}

output "private_subnet_3_id" {
  value = module.main_private_subnet_3.subnet_id
}

output "public_subnet_1_id" {
  value = module.main_public_subnet_1.subnet_id
}

output "public_subnet_2_id" {
  value = module.main_public_subnet_2.subnet_id
}

output "public_subnet_3_id" {
  value = module.main_public_subnet_3.subnet_id
}

output "aws_internet_gateway_id" {
  value = module.main_igw.aws_internet_gateway_id
}

# ec2

output "nats_ec2_id" {
  value = module.nats.ec2_instance_id
}

output "nats_private_ip" {
  value = module.nats.ec2_private_ip
}

output "nats_public_ip" {
  value = module.nats.ec2_public_ip
}

output "bastion_ec2_id" {
  value = module.bastion.ec2_instance_id
}

output "bastion_private_ip" {
  value = module.bastion.ec2_private_ip
}

output "bastion_public_ip" {
  value = module.bastion.ec2_public_ip
}

# nat

output "nat_1_id" {
  value = module.nat-1.nat_id
}

output "nat_1_private_ip" {
  value = module.nat-1.private_ip
}

output "nat_1_public_ip" {
  value = module.nat-1.public_ip
}


output "nat_2_id" {
  value = module.nat-2.nat_id
}

output "nat_2_private_ip" {
  value = module.nat-2.private_ip
}

output "nat_2_public_ip" {
  value = module.nat-2.public_ip
}

output "nat_3_id" {
  value = module.nat-3.nat_id
}

output "nat_3_private_ip" {
  value = module.nat-3.private_ip
}

output "nat_3_public_ip" {
  value = module.nat-3.public_ip
}

# ecs

output "ecs_cluster_id" {
  value = module.ecs_cluster.ecs_cluster_id
}

output "service_discovery_namespace_arn" {
  value = module.ecs_cluster.service_discovery_namespace_arn
}

# IAM roles
output "task_role_arn" {
  value = aws_iam_role.task_role.arn
}

output "task_execution_role_arn" {
  value = aws_iam_role.task_execution_role.arn
}

output "log_metric_name" {
  value = module.gateway_log_alert.metric_name
}
