resource "aws_iam_role" "CloudWatchAgentServerRole" {
  name = "CloudWatchAgentProdServerRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "CloudWatchAgentPolicy" {
  name        = "CloudWatchAgentPolicy"
  description = "Policy for CloudWatch Agent with necessary permissions"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "tag:GetResources",
          "cloudwatch:GetMetricData",
          "cloudwatch:GetMetricStatistics",
          "cloudwatch:ListMetrics",
          "apigateway:GET",
          "aps:ListWorkspaces",
          "autoscaling:DescribeAutoScalingGroups",
          "dms:DescribeReplicationInstances",
          "dms:DescribeReplicationTasks",
          "ec2:DescribeTransitGatewayAttachments",
          "ec2:DescribeSpotFleetRequests",
          "shield:ListProtections",
          "storagegateway:ListGateways",
          "storagegateway:ListTagsForResource",
          "iam:ListAccountAliases"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "CloudWatchAgentServerPolicy_attach" {
  role       = aws_iam_role.CloudWatchAgentServerRole.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

resource "aws_iam_role_policy_attachment" "CloudWatchAgentRolePolicyAttachment" {
  role       = aws_iam_role.CloudWatchAgentServerRole.name
  policy_arn = aws_iam_policy.CloudWatchAgentPolicy.arn
}

resource "aws_iam_role_policy_attachment" "AmazonSSMManagedInstanceCore_attach" {
  role       = aws_iam_role.CloudWatchAgentServerRole.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role" "CloudWatchAgentAdminRole" {
  name = "CloudWatchAgentProdAdminRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "CloudWatchAgentAdminPolicy_attach" {
  role       = aws_iam_role.CloudWatchAgentAdminRole.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentAdminPolicy"
}

resource "aws_iam_role_policy_attachment" "AmazonSSMManagedInstanceCore_attach_admin" {
  role       = aws_iam_role.CloudWatchAgentAdminRole.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_instance_profile" "CloudWatchAgentServerProfile" {
  name = "CloudWatchAgentProdServerProfile"
  role = aws_iam_role.CloudWatchAgentServerRole.name
}

resource "aws_iam_instance_profile" "CloudWatchAgentAdminProfile" {
  name = "CloudWatchAgentProdAdminProfile"
  role = aws_iam_role.CloudWatchAgentAdminRole.name
}
