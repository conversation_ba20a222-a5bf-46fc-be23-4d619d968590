resource "aws_sns_topic" "ml_inference_alb_alerts" {
  name = "ML_INFERENCE_ALB_ALERT_SNS"
  tags = {
    Name        = "ML_INFERENCE_ALB_ALERT_SNS"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_lambda_function" "ml_inference_sns_to_google_chat" {
  filename         = var.metrics_alarm_lambda_filename
  function_name    = "MLInferenceAlertLambda"
  role             = aws_iam_role.lambda_execution_role.arn
  handler          = var.metrics_alarm_lambda_handler
  source_code_hash = filebase64sha256(var.metrics_alarm_lambda_filename)
  runtime          = var.metrics_alarm_lambda_python_runtime
  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.ML_INFERENCE_LOG_ALERTS_GCHAT_URL
    }
  }

  tags = {
    Name        = "MLInferenceAlertLambda"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic_subscription" "ml_inference_alb_alerts_subscription" {
  topic_arn = aws_sns_topic.ml_inference_alb_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.ml_inference_sns_to_google_chat.arn
}

resource "aws_lambda_permission" "ml_inference_allow_sns_alb" {
  statement_id  = "AllowExecutionFromSNSMLInferenceALB"
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.ml_inference_sns_to_google_chat.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.ml_inference_alb_alerts.arn
}

# HTTPCode_ELB_4XX_Count
resource "aws_cloudwatch_metric_alarm" "ml_inference_internal_alb_http_code_4xx_count" {
  alarm_name          = "ML_INFERENCE_INTERNAL_ALB_HTTP_CODE_4XX_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = "1"
  metric_name         = "HTTPCode_ELB_4XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Alarm when ML Inference Internal ALB 4XX Count is greater than 1 in last 5 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ml_inference_alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/alb-ml-inference-internal/2acfddc47ca6d266"
  }

  tags = {
    Name        = "ML_INFERENCE_INTERNAL_ALB_HTTP_CODE_4XX_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# HTTPCode_ELB_5XX_Count
resource "aws_cloudwatch_metric_alarm" "ml_inference_internal_alb_http_code_5xx_count" {
  alarm_name          = "ML_INFERENCE_INTERNAL_ALB_HTTP_CODE_5XX_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = "1"
  metric_name         = "HTTPCode_ELB_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Alarm when ML Inference Internal ALB 5XX Count is greater than 1 in last 5 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ml_inference_alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/alb-ml-inference-internal/2acfddc47ca6d266"
  }

  tags = {
    Name        = "ML_INFERENCE_INTERNAL_ALB_HTTP_CODE_5XX_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}


# # RejectedConnectionCount
resource "aws_cloudwatch_metric_alarm" "ml_inference_internal_alb_rejected_connection_count" {
  alarm_name          = "ML_INFERENCE_INTERNAL_ALB_REJECTED_CONNECTION_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "RejectedConnectionCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Alarm when ML Inference Internal ALB has equal or more than 1 connection rejections in last 5 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ml_inference_alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/alb-ml-inference-internal/2acfddc47ca6d266"
  }

  tags = {
    Name        = "ML_INFERENCE_INTERNAL_ALB_REJECTED_CONNECTION_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# HTTPCode_Target_4XX_Count
resource "aws_cloudwatch_metric_alarm" "ml_inference_internal_alb_http_code_target_4xx_count" {
  alarm_name          = "ML_INFERENCE_INTERNAL_ALB_HTTP_CODE_TARGET_4XX_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = "1"
  metric_name         = "HTTPCode_Target_4XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Alarm when ML Inference Internal ALB 4XX Target Count is greater than 1 in last 5 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ml_inference_alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/alb-ml-inference-internal/2acfddc47ca6d266"
  }

  tags = {
    Name        = "ML_INFERENCE_INTERNAL_ALB_HTTP_CODE_TARGET_4XX_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# HTTPCode_Target_5XX_Count
resource "aws_cloudwatch_metric_alarm" "ml_inference_internal_alb_http_code_target_5xx_count" {
  alarm_name          = "ML_INFERENCE_INTERNAL_ALB_HTTP_CODE_TARGET_5XX_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = "1"
  metric_name         = "HTTPCode_Target_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Alarm when ML Inference Internal ALB 5XX Target Count is greater than 1 in last 5 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ml_inference_alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/alb-ml-inference-internal/2acfddc47ca6d266"
  }

  tags = {
    Name        = "ML_INFERENCE_INTERNAL_ALB_HTTP_CODE_5XX_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}


# TargetConnectionErrorCount
resource "aws_cloudwatch_metric_alarm" "ml_inference_internal_alb_target_connection_error_count" {
  alarm_name          = "ML_INFERENCE_INTERNAL_ALB_TARGET_CONNECTION_ERROR_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "TargetConnectionErrorCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Alarm when ML Inference Internal ALB Target Connection Error Count is greater than 1 in last 5 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ml_inference_alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/alb-ml-inference-internal/2acfddc47ca6d266"
  }

  tags = {
    Name        = "ML_INFERENCE_INTERNAL_ALB_TARGET_CONNECTION_ERROR_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# UnhealthyStateDNS
resource "aws_cloudwatch_metric_alarm" "ml_inference_internal_alb_target_grp_unhealthy_state_dns" {
  alarm_name          = "ML_INFERENCE_INTERNAL_ALB_TARGET_GROUP_UNHEALTHY_STATE_DNS"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyStateDNS"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Alarm when ML Inference Internal ALB Target Group - Unhealthy State DNS Count is at least 1 in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ml_inference_alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/alb-ml-inference-internal/2acfddc47ca6d266"
    TargetGroup  = "targetgroup/ml-inference-target-group/f7d6d9199ca3e4a3"
  }

  tags = {
    Name        = "ML_INFERENCE_INTERNAL_ALB_TARGET_GROUP_UNHEALTHY_STATE_DNS"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}
