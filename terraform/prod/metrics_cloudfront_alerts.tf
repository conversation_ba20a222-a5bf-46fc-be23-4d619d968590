# # 4xxErrorRate
# resource "aws_cloudwatch_metric_alarm" "cloudfront_4xx_error_rate" {
#   alarm_name          = "CLOUDFRONT_4XX_ERROR_RATE"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "4xxErrorRate"
#   namespace           = "AWS/CloudFront"
#   period              = 60
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Cloudfront 4XX Error Rate is greater than 10% in last 1 minute"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.cloudfront_alerts.arn]
#   dimensions = {
#     DistributionId = aws_cloudfront_distribution.s3_distribution.id
#   }

#   tags = {
#     Name        = "CLOUDFRONT_4XX_ERROR_RATE"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "cloudfront"
#   }
# }

# # 5xxErrorRate
# resource "aws_cloudwatch_metric_alarm" "cloudfront_5xx_error_rate" {
#   alarm_name          = "CLOUDFRONT_5XX_ERROR_RATE"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "5xxErrorRate"
#   namespace           = "AWS/CloudFront"
#   period              = 60
#   statistic           = "Average"
#   threshold           = 10
#   alarm_description   = "Alarm when Cloudfront 5XX Error Rate is greater than 10% in last 1 minute"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.cloudfront_alerts.arn]
#   dimensions = {
#     DistributionId = aws_cloudfront_distribution.s3_distribution.id
#   }

#   tags = {
#     Name        = "CLOUDFRONT_5XX_ERROR_RATE"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "cloudfront"
#   }
# }

# # TotalErrorRate
# resource "aws_cloudwatch_metric_alarm" "cloudfront_total_error_rate" {
#   alarm_name          = "CLOUDFRONT_TOTAL_ERROR_RATE"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "TotalErrorRate"
#   namespace           = "AWS/CloudFront"
#   period              = 60
#   statistic           = "Average"
#   threshold           = 10
#   alarm_description   = "Alarm when Cloudfront Total Error Rate is greater than 10% in last 1 minute"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.cloudfront_alerts.arn]
#   dimensions = {
#     DistributionId = aws_cloudfront_distribution.s3_distribution.id
#   }

#   tags = {
#     Name        = "CLOUDFRONT_TOTAL_ERROR_RATE"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "cloudfront"
#   }
# }

# OriginLatency
resource "aws_cloudwatch_metric_alarm" "cloudfront_origin_latency" {
  alarm_name          = "CLOUDFRONT_ORIGIN_LATENCY"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "OriginLatency"
  namespace           = "AWS/CloudFront"
  period              = 60
  statistic           = "Minimum"
  threshold           = 1000
  alarm_description   = "Alarm when Cloudfront Origin Latency is minimum 10 secs in last 1 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.cloudfront_alerts.arn]
  dimensions = {
    DistributionId = aws_cloudfront_distribution.s3_distribution.id
  }

  tags = {
    Name        = "CLOUDFRONT_ORIGIN_LATENCY"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "cloudfront"
  }
}
