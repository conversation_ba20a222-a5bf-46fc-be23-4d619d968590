module "keycloak_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "start",
      "--optimized"
    ],
    "secrets" : [{
      "name" : "KEYCLOAK_ADMIN",
      "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_ADMIN_USERNAME.arn
      }, {
      "name" : "KEYCLOAK_ADMIN_PASSWORD",
      "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_ADMIN_PASSWORD.arn
      }, {
      "name" : "KC_DB_PASSWORD",
      "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_PG_PASSWORD.arn
    }],
    "environment" : [{
      "name" : "KC_DB",
      "value" : var.keycloak_db
      }, {
      "name" : "KC_DB_URL_HOST",
      "value" : module.keycloak_rds.db_host
      }, {
      "name" : "KC_DB_URL_PORT",
      "value" : var.keycloak_db_port
      }, {
      "name" : "KC_DB_URL_DATABASE",
      "value" : var.kc_db_url_db
      }, {
      "name" : "KC_DB_USERNAME",
      "value" : module.keycloak_rds.db_username
      }, {
      "name" : "KC_HOSTNAME",
      "value" : var.keycloak_hostname
      }, {
      "name" : "KC_LOG_LEVEL",
      "value" : var.kc_log_level
      }, {
      "name"  = "KC_PROXY_HEADERS"
      "value" = var.kc_proxy_headers
      }, {
      "name"  = "KC_CACHE",
      "value" = var.kc_cache
      }, {
      "name"  = "KC_CACHE_CONFIG_FILE",
      "value" = var.kc_cache_config_file
      }, {
      "name"  = "KC_DB_URL",
      "value" = "jdbc:postgresql://${module.keycloak_rds.db_host}:${var.keycloak_db_port}/${var.kc_db_url_db}"
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.keycloak_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.keycloak_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.keycloak_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8443,
      "hostPort" : 8443,
      "name" : "dashboard",
      "protocol" : "tcp"
      }, {
      "appProtocol" : "http",
      "containerPort" : 9000,
      "hostPort" : 9000,
      "name" : "management",
      "protocol" : "tcp"
      }, {
      "appProtocol" : "http",
      "containerPort" : 7800,
      "hostPort" : 7800,
      "name" : "jdbc_ping",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.keycloak_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "keycloak"
  memory             = var.keycloak_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.keycloak_task_def_tags
}

module "keycloak_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.keycloak_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.keycloak_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.keycloak_service_name
  desired_count                  = var.keycloak_desired_count
  task_definition_arn            = module.keycloak_task_definition.arn
  target_group_arn               = aws_lb_target_group.keycloak.arn
  container_name                 = var.keycloak_docker_image_name
  container_port                 = var.kc_container_port

  service_discovery_svc_tags = var.keycloak_service_discovery_tags
  spread                     = true

  ecs_service_tags = var.keycloak_service_tags
}
