resource "aws_sns_topic" "ses-alert-delivery" {
  name = "PROD-SES-MAIL-ALERT-DELIVERY"
  tags = {
    Name        = "PROD-SES-MAIL-ALERT-DELIVERY"
    Environment = "prod"
    Team        = "infra"
    Product     = "ses"
  }
}

resource "aws_sns_topic" "ses-alert-bounce" {
  name = "PROD-SES-MAIL-ALERT-BOUNCE"
  tags = {
    Name        = "PROD-SES-MAIL-ALERT-BOUNCE"
    Environment = "prod"
    Team        = "infra"
    Product     = "ses"
  }
}

resource "aws_sns_topic" "ses-alert-feedback" {
  name = "PROD-SES-MAIL-ALERT-FEEDBACK"
  tags = {
    Name        = "PROD-SES-MAIL-ALERT-FEEDBACK"
    Environment = "prod"
    Team        = "infra"
    Product     = "ses"
  }
}

resource "aws_lambda_function" "ses_sns" {
  filename         = "lambda_function_ses.zip"
  function_name    = "SESNotification"
  role             = aws_iam_role.lambda_execution_role.arn
  handler          = "lambda_handler.lambda_handler"
  source_code_hash = filebase64sha256("lambda_function_ses.zip")
  runtime          = "python3.10"
  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL   = var.SES_NOTIFICATION_CHAT_WEBHOOK_URL
      SECURITY_CHAT_WEBHOOK_URL = var.SECURITY_NOTIFICATION_CHAT_WEBHOOK_URL
    }
  }

  tags = {
    Name        = var.metrics_alarm_lambda_function_name
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic_subscription" "ses_delivery_subscription" {
  topic_arn = aws_sns_topic.ses-alert-delivery.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.ses_sns.arn
}

resource "aws_sns_topic_subscription" "ses_bounce_subscription" {
  topic_arn = aws_sns_topic.ses-alert-bounce.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.ses_sns.arn
}

resource "aws_sns_topic_subscription" "ses_feedback_subscription" {
  topic_arn = aws_sns_topic.ses-alert-feedback.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.ses_sns.arn
}

resource "aws_lambda_permission" "allow_ses_delivery" {
  statement_id  = "AllowExecutionFromSESDelivery"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.ses_sns.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.ses-alert-delivery.arn
}

resource "aws_lambda_permission" "allow_ses_bounce" {
  statement_id  = "AllowExecutionFromSESBounce"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.ses_sns.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.ses-alert-bounce.arn
}

resource "aws_lambda_permission" "allow_ses_feedback" {
  statement_id  = "AllowExecutionFromSESFeedback"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.ses_sns.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.ses-alert-feedback.arn
}
