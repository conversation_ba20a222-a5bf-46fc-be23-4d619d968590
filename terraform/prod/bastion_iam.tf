resource "aws_iam_role" "bastion_iam_role" {
  name = "BastionProdIAMRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = [
            "ecs.amazonaws.com",
            "ec2.amazonaws.com",
            "s3.amazonaws.com",
            "cloudfront.amazonaws.com",
            "logs.amazonaws.com",
            "route53.amazonaws.com",
            "secretsmanager.amazonaws.com",
            "kms.amazonaws.com",
            "cloudtrail.amazonaws.com",
            "acm.amazonaws.com",
            "sns.amazonaws.com",
            "lambda.amazonaws.com",
            "elasticfilesystem.amazonaws.com",
            "sagemaker.amazonaws.com",
          ]
        }
      }
    ]
  })
}

# Create an IAM policy for the EC2 instance
resource "aws_iam_policy" "bastion_iam_policy" {
  name        = "BastionProdIAMPolicy"
  path        = "/"
  description = "IAM policy for prod Bastion instance to running scripts and terraform"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:UpdateService",
          "ecs:Describe*",
          "ecs:RunTask",
          "ecs:DescribeTaskDefinition",
          "ec2:DescribeVpcs",
          "ec2:DescribeSubnets",
          "ec2:DescribeSecurityGroups",
          "ecs:TagResource",
          "elasticfilesystem:Describe*",
          "elasticfilesystem:List*"
        ]
        Resource = "*",
        # Condition : {
        #   "StringEquals" : {
        #     "aws:RequestedRegion" : "ap-south-1",
        #   },
        #   "IpAddress" : {
        #     "aws:SourceIp" : ["${module.bastion.ec2_public_ip}/32"]
        #   }
        # }
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::terraform-test-bucket-2024-1",
          "arn:aws:s3:::terraform-test-bucket-2024-1/*"
        ]
        # Condition : {
        #   "IpAddress" : {
        #     "aws:SourceIp" : ["${module.bastion.ec2_public_ip}/32"]
        #   }
        # }
      },
      {
        Effect = "Allow"
        Action = [
          "iam:PassRole"
        ]
        Resource = "*"
        # Condition = {
        #   StringLike = {
        #     "iam:PassedToService" : "ecs-tasks.amazonaws.com",
        #     "aws:RequestedRegion" : "ap-south-1",
        #   },
        #   "IpAddress" : {
        #     "aws:SourceIp" : ["${module.bastion.ec2_public_ip}/32"]
        #   }
        # }
      }
    ]
  })
}

resource "aws_iam_policy" "bastion_terraform_get_state_iam_policy" {
  name        = "BastionProdTerraformGetStateIAMPolicy"
  path        = "/"
  description = "IAM policy for Prod Bastion instance to get and load terraform state"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:Describe*",
          "ecs:TagResource",
          "ecs:DeregisterTaskDefinition",
          "ecs:RegisterTaskDefinition",

          # EC2
          "ec2:Describe*",

          # IAM
          "iam:Get*",
          "iam:List*",

          # Cloudfront
          "cloudfront:Get*",
          "cloudfront:List*",

          # Logs
          "logs:Describe*",
          "logs:List*",

          # Route 53
          "route53:Get*",
          "route53:List*",

          # S3
          "s3:HeadBucket",
          "s3:Get*",
          "s3:List*",

          # Secretsmanager
          "secretsmanager:Describe*",
          "secretsmanager:Get*",

          # KMS
          "kms:Describe*",
          "kms:Get*",
          "kms:List*",
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",

          # Service Discovery
          "servicediscovery:Get*",
          "servicediscovery:List*",

          # Elastic Load Balancing
          "elasticloadbalancing:Describe*",

          # RDS
          "rds:Describe*",
          "rds:List*",

          # Cloudtrail
          "cloudtrail:Get*",
          "cloudtrail:List*",
          "cloudtrail:Describe*",
          "cloudtrail:LookupEvents",
          "cloudtrail:AddTags",

          # ACM
          "acm:ExportCertificate",
          "acm:Describe*",
          "acm:Get*",
          "acm:List*",

          # SNS
          "sns:Get*",
          "sns:List*",

          # Lambda
          "lambda:Get*",
          "lambda:List*",

          # cloudwatch
          "cloudwatch:Describe*",
          "cloudwatch:List*",
          "cloudwatch:Get*",

          # waf
          "wafV2:Get*",
          "wafV2:List*",
          "wafV2:Describe*",

          "application-autoscaling:Describe*",
          "application-autoscaling:List*",
          "config:Describe*",
          "securityhub:Describe*",
          "securityhub:List*",
          "securityhub:Get*",
          "securityhub:*",
          "kinesis:Describe*",
          "kinesis:List*",
          "firehose:Describe*",
          "firehose:List*",
          "firehose:Get*",

          "elasticfilesystem:Describe*",
          "elasticfilesystem:List*",

          "sagemaker:*",
        ]
        Resource = "*",
        # Condition : {
        #   "StringEquals" : {
        #     "aws:RequestedRegion" : "ap-south-1",
        #   },
        #   "IpAddress" : {
        #     "aws:SourceIp" : ["${module.bastion.ec2_public_ip}/32"]
        #   }
        # }
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
        ]
        Resource = [
          "arn:aws:s3:::webui-build-prod",
          "arn:aws:s3:::webui-build-prod/*"
        ]
        # Condition : {
        #   "StringEquals" : {
        #     "aws:RequestedRegion" : "ap-south-1",
        #   },
        #   "IpAddress" : {
        #     "aws:SourceIp" : ["${module.bastion.ec2_public_ip}/32"]
        #   }
        # }
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::terraform-test-bucket-2024-1",
          "arn:aws:s3:::terraform-test-bucket-2024-1/*"
        ]
        # Condition : {
        #   "IpAddress" : {
        #     "aws:SourceIp" : ["${module.bastion.ec2_public_ip}/32"]
        #   }
        # }
      }
    ]
  })
}

resource "aws_iam_policy" "bastion_terraform_apply_state_iam_policy" {
  name        = "BastionProdTerraformApplyStateIAMPolicy"
  path        = "/"
  description = "IAM policy for prod Bastion instance to apply changes to terraform state"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:UpdateService",
          "ecs:RunTask",
          "ecs:TagResource"
        ]
        Resource = "*",
        # Condition : {
        #   "StringEquals" : {
        #     "aws:RequestedRegion" : "ap-south-1",
        #   },
        #   "IpAddress" : {
        #     "aws:SourceIp" : ["${module.bastion.ec2_public_ip}/32"]
        #   }
        # }
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::terraform-test-bucket-2024-1",
          "arn:aws:s3:::terraform-test-bucket-2024-1/*"
        ]
        # Condition : {
        #   "IpAddress" : {
        #     "aws:SourceIp" : ["${module.bastion.ec2_public_ip}/32"]
        #   }
        # }
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "bastion_policy_attachment" {
  policy_arn = aws_iam_policy.bastion_iam_policy.arn
  role       = aws_iam_role.bastion_iam_role.name
}

resource "aws_iam_role_policy_attachment" "bastion_terraform_get_state_policy_attachment" {
  policy_arn = aws_iam_policy.bastion_terraform_get_state_iam_policy.arn
  role       = aws_iam_role.bastion_iam_role.name
}

resource "aws_iam_role_policy_attachment" "bastion_terraform_apply_state_policy_attachment" {
  policy_arn = aws_iam_policy.bastion_terraform_apply_state_iam_policy.arn
  role       = aws_iam_role.bastion_iam_role.name
}

# Create an instance profile for the EC2 instance
resource "aws_iam_instance_profile" "bastion_iam_profile" {
  name = "BastionProdIAMProfile"
  role = aws_iam_role.bastion_iam_role.name
}

resource "aws_iam_user" "bastion_prod_user" {
  name = "bastion_prod_user"
}

resource "aws_iam_user_policy_attachment" "bastion_prod_user_policy" {
  user       = aws_iam_user.bastion_prod_user.name
  policy_arn = aws_iam_policy.bastion_iam_policy.arn
}


resource "aws_iam_user_policy_attachment" "bastion_prod_terraform_get_state_user_policy" {
  user       = aws_iam_user.bastion_prod_user.name
  policy_arn = aws_iam_policy.bastion_terraform_get_state_iam_policy.arn
}

resource "aws_iam_user_policy_attachment" "bastion_prod_terraform_apply_state_user_policy" {
  user       = aws_iam_user.bastion_prod_user.name
  policy_arn = aws_iam_policy.bastion_terraform_apply_state_iam_policy.arn
}
