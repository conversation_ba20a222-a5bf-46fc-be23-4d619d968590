variable "gateway_log_filter_name" {
  type    = string
  default = "gateway_error_log_alarm"
}

variable "bff_log_filter_name" {
  type    = string
  default = "bff_error_log_alarm"
}

variable "setup_log_filter_name" {
  type    = string
  default = "setup_error_log_alarm"
}

variable "remediator_log_filter_name" {
  type    = string
  default = "remediator_error_log_alarm"
}

variable "ti_go_service_log_filter_name" {
  type    = string
  default = "ti_go_service_error_log_alarm"
}

variable "ml_inference_log_filter_name" {
  type    = string
  default = "ml_inference_error_log_alarm"
}

variable "log_pattern" {
  type    = string
  default = "[date, time, status=%INFO%]"
}

variable "gateway_log_alarm_name" {
  type    = string
  default = "gateway_log_alarm"
}

variable "bff_log_alarm_name" {
  type    = string
  default = "bff_log_alarm"
}

variable "remediator_log_alarm_name" {
  type    = string
  default = "remediator_log_alarm"
}

variable "setup_log_alarm_name" {
  type    = string
  default = "setup_log_alarm"
}

variable "ti_go_service_log_alarm_name" {
  type    = string
  default = "ti_go_service_log_alarm"
}

variable "ml_inference_log_alarm_name" {
  type    = string
  default = "ml_inference_service_log_alarm"
}


variable "GOOGLE_CHAT_WEBHOOK_URL" {
  type = string
}

variable "LOG_ALERTS_GCHAT_URL" {
  type = string
}

variable "gateway_sns_topic_name" {
  type    = string
  default = "gateway_alert_sns_topic"
}

variable "bff_sns_topic_name" {
  type    = string
  default = "bff_alert_sns_topic"
}

variable "remediator_sns_topic_name" {
  type    = string
  default = "remediator_alert_sns_topic"
}

variable "setup_sns_topic_name" {
  type    = string
  default = "setup_alert_sns_topic"
}

variable "sns_topic_name" {
  type    = string
  default = "alert_sns_topic"
}

variable "lambda_function_name" {
  type    = string
  default = "sendAlert"
}

variable "lambda_handler" {
  type    = string
  default = "lambda_function.lambda_handler"
}

variable "lambda_runtime" {
  type    = string
  default = "python3.10"
}


variable "lambda_filename" {
  type    = string
  default = "lambda_function_handler.zip"
}

variable "lambda_policy_arn" {
  type    = string
  default = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

variable "lambda_permission_statement_id" {
  type    = string
  default = "AllowExecutionFromSNS"
}

variable "lambda_permission_action" {
  type    = string
  default = "lambda:InvokeFunction"
}

variable "lambda_permission_principal" {
  type    = string
  default = "sns.amazonaws.com"
}

variable "sns_topic_subscription_protocol" {
  type    = string
  default = "lambda"
}

variable "gateway_metric_transformation_name" {
  type    = string
  default = "GATEWAY_ERROR_COUNT"
}

variable "bff_metric_transformation_name" {
  type    = string
  default = "BFF_ERROR_COUNT"
}

variable "remediator_metric_transformation_name" {
  type    = string
  default = "REMEDIATOR_ERROR_COUNT"
}

variable "setup_metric_transformation_name" {
  type    = string
  default = "SETUP_ERROR_COUNT"
}

variable "ti_go_service_metric_transformation_name" {
  type    = string
  default = "TI_GO_SERVICE_ERROR_COUNT"
}

variable "ml_inference_metric_transformation_name" {
  type    = string
  default = "ML_INFERENCE_ERROR_COUNT"
}

variable "rds_sns_topic" {
  type    = string
  default = "rds-alerts"
}

variable "metrics_alarm_lambda_filename" {
  type    = string
  default = "lambda_function.zip"
}

variable "metrics_alarm_lambda_function_name" {
  type    = string
  default = "SnsToGoogleChat"
}

variable "metrics_alarm_lambda_handler" {
  type    = string
  default = "lambda_function.lambda_handler"
}

variable "metrics_alarm_lambda_python_runtime" {
  type    = string
  default = "python3.10"
}

variable "RDS_CPU_Utilization_alarm_name" {
  type    = string
  default = "Ravenclaw_RDS_CPU_Utilization_Alarm"
}

variable "RDS_CPU_Utilization_comparison_operator" {
  type    = string
  default = "GreaterThanThreshold"
}

variable "RDS_CPU_Utilization_evaluation_periods" {
  type    = string
  default = "10"
}

variable "RDS_CPU_Utilization_metric_name" {
  type    = string
  default = "CPUUtilization"
}

variable "rds_alarm_namespace" {
  type    = string
  default = "AWS/RDS"
}

variable "rds_cpu_utilization_alarm_period" {
  type    = string
  default = "180"
}

variable "rds_cpu_utilization_alarm_station" {
  type    = string
  default = "Average"
}

variable "rds_cpu_utilization_threshold" {
  type    = number
  default = 90
}

variable "rds_cpu_utilization_alarm_description" {
  type    = string
  default = "Ravenclaw - Alarm when RDS cpu utilization is less than the threshold"
}

variable "rds_cpu_utilization_actions_enabled" {
  type    = bool
  default = true
}

variable "RDS_Connections_alarm_name" {
  type    = string
  default = "Ravenlaw_RDS_Connections_Alarm"
}

variable "RDS_Connections_comparison_operator" {
  type    = string
  default = "GreaterThanThreshold"
}

variable "RDS_Connections_evaluation_periods" {
  type    = string
  default = "1"
}

variable "RDS_Connections_metric_name" {
  type    = string
  default = "DatabaseConnections"
}

variable "rds_connections_alarm_period" {
  type    = string
  default = "120"
}

variable "rds_connections_alarm_statistic" {
  type    = string
  default = "Average"
}

variable "RDS_Connections_threshold" {
  type    = number
  default = 150
}

variable "RDS_Connections_alarm_description" {
  type    = string
  default = "Alarm when Ravenclaw RDS connections exceed the threshold"
}

variable "RDS_Connections_actions_enabled" {
  type    = bool
  default = true
}

variable "RDS_Freeable_Memory_alarm_name" {
  type    = string
  default = "Ravenclaw_RDS_Freeable_Memory_Alarm"
}

variable "RDS_Freeable_Memory_comparison_operator" {
  type    = string
  default = "LessThanThreshold"
}

variable "RDS_Freeable_Memory_evaluation_periods" {
  type    = string
  default = "1"
}

variable "RDS_Freeable_Memory_metric_name" {
  type    = string
  default = "FreeableMemory"
}

variable "rds_freeable_memory_alarm_period" {
  type    = string
  default = "300"
}

variable "rds_freeable_memory_alarm_statistic" {
  type    = string
  default = "Average"
}

variable "RDS_Freeable_Memory_threshold" {
  type    = number
  default = 419430400 # 400MB
}

variable "RDS_Freeable_Memory_alarm_description" {
  type    = string
  default = "Alarm when Ravenclaw RDS FreeableMemory is less than the threshold"
}

variable "RDS_Freeable_Memory_actions_enabled" {
  type    = bool
  default = true
}

variable "elasticache_sns_topic" {
  type    = string
  default = "elasticache-alerts"
}

variable "ecs_sns_topic" {
  type    = string
  default = "ecs-alerts"
}

variable "nats_logs_group" {
  type    = string
  default = "/ec2/nats"
}

variable "nats_log_filter_name" {
  type    = string
  default = "nats_error_log_alarm"
}

variable "nats_log_alarm_name" {
  type    = string
  default = "nats_log_alarm"
}

variable "nats_metric_transformation_name" {
  type    = string
  default = "NATS_ERROR_COUNT"
}

# keycloak
variable "keycloak_log_filter_name" {
  type    = string
  default = "keycloak_error_log_alarm"
}

variable "keycloak_log_alarm_name" {
  type    = string
  default = "keycloak_log_alarm"
}

variable "keycloak_metric_transformation_name" {
  type    = string
  default = "KEYCLOAK_ERROR_COUNT"
}

variable "keycloak_rds_sns_topic" {
  type    = string
  default = "keycloak-rds-alerts"
}

# keycloak RDS
variable "KEYCLOAK_RDS_CPU_Utilization_alarm_name" {
  type    = string
  default = "KEYCLOAK_RDS_CPU_Utilization_Alarm"
}

variable "KEYCLOAK_RDS_CPU_Utilization_comparison_operator" {
  type    = string
  default = "GreaterThanThreshold"
}

variable "KEYCLOAK_RDS_CPU_Utilization_evaluation_periods" {
  type    = string
  default = "1"
}

variable "KEYCLOAK_RDS_CPU_Utilization_metric_name" {
  type    = string
  default = "CPUUtilization"
}

variable "keycloak_rds_alarm_namespace" {
  type    = string
  default = "AWS/RDS"
}

variable "keycloak_rds_cpu_utilization_alarm_period" {
  type    = string
  default = "180"
}

variable "keycloak_rds_cpu_utilization_alarm_station" {
  type    = string
  default = "Average"
}

variable "keycloak_rds_cpu_utilization_threshold" {
  type    = number
  default = 80
}

variable "keycloak_rds_cpu_utilization_alarm_description" {
  type    = string
  default = "Keycloak RDS - Alarm when RDS cpu utilization is less than the threshold"
}

variable "keycloak_rds_cpu_utilization_actions_enabled" {
  type    = bool
  default = true
}

variable "KEYCLOAK_RDS_Connections_alarm_name" {
  type    = string
  default = "Keycloak_Ravenlaw_RDS_Connections_Alarm"
}

variable "KEYCLOAK_RDS_Connections_comparison_operator" {
  type    = string
  default = "GreaterThanThreshold"
}

variable "KEYCLOAK_RDS_Connections_evaluation_periods" {
  type    = string
  default = "1"
}

variable "KEYCLOAK_RDS_Connections_metric_name" {
  type    = string
  default = "DatabaseConnections"
}

variable "keycloak_rds_connections_alarm_period" {
  type    = string
  default = "120"
}

variable "keycloak_rds_connections_alarm_statistic" {
  type    = string
  default = "Average"
}

variable "KEYCLOAK_RDS_Connections_threshold" {
  type    = number
  default = 100
}

variable "KEYCLOAK_RDS_Connections_alarm_description" {
  type    = string
  default = "Alarm when Ravenclaw RDS connections exceed the threshold"
}

variable "KEYCLOAK_RDS_Connections_actions_enabled" {
  type    = bool
  default = true
}

variable "KEYCLOAK_RDS_Freeable_Memory_alarm_name" {
  type    = string
  default = "Keycloak_RDS_Freeable_Memory_Alarm"
}

variable "KEYCLOAK_RDS_Freeable_Memory_comparison_operator" {
  type    = string
  default = "LessThanThreshold"
}

variable "KEYCLOAK_RDS_Freeable_Memory_evaluation_periods" {
  type    = string
  default = "1"
}

variable "KEYCLOAK_RDS_Freeable_Memory_metric_name" {
  type    = string
  default = "FreeableMemory"
}

variable "keycloak_rds_freeable_memory_alarm_period" {
  type    = string
  default = "300"
}

variable "keycloak_rds_freeable_memory_alarm_statistic" {
  type    = string
  default = "Average"
}

variable "KEYCLOAK_RDS_Freeable_Memory_threshold" {
  type    = number
  default = 419430400 # 400MB
}

variable "KEYCLOAK_RDS_Freeable_Memory_alarm_description" {
  type    = string
  default = "Alarm when Ravenclaw RDS FreeableMemory is less than the threshold"
}

variable "KEYCLOAK_RDS_Freeable_Memory_actions_enabled" {
  type    = bool
  default = true
}

# EC2
variable "ec2_sns_topic" {
  type    = string
  default = "ec2_alert_sns_topic"
}

variable "ingestion_log_filter_name" {
  type    = string
  default = "ingestion_error_log_alarm"
}

variable "ingestion_log_alarm_name" {
  type    = string
  default = "ingestion_log_alarm"
}

variable "ingestion_metric_transformation_name" {
  type    = string
  default = "INGESTION_ERROR_COUNT"
}

variable "CLOUDTRAIL_GOOGLE_CHAT_WEBHOOK_URL" {
  type = string
}

variable "SES_NOTIFICATION_CHAT_WEBHOOK_URL" {
  type = string
}

variable "SECURITY_NOTIFICATION_CHAT_WEBHOOK_URL" {
  description = "Google Chat webhook URL for security-related SES notifications"
  type        = string
}
