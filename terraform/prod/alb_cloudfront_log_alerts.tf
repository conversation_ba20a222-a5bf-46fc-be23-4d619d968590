resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_4xx_5xx_response" {
  alarm_name          = "RAVENCLAW_ALB_4XX_5XX_RESPONSE_ALERT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 600
  statistic           = "Sum"
  threshold           = 30
  alarm_description   = "Alarm when there are more than 30 log events in last 10 minutes of Ravenclaw ALB 4xx/5xx response"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/aws/alb/ravenclaw/access-logs"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_4XX_5XX_RESPONSE_ALERT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_4xx_5xx_response" {
  alarm_name          = "KEYCLOAK_ALB_4XX_5XX_RESPONSE_ALERT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 600
  statistic           = "Sum"
  threshold           = 30
  alarm_description   = "Alarm when there are more than 30 log events in last 10 minutes of Keycloak ALB 4xx/5xx response"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/aws/alb/keycloak/access-logs"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_4XX_5XX_RESPONSE_ALERT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "webui_cloudfront_realtime_4xx_5xx_response" {
  alarm_name          = "WEBUI_CLOUDFRONT_REALTIME_4XX_5XX_RESPONSE_ALERT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 600
  statistic           = "Sum"
  threshold           = 30
  alarm_description   = "Alarm when there are more than 30 log events in last 10 minutes of WebUI Cloudfront 4xx/5xx response"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/aws/cloudfront/webui/realtimelogs"
  }

  tags = {
    Name        = "WEBUI_CLOUDFRONT_REALTIME_4XX_5XX_RESPONSE_ALERT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "webui_cloudfront_std_4xx_5xx_response" {
  alarm_name          = "WEBUI_CLOUDFRONT_STD_4XX_5XX_RESPONSE_ALERT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 600
  statistic           = "Sum"
  threshold           = 30
  alarm_description   = "Alarm when there are more than 30 log events in last 10 minutes of  WebUI Std. Cloudfront 4xx/5xx response"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/aws/cloudfront/webui/stdlogs"
  }

  tags = {
    Name        = "WEBUI_CLOUDFRONT_STD_4XX_5XX_RESPONSE_ALERT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}
