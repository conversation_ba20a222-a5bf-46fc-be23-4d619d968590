resource "aws_db_subnet_group" "main" {
  description = "Default VPC"
  name        = var.vpc_name
  subnet_ids  = ["${module.main_private_subnet_1.subnet_id}", "${module.main_private_subnet_2.subnet_id}", "${module.main_private_subnet_3.subnet_id}"]

  tags = var.ravenclaw_rds_subnet_group_tags
}

module "rds" {
  source                 = "../modules/rds"
  region                 = var.region
  subnet_ids             = ["${module.main_private_subnet_1.subnet_id}", "${module.main_private_subnet_2.subnet_id}", "${module.main_private_subnet_3.subnet_id}"]
  vpc_name               = var.vpc_name
  db_name                = var.db_name
  allocated_storage      = var.rds_allocated_storage
  storage_type           = "gp3"
  availability_zone      = var.rds_availability_zone
  vpc_security_group_ids = ["${aws_security_group.ravenclaw_db_sg.id}"]
  password               = aws_secretsmanager_secret_version.PG_PASSWORD.secret_string
  identifier             = var.identifier
  multi_az               = false
  instance_class         = var.rds_instance_class
  db_subnet_group_name   = aws_db_subnet_group.main.name
  iops                   = 3000
  storage_throughput     = 125
  engine_version         = 16.5

  # monitoring_interval = 60
  # monitoring_role_arn = "arn:aws:iam::771151923073:role/rds-monitoring-role"

  instance_tags = var.ravenclaw_rds_instance_tags

  # kms_tags = var.ravenclaw_rds_kms_tags

  create_read_replica         = var.rds_create_read_replica
  read_replica_azs            = var.rds_read_replica_azs
  read_replica_engine_version = 16.5
  replica_instance_tags       = var.rds_replica_instance_tags
  read_replica_instance_class = "db.t4g.small"
  read_replica_multi_az       = false
}

module "keycloak_rds" {
  source                      = "../modules/rds"
  region                      = var.region
  subnet_ids                  = ["${module.main_private_subnet_1.subnet_id}", "${module.main_private_subnet_2.subnet_id}", "${module.main_private_subnet_3.subnet_id}"]
  vpc_name                    = var.vpc_name
  db_name                     = var.keycloak_db_name
  allocated_storage           = var.keycloak_rds_allocated_storage
  availability_zone           = var.keycloak_rds_availability_zone
  vpc_security_group_ids      = ["${aws_security_group.keycloak_db_sg.id}"]
  password                    = aws_secretsmanager_secret_version.KEYCLOAK_PG_PASSWORD.secret_string
  identifier                  = var.keycloak_identifier
  multi_az                    = var.keycloak_rds_multi_az
  engine_version              = 16.5
  instance_class              = var.keycloak_rds_instance_class
  read_replica_instance_class = "db.t4g.small"
  db_subnet_group_name        = aws_db_subnet_group.main.name
  read_replica_multi_az       = false
  read_replica_engine_version = 16.5

  instance_tags = var.keycloak_rds_instance_tags

  subnet_group_tags = var.keycloak_rds_subnet_group_tags

  # kms_tags = var.keycloak_rds_kms_tags

  # Read replica
  create_read_replica   = var.keycloak_rds_create_read_replica
  read_replica_azs      = var.keycloak_rds_read_replica_azs
  replica_instance_tags = var.keycloak_rds_replica_instance_tags
}
