resource "aws_eip" "nat1_eip" {
  domain               = var.nat1_eip_domain
  network_border_group = var.region
  public_ipv4_pool     = var.nat1_public_ipv4_pool

  tags = var.nat_gateway_1_eip_tags
}

resource "aws_eip" "nat2_eip" {
  domain               = var.nat2_eip_domain
  network_border_group = var.region
  public_ipv4_pool     = var.nat2_public_ipv4_pool

  tags = var.nat_gateway_2_eip_tags
}

resource "aws_eip" "nat3_eip" {
  domain               = var.nat3_eip_domain
  network_border_group = var.region
  public_ipv4_pool     = var.nat3_public_ipv4_pool

  tags = var.nat_gateway_3_eip_tags
}

resource "aws_eip" "bastion_eip" {
  domain               = var.bastion_eip_domain
  network_border_group = var.region
  public_ipv4_pool     = var.bastion_public_ipv4_pool

  tags = var.bastion_eip_tags
}

resource "aws_eip_association" "bastion" {
  instance_id   = module.bastion.ec2_instance_id
  allocation_id = aws_eip.bastion_eip.id
}

resource "aws_eip" "metabase_eip" {
  domain               = "vpc"
  network_border_group = var.region
  public_ipv4_pool     = "amazon"

  tags = {
    Name        = "metabase-elastic-ip"
    Application = "metabase"
    Environment = "prod"
    Team        = "infra"
  }
}

resource "aws_eip_association" "metabase" {
  instance_id   = module.metabase_ec2.ec2_instance_id
  allocation_id = aws_eip.metabase_eip.id
}
