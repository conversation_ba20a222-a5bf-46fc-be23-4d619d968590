import json
import urllib3
import os
import logging
import re
from datetime import datetime

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

http = urllib3.PoolManager()

def format_message(message_content):
    """Format the SES message into a readable structure"""
    
    # Helper function to format timestamp
    def format_timestamp(timestamp_str):
        dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        return dt.strftime('%B %d, %Y %H:%M:%S UTC')

    formatted_sections = [
        "Email Delivery Details",
        "=====================\n",
        "Basic Information:",
        "-----------------"
    ]

    # Basic Information
    notification_type = message_content.get('notificationType', 'N/A')
    mail_info = message_content.get('mail', {})
    timestamp = format_timestamp(mail_info.get('timestamp', ''))
    
    formatted_sections.extend([
        f"Notification Type: {notification_type}",
        f"Timestamp: {timestamp}",
        f"Status: Successfully Delivered\n",
        "Email Content:",
        "-------------"
    ])

    # Email Content
    common_headers = mail_info.get('commonHeaders', {})
    formatted_sections.extend([
        f"From: {', '.join(common_headers.get('from', ['N/A']))}",
        "To:",
        *[f"  - {dest}" for dest in mail_info.get('destination', [])],
        f"Subject: {common_headers.get('subject', 'N/A')}\n",
        "AWS Details:",
        "-----------"
    ])

    # AWS Details
    formatted_sections.extend([
        f"Source ARN: {mail_info.get('sourceArn', 'N/A')}",
        f"Source IP: {mail_info.get('sourceIp', 'N/A')}",
        f"Message ID: {mail_info.get('messageId', 'N/A')}",
        f"Account ID: {mail_info.get('sendingAccountId', 'N/A')}",
        f"Caller Identity: {mail_info.get('callerIdentity', 'N/A')}\n",
        "Delivery Information:",
        "-------------------"
    ])

    # Delivery Information
    delivery_info = message_content.get('delivery', {})
    formatted_sections.extend([
        f"Processing Time: {delivery_info.get('processingTimeMillis', 'N/A')} milliseconds",
        f"SMTP Response: {delivery_info.get('smtpResponse', 'N/A')}",
        f"Remote MTA IP: {delivery_info.get('remoteMtaIp', 'N/A')}",
        f"Reporting MTA: {delivery_info.get('reportingMTA', 'N/A')}"
    ])

    return '\n'.join(formatted_sections)

def lambda_handler(event, context):
    try:
        primary_webhook_url = os.environ['GOOGLE_CHAT_WEBHOOK_URL']
        security_webhook_url = os.environ.get('SECURITY_CHAT_WEBHOOK_URL', primary_webhook_url)
        logger.info("Webhook URLs retrieved from environment variable")
        sns_message = event['Records'][0]['Sns']
        logger.info(f"Received SNS message: {sns_message}")
        
        # Parse the message content
        message_content = json.loads(sns_message['Message'])
        
        # Determine which webhook to use based on the subject line
        webhook_url = primary_webhook_url
        
        # Check if this is an email notification with a subject line
        if 'subject' in message_content['mail']['commonHeaders']:
            subject = message_content['mail']['commonHeaders']['subject']
            print("Subject:", subject)
            
            # Case-insensitive check for "Email Threat Detected" in the subject
            if re.search(r'email\s+threat\s+detected', subject, re.IGNORECASE):
                webhook_url = security_webhook_url
                logger.info(f"Routing message with subject '{subject}' to security webhook")
        
        formatted_message = format_message(message_content)
        
        message = {
            'text': f"SNS Topic: {sns_message['TopicArn'].split(':')[-1]}\n\nMessage: {formatted_message}"
        }
        
        encoded_message = json.dumps(message).encode('utf-8')
        
        response = http.request('POST', webhook_url, body=encoded_message, headers={'Content-Type': 'application/json'})
        logger.info(f"Posted message to Google Chat, response status: {response.status}")
        
        return {
            'statusCode': response.status,
            'body': response.data.decode('utf-8')
        }
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise e
