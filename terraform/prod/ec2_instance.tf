module "nats" {
  source                                  = "../modules/ec2_instance"
  region                                  = var.region
  availability_zone                       = var.nats_availability_zone
  instance_type                           = "t2.small"
  root_block_device_volume_size           = 200
  root_block_device_iops                  = var.nats_root_block_device_iops
  root_block_device_throughput            = var.nats_root_block_device_throughput
  root_block_device_volume_type           = var.nats_root_block_device_volume_type
  subnet_id                               = module.main_private_subnet_2.subnet_id
  vpc_security_group_ids                  = ["${aws_security_group.nats_sg.id}"]
  key_name                                = var.ssh_key_name
  iam_instance_profile                    = aws_iam_instance_profile.CloudWatchAgentServerProfile.name
  root_block_device_encrypted             = true
  root_block_device_delete_on_termination = false

  tags = var.nats_instance_tags
}

module "bastion" {
  source                                  = "../modules/ec2_instance"
  region                                  = var.region
  associate_public_ip_address             = true
  availability_zone                       = var.bastion_availability_zone
  instance_type                           = var.bastion_instance_type
  root_block_device_volume_size           = var.bastion_root_block_device_volume_size
  root_block_device_iops                  = var.bastion_root_block_device_iops
  root_block_device_throughput            = var.bastion_root_block_device_throughput
  root_block_device_volume_type           = var.bastion_root_block_device_volume_type
  subnet_id                               = module.main_public_subnet_1.subnet_id
  vpc_security_group_ids                  = ["${aws_security_group.bastion_sg.id}"]
  key_name                                = var.ssh_key_name
  iam_instance_profile                    = aws_iam_instance_profile.bastion_iam_profile.name
  root_block_device_encrypted             = true
  root_block_device_delete_on_termination = false

  tags = var.bastion_instance_tags
}

module "monitoring" {
  source                                  = "../modules/ec2_instance"
  region                                  = var.region
  associate_public_ip_address             = false
  availability_zone                       = var.monitoring_availability_zone
  instance_type                           = var.monitoring_instance_type
  root_block_device_volume_size           = var.monitoring_root_block_device_volume_size
  root_block_device_iops                  = var.monitoring_root_block_device_iops
  root_block_device_throughput            = var.monitoring_root_block_device_throughput
  root_block_device_volume_type           = var.monitoring_root_block_device_volume_type
  subnet_id                               = module.main_private_subnet_1.subnet_id
  vpc_security_group_ids                  = ["${aws_security_group.monitoring_sg.id}"]
  key_name                                = var.ssh_key_name
  monitoring                              = var.monitor_monitoring_instance
  root_block_device_encrypted             = true
  root_block_device_delete_on_termination = false

  service_discovery_service_name = "monitoring"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  iam_instance_profile = aws_iam_instance_profile.CloudWatchAgentServerProfile.name

  tags = var.monitoring_instance_tags

  root_block_device_tags = {
    Snapshot = "true"
  }
}
