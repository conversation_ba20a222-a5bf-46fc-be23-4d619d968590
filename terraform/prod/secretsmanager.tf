resource "aws_secretsmanager_secret" "NATS_TI_USERNAME" {
  name                    = "NATS_TI_USERNAME"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.nat_ti_username_tags
}

resource "aws_secretsmanager_secret" "NATS_TI_PASSWORD" {
  name                    = "NATS_TI_PASSWORD"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.nat_ti_password_tags
}

resource "aws_secretsmanager_secret" "GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET" {
  name                    = "GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.gateway_microsoft_suscription_tags
}

resource "aws_secretsmanager_secret" "PG_PASSWORD" {
  name                    = "PG_PASSWORD"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.pg_password_tags
}

resource "aws_secretsmanager_secret" "GROQ_API_KEY" {
  name                    = "GROQ_API_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.groq_api_key_tags
}

resource "aws_secretsmanager_secret" "INTENT_EXTRACTION_MODEL_API_KEY" {
  name                    = "INTENT_EXTRACTION_MODEL_API_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.intent_extraction_model_api_key_tags
}

resource "aws_secretsmanager_secret" "URL_SCAN_API_KEY" {
  name                    = "URL_SCAN_API_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.url_scan_api_key_tags
}

resource "aws_secretsmanager_secret" "VIRUSTOTAL_API_KEY" {
  name                    = "VIRUSTOTAL_API_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.virustotal_api_key_tags
}

resource "aws_secretsmanager_secret" "KEYCLOAK_ADMIN_PASSWORD" {
  name                    = "KEYCLOAK_PROD_ADMIN_PASSWORD"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.keycloak_admin_username_tags
}

resource "aws_secretsmanager_secret" "KEYCLOAK_ADMIN_USERNAME" {
  name                    = "KEYCLOAK_PROD_ADMIN_USERNAME"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.keycloak_admin_password_tags
}

# resource "aws_secretsmanager_secret" "KEYCLOAK_ADMIN_CLIENT_ID" {
#   name = "KEYCLOAK_ADMIN_CLIENT_ID"

#   tags = var.keycloak_admin_client_id_tags
# }

resource "aws_secretsmanager_secret" "KEYCLOAK_PG_PASSWORD" {
  name                    = "KEYCLOAK_PG_PASSWORD"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.keycloak_pg_password_tags
}

# resource "aws_secretsmanager_secret" "KEYCLOAK_ADMIN_CLIENT_SECRET" {
#   name = "KEYCLOAK_ADMIN_CLIENT_SECRET"

#   tags = var.keycloak_admin_client_secret_tags
# }

# resource "aws_secretsmanager_secret" "KEYCLOAK_SERVICE_ACCOUNT_ID" {
#   name = "KEYCLOAK_SERVICE_ACCOUNT_ID"

#   tags = var.keycloak_service_account_id_tags
# }

resource "aws_secretsmanager_secret" "DETECTION_MODEL_API_KEY" {
  name                    = "DETECTION_MODEL_API_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.detection_model_api_key_tags
}

resource "aws_secretsmanager_secret" "DETECTION_MODEL_ENDPOINT" {
  name                    = "DETECTION_MODEL_ENDPOINT"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.detection_model_endpoint_tags
}

resource "aws_secretsmanager_secret" "DETECTION_MODEL_DEPLOYMENT" {
  name                    = "DETECTION_MODEL_DEPLOYMENT"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.detection_model_deployment_tags
}

resource "aws_secretsmanager_secret" "KEYCLOAK_ADMIN_CLIENT_ID" {
  name                    = "KEYCLOAK_ADMIN_CLIENT_ID"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.keycloak_admin_client_id_tags
}

resource "aws_secretsmanager_secret" "KEYCLOAK_ADMIN_CLIENT_SECRET" {
  name                    = "KEYCLOAK_ADMIN_CLIENT_SECRET"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.keycloak_admin_client_secret_tags
}

resource "aws_secretsmanager_secret" "KEYCLOAK_SERVICE_ACCOUNT_ID" {
  name                    = "KEYCLOAK_SERVICE_ACCOUNT_ID"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.keycloak_service_account_id_tags
}

resource "aws_secretsmanager_secret" "MS_AUTH_CLIENT_ID" {
  name                    = "MS_AUTH_CLIENT_ID"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.ms_auth_client_id_secret_tags
}

resource "aws_secretsmanager_secret" "MS_AUTH_CLIENT_SECRET" {
  name                    = "MS_AUTH_CLIENT_ID_SECRET"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.ms_auth_client_secret_tags
}

resource "aws_secretsmanager_secret" "MS_AUTH_TENANT_ID" {
  name                    = "MS_AUTH_TENANT_ID"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = var.ms_auth_tenant_id_tags
}

resource "aws_secretsmanager_secret" "ATTACHMENT_MODEL_API_KEY" {
  name                    = "ATTACHMENT_MODEL_API_KEY"
  recovery_window_in_days = 7
  kms_key_id              = aws_kms_key.secretsmanager.key_id

  tags = {
    Name        = "attachment-model-api-key-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_secretsmanager_secret" "GW_AUTH_CLIENT_ID" {
  name                    = "GW_AUTH_CLIENT_ID"
  recovery_window_in_days = 7
  kms_key_id              = aws_kms_key.secretsmanager.key_id

  tags = {
    Name        = "gw-auth-client-id-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_secretsmanager_secret" "GW_AUTH_CLIENT_SECRET" {
  name                    = "GW_AUTH_CLIENT_SECRET"
  recovery_window_in_days = 7
  kms_key_id              = aws_kms_key.secretsmanager.key_id

  tags = {
    Name        = "gw-auth-client-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_secretsmanager_secret" "GW_SERVICE_ACCOUNT" {
  name                    = "GW_SERVICE_ACCOUNT"
  recovery_window_in_days = 7
  kms_key_id              = aws_kms_key.secretsmanager.key_id

  tags = {
    Name        = "gw-service-account-secret-tag"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC" {
  name                    = "GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC"
  recovery_window_in_days = 7
  kms_key_id              = aws_kms_key.secretsmanager.key_id


  tags = {
    Name        = "gw-google-subscription-topic-secret-tag"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "INGESTION_GW_SERVICE_ACCOUNT" {
  name                    = "INGESTION_GW_SERVICE_ACCOUNT"
  recovery_window_in_days = 7
  kms_key_id              = aws_kms_key.secretsmanager.key_id

  tags = {
    Name        = "ingestion-gw-service-account-tag"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "NATS_SERVER_URL" {
  name                    = "NATS_SERVER_URL"
  recovery_window_in_days = 7
  kms_key_id              = aws_kms_key.secretsmanager.key_id

  tags = {
    Name        = "nats-server-url-tag"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "ARGILLA_API_KEY" {
  name                    = "ARGILLA_API_KEY"
  recovery_window_in_days = 7
  kms_key_id              = aws_kms_key.secretsmanager.key_id

  tags = {
    Name        = "argilla-api-key-secret"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "SMARTHOST_TLS_CERT" {
  name                    = "SMARTHOST_TLS_CERT"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "smarthost-tls-cert-secret"
    Environment = "prod"
    Team        = "infra"
    Product     = "inline"
  }
}

resource "aws_secretsmanager_secret" "SMARTHOST_TLS_KEY" {
  name                    = "SMARTHOST_TLS_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "smarthost-tls-key-secret"
    Environment = "prod"
    Team        = "infra"
    Product     = "inline"
  }
}

resource "aws_secretsmanager_secret" "DKIM_PRIVATE_KEY" {
  name                    = "DKIM_PRIVATE_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "dkim-private-key"
    Environment = "prod"
    Team        = "infra"
    Product     = "inline"
  }
}

resource "aws_secretsmanager_secret" "DLP_MODEL_CREDENTIALS" {
  name                    = "DLP_MODEL_CREDENTIALS"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "dlp-model-credentials-secret"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "CLASSIFICATION_OAI_API_KEY" {
  name                    = "CLASSIFICATION_OAI_API_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "classification-oai-api-key"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "SPLUNK_ORGANIZATIONS_KEY" {
  name                    = "SPLUNK_ORGANIZATIONS_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "splunk-organization-secret"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "JWT_SECRET" {
  name                    = "JWT_SECRET"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "jwt-secret"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}


resource "aws_secretsmanager_secret" "ATTACHMENT_SCAN_ENABLED_ORGS" {
  name                    = "ATTACHMENT_SCAN_ENABLED_ORGS"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "attachment-scan-enabled-orgs"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "CLICKSTACK_PASSWORD" {
  name                    = "CLICKSTACK_PASSWORD"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "clickstack-password"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "CLICKSTACK_USER" {
  name                    = "CLICKSTACK_USER"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "clickstack-user"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}


resource "aws_secretsmanager_secret" "ANALYTICS_EXPORTER_RDS_USER" {
  name                    = "ANALYTICS_EXPORTER_RDS_USER"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "analytics-exporter-rds-user"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "ANALYTICS_EXPORTER_RDS_PASSWORD" {
  name                    = "ANALYTICS_EXPORTER_RDS_PASSWORD"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "analytics-exporter-rds-password"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "ANALYTICS_EXPORTER_CLICKHOUSE_USER" {
  name                    = "ANALYTICS_EXPORTER_CLICKHOUSE_USER"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "analytics-exporter-clickstack-user"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_secretsmanager_secret" "ANALYTICS_EXPORTER_CLICKHOUSE_PASSWORD" {
  name                    = "ANALYTICS_EXPORTER_CLICKHOUSE_PASSWORD"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Name        = "analytics-exporter-clickstack-password"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}
