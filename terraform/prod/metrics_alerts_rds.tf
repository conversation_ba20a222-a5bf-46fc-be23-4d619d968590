# Ravenclaw Primary
resource "aws_cloudwatch_metric_alarm" "rds_cpu_utilization" {
  alarm_name          = var.RDS_CPU_Utilization_alarm_name
  comparison_operator = var.RDS_CPU_Utilization_comparison_operator
  evaluation_periods  = var.RDS_CPU_Utilization_evaluation_periods
  datapoints_to_alarm = "5"
  metric_name         = var.RDS_CPU_Utilization_metric_name
  namespace           = var.rds_alarm_namespace
  period              = var.rds_cpu_utilization_alarm_period
  statistic           = var.rds_cpu_utilization_alarm_station
  threshold           = var.rds_cpu_utilization_threshold
  alarm_description   = var.rds_cpu_utilization_alarm_description
  actions_enabled     = var.rds_cpu_utilization_actions_enabled
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = var.identifier
  }

  tags = {
    Name        = var.RDS_CPU_Utilization_alarm_name
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_connections" {
  alarm_name          = var.RDS_Connections_alarm_name
  comparison_operator = var.RDS_Connections_comparison_operator
  evaluation_periods  = var.RDS_Connections_evaluation_periods
  metric_name         = var.RDS_Connections_metric_name
  namespace           = var.rds_alarm_namespace
  period              = var.rds_connections_alarm_period
  statistic           = var.rds_connections_alarm_statistic
  threshold           = var.RDS_Connections_threshold
  alarm_description   = var.RDS_Connections_alarm_description
  actions_enabled     = var.RDS_Connections_actions_enabled
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = var.identifier
  }

  tags = {
    Name        = var.RDS_Connections_alarm_name
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_freeable_memory" {
  alarm_name          = var.RDS_Freeable_Memory_alarm_name
  comparison_operator = var.RDS_Freeable_Memory_comparison_operator
  evaluation_periods  = var.RDS_Freeable_Memory_evaluation_periods
  metric_name         = var.RDS_Freeable_Memory_metric_name
  namespace           = var.rds_alarm_namespace
  period              = var.rds_freeable_memory_alarm_period
  statistic           = var.rds_freeable_memory_alarm_statistic
  threshold           = var.RDS_Freeable_Memory_threshold
  alarm_description   = var.RDS_Freeable_Memory_alarm_description
  actions_enabled     = var.RDS_Freeable_Memory_actions_enabled
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = var.identifier
  }

  tags = {
    Name        = var.RDS_Freeable_Memory_alarm_name
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

# Ravenclaw Read Replica - 1
resource "aws_cloudwatch_metric_alarm" "rds_read_replica_1_cpu_utilization" {
  alarm_name          = "${var.RDS_CPU_Utilization_alarm_name}-read-replica-1"
  comparison_operator = var.RDS_CPU_Utilization_comparison_operator
  evaluation_periods  = var.RDS_CPU_Utilization_evaluation_periods
  metric_name         = var.RDS_CPU_Utilization_metric_name
  namespace           = var.rds_alarm_namespace
  period              = var.rds_cpu_utilization_alarm_period
  statistic           = var.rds_cpu_utilization_alarm_station
  threshold           = var.rds_cpu_utilization_threshold
  alarm_description   = var.rds_cpu_utilization_alarm_description
  actions_enabled     = var.rds_cpu_utilization_actions_enabled
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = "${var.identifier}-read-replica-1"
  }

  tags = {
    Name        = "${var.RDS_CPU_Utilization_alarm_name}-read-replica-1"
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_read_replica_1_connections" {
  alarm_name          = "${var.RDS_Connections_alarm_name}-read-replica-1"
  comparison_operator = var.RDS_Connections_comparison_operator
  evaluation_periods  = var.RDS_Connections_evaluation_periods
  metric_name         = var.RDS_Connections_metric_name
  namespace           = var.rds_alarm_namespace
  period              = var.rds_connections_alarm_period
  statistic           = var.rds_connections_alarm_statistic
  threshold           = 100
  alarm_description   = var.RDS_Connections_alarm_description
  actions_enabled     = var.RDS_Connections_actions_enabled
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = "${var.identifier}-read-replica-1"
  }

  tags = {
    Name        = "${var.RDS_Connections_alarm_name}-read-replica-1"
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_read_replica_1_freeable_memory" {
  alarm_name          = "${var.RDS_Freeable_Memory_alarm_name}-read-replica-1"
  comparison_operator = var.RDS_Freeable_Memory_comparison_operator
  evaluation_periods  = var.RDS_Freeable_Memory_evaluation_periods
  metric_name         = var.RDS_Freeable_Memory_metric_name
  namespace           = var.rds_alarm_namespace
  period              = var.rds_freeable_memory_alarm_period
  statistic           = var.rds_freeable_memory_alarm_statistic
  threshold           = var.RDS_Freeable_Memory_threshold
  alarm_description   = var.RDS_Freeable_Memory_alarm_description
  actions_enabled     = var.RDS_Freeable_Memory_actions_enabled
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = "${var.identifier}-read-replica-1"
  }

  tags = {
    Name        = "${var.RDS_Freeable_Memory_alarm_name}-read-replica-1"
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

# Keycloak Primary
resource "aws_cloudwatch_metric_alarm" "keycloak_rds_cpu_utilization" {
  alarm_name          = var.KEYCLOAK_RDS_CPU_Utilization_alarm_name
  comparison_operator = var.KEYCLOAK_RDS_CPU_Utilization_comparison_operator
  evaluation_periods  = var.KEYCLOAK_RDS_CPU_Utilization_evaluation_periods
  metric_name         = var.KEYCLOAK_RDS_CPU_Utilization_metric_name
  namespace           = var.keycloak_rds_alarm_namespace
  period              = var.keycloak_rds_cpu_utilization_alarm_period
  statistic           = var.keycloak_rds_cpu_utilization_alarm_station
  threshold           = var.keycloak_rds_cpu_utilization_threshold
  alarm_description   = var.keycloak_rds_cpu_utilization_alarm_description
  actions_enabled     = var.keycloak_rds_cpu_utilization_actions_enabled
  alarm_actions       = [aws_sns_topic.keycloak_rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = var.keycloak_identifier
  }

  tags = {
    Name        = var.KEYCLOAK_RDS_CPU_Utilization_alarm_name
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_rds_connections" {
  alarm_name          = var.KEYCLOAK_RDS_Connections_alarm_name
  comparison_operator = var.KEYCLOAK_RDS_Connections_comparison_operator
  evaluation_periods  = var.KEYCLOAK_RDS_Connections_evaluation_periods
  metric_name         = var.KEYCLOAK_RDS_Connections_metric_name
  namespace           = var.keycloak_rds_alarm_namespace
  period              = var.keycloak_rds_connections_alarm_period
  statistic           = var.keycloak_rds_connections_alarm_statistic
  threshold           = var.KEYCLOAK_RDS_Connections_threshold
  alarm_description   = var.KEYCLOAK_RDS_Connections_alarm_description
  actions_enabled     = var.KEYCLOAK_RDS_Connections_actions_enabled
  alarm_actions       = [aws_sns_topic.keycloak_rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = var.keycloak_identifier
  }

  tags = {
    Name        = var.KEYCLOAK_RDS_Connections_alarm_name
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_rds_freeable_memory" {
  alarm_name          = var.KEYCLOAK_RDS_Freeable_Memory_alarm_name
  comparison_operator = var.KEYCLOAK_RDS_Freeable_Memory_comparison_operator
  evaluation_periods  = var.KEYCLOAK_RDS_Freeable_Memory_evaluation_periods
  metric_name         = var.KEYCLOAK_RDS_Freeable_Memory_metric_name
  namespace           = var.keycloak_rds_alarm_namespace
  period              = var.keycloak_rds_freeable_memory_alarm_period
  statistic           = var.keycloak_rds_freeable_memory_alarm_statistic
  threshold           = var.KEYCLOAK_RDS_Freeable_Memory_threshold
  alarm_description   = var.KEYCLOAK_RDS_Freeable_Memory_alarm_description
  actions_enabled     = var.KEYCLOAK_RDS_Freeable_Memory_actions_enabled
  alarm_actions       = [aws_sns_topic.keycloak_rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = var.keycloak_identifier
  }

  tags = {
    Name        = var.KEYCLOAK_RDS_Freeable_Memory_alarm_name
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

# Keycloak Replica
resource "aws_cloudwatch_metric_alarm" "keycloak_rds_read_replica_1_cpu_utilization" {
  alarm_name          = "${var.KEYCLOAK_RDS_CPU_Utilization_alarm_name}-read-replica-1"
  comparison_operator = var.KEYCLOAK_RDS_CPU_Utilization_comparison_operator
  evaluation_periods  = var.KEYCLOAK_RDS_CPU_Utilization_evaluation_periods
  metric_name         = var.KEYCLOAK_RDS_CPU_Utilization_metric_name
  namespace           = var.keycloak_rds_alarm_namespace
  period              = var.keycloak_rds_cpu_utilization_alarm_period
  statistic           = var.keycloak_rds_cpu_utilization_alarm_station
  threshold           = var.keycloak_rds_cpu_utilization_threshold
  alarm_description   = var.keycloak_rds_cpu_utilization_alarm_description
  actions_enabled     = var.keycloak_rds_cpu_utilization_actions_enabled
  alarm_actions       = [aws_sns_topic.keycloak_rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = "${var.keycloak_identifier}-read-replica-1"
  }

  tags = {
    Name        = "${var.KEYCLOAK_RDS_CPU_Utilization_alarm_name}-read-replica-1"
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_rds_read_replica_1_connections" {
  alarm_name          = "${var.KEYCLOAK_RDS_Connections_alarm_name}-read-replica-1"
  comparison_operator = var.KEYCLOAK_RDS_Connections_comparison_operator
  evaluation_periods  = var.KEYCLOAK_RDS_Connections_evaluation_periods
  metric_name         = var.KEYCLOAK_RDS_Connections_metric_name
  namespace           = var.keycloak_rds_alarm_namespace
  period              = var.keycloak_rds_connections_alarm_period
  statistic           = var.keycloak_rds_connections_alarm_statistic
  threshold           = var.KEYCLOAK_RDS_Connections_threshold
  alarm_description   = var.KEYCLOAK_RDS_Connections_alarm_description
  actions_enabled     = var.KEYCLOAK_RDS_Connections_actions_enabled
  alarm_actions       = [aws_sns_topic.keycloak_rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = "${var.keycloak_identifier}-read-replica-1"
  }

  tags = {
    Name        = "${var.KEYCLOAK_RDS_Connections_alarm_name}-read-replica-1"
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_rds_read_replica_1_freeable_memory" {
  alarm_name          = "${var.KEYCLOAK_RDS_Freeable_Memory_alarm_name}-read-replica-1"
  comparison_operator = var.KEYCLOAK_RDS_Freeable_Memory_comparison_operator
  evaluation_periods  = var.KEYCLOAK_RDS_Freeable_Memory_evaluation_periods
  metric_name         = var.KEYCLOAK_RDS_Freeable_Memory_metric_name
  namespace           = var.keycloak_rds_alarm_namespace
  period              = var.keycloak_rds_freeable_memory_alarm_period
  statistic           = var.keycloak_rds_freeable_memory_alarm_statistic
  threshold           = var.KEYCLOAK_RDS_Freeable_Memory_threshold
  alarm_description   = var.KEYCLOAK_RDS_Freeable_Memory_alarm_description
  actions_enabled     = var.KEYCLOAK_RDS_Freeable_Memory_actions_enabled
  alarm_actions       = [aws_sns_topic.keycloak_rds_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = "${var.keycloak_identifier}-read-replica-1"
  }

  tags = {
    Name        = "${var.KEYCLOAK_RDS_Freeable_Memory_alarm_name}-read-replica-1"
    Environment = "prod"
    Team        = "infra"
    Application = "rds"
    Product     = "ravenclaw"
  }
}
