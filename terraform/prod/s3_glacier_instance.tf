resource "tls_private_key" "s3_glacier" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "aws_key_pair" "s3_glacier" {
  key_name   = "s3-glacier-ssh-prod-key"
  public_key = tls_private_key.s3_glacier.public_key_openssh
  tags = {
    Name        = "s3-glacier-ssh-prod-key"
    Environment = "prod"
    Application = "s3-glacier"
    Team        = "infra"
    Product     = "storage"
  }
}

# resource "local_file" "s3_glacier-ssh-key" {
#   content              = tls_private_key.s3_glacier.private_key_pem
#   filename             = "s3-glacier-ssh-prod-key.pem"
#   file_permission      = "0600"
#   directory_permission = "0700"
# }

data "aws_ami" "s3_glacier_ami" {
  most_recent = true
  owners      = ["amazon"]
  filter {
    name   = "architecture"
    values = ["arm64"]
  }
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*"]
  }
}

module "s3_glacier_sg" {
  source      = "../modules/security_groups"
  region      = var.region
  name        = "s3-glacier-security-groups"
  description = "sg for s3-glacier instance"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "s3-glacier-security-groups"
    Team        = "infra"
    Product     = "storage"
    Application = "s3-glacier"
    Environment = "prod"
  }

  ingress_custom_rules = [
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 22
      to_port     = 22
      ip_protocol = "tcp"
      description = "ssh port"
    },
  ]

  # ───────────────────────────────────────────────────────────
  # egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}


module "s3_glacier" {
  ami                                     = data.aws_ami.s3_glacier_ami.id
  source                                  = "../modules/ec2_instance"
  region                                  = var.region
  associate_public_ip_address             = false
  availability_zone                       = "ap-south-1b"
  instance_type                           = "t4g.xlarge"
  root_block_device_volume_size           = "200"
  root_block_device_iops                  = 3000
  root_block_device_throughput            = 125
  root_block_device_volume_type           = "gp3"
  subnet_id                               = module.main_private_subnet_2.subnet_id
  vpc_security_group_ids                  = ["${module.s3_glacier_sg.security_group_id}"]
  key_name                                = aws_key_pair.s3_glacier.key_name
  monitoring                              = true
  root_block_device_encrypted             = true
  root_block_device_delete_on_termination = false

  service_discovery_service_name = "s3-glacier"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  iam_instance_profile = aws_iam_instance_profile.s3_glacier_ec2_instance_profile.name

  # attach_external_disk       = true
  # external_volume_size       = 100
  # external_volume_type       = "gp3"
  # external_volume_encrypted  = true
  # external_volume_iops       = 3000
  # external_volume_throughput = 125

  tags = {
    Name        = "s3-glacier-instance"
    Environment = "prod"
    Application = "s3-glacier"
    Team        = "infra"
    Product     = "storage"
  }

  root_block_device_tags = {
    snapshot = "true"
  }
}

resource "aws_iam_role" "s3_glacier_ec2_role" {
  name = "s3-glacier-prod-ec2-role"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "ec2.amazonaws.com"
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })
}



# define the iam policy for the ec2 role
resource "aws_iam_policy" "s3_glacier_ec2_policy" {
  name = "s3-glacier-prod-ec2-policy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:*"
        ],
        "Resource" : [
          "arn:aws:s3:::*"
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:*"
        ],
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "ecr:Get*",
          "ecr:Describe*",
          "ecr:Batch*",
          "ecr:List*",
          "ecr:Validate*",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:CreateLogGroup",
          "logs:DescribeLogStreams",
          "ses:Create*",
          "ses:Send*"
        ],
        "Resource" : "*"
      }
    ]
  })
}



# attach the policy to the role
resource "aws_iam_role_policy_attachment" "s3_glacier_ec2_role_policy_attachment" {
  role       = aws_iam_role.s3_glacier_ec2_role.name
  policy_arn = aws_iam_policy.s3_glacier_ec2_policy.arn
}

# define the iam instance profile
resource "aws_iam_instance_profile" "s3_glacier_ec2_instance_profile" {
  name = "s3-glacier-ec2-instance-prod-profile"
  role = aws_iam_role.s3_glacier_ec2_role.name
}
