variable "NATS_SERVER_PORT" {
  type    = string
  default = 4222
}

variable "PG_DB" {
  type = string
}

variable "PG_PORT" {
  type = string
}

variable "SUBJECT" {
  type = string
}

variable "SUBJECT_DETECT" {
  type = string
}

variable "SUBJECT_ENRICHED" {
  type = string
}

# bff
variable "BFF_HOST" {
  type    = string
  default = "0.0.0.0"
}

variable "BFF_PORT" {
  type    = string
  default = "8080"
}

variable "PG_SSL" {
  type    = string
  default = "disable"
}

variable "bff_image_uri" {
  type = string
}

variable "bff_logs_group" {
  type    = string
  default = "/ecs/bff"
}

variable "bff_docker_image_name" {
  type    = string
  default = "bff"
}

variable "bff_cpu" {
  type = number
}

variable "bff_memory" {
  type = number
}


# Gateway
variable "GATEWAY_MICROSOFT_WEBHOOK_HOST" {
  type = string
}

variable "GATEWAY_MICROSOFT_WEBHOOK_PATH" {
  type = string
}

variable "GATEWAY_MICROSOFT_WEBHOOK_PORT" {
  type = string
}

variable "GATEWAY_PUBLIC_HOST" {
  type = string
}

variable "gateway_image_uri" {
  type = string
}

variable "gateway_logs_group" {
  type    = string
  default = "/ecs/gateway"
}

variable "gateway_docker_image_name" {
  type    = string
  default = "gateway"
}

variable "gateway_cpu" {
  type = number
}

variable "gateway_memory" {
  type = number
}

# Remediator
variable "remediator_cpu" {
  type = number
}

variable "remediator_LOG_ROTATION_FILE" {
  type    = string
  default = "remediator"
}

variable "remediator_LOG_ROTATION_MAX_AGE" {
  type    = string
  default = "90"
}

variable "remediator_image_uri" {
  type = string
}

variable "remediator_logs_group" {
  type    = string
  default = "/ecs/remediator"
}

variable "remediator_docker_image_name" {
  type    = string
  default = "remediator"
}

variable "remediator_memory" {
  type = number
}

# setup
variable "setup_image_uri" {
  type = string
}

variable "setup_logs_group" {
  type    = string
  default = "/ecs/setup"
}

variable "setup_docker_image_name" {
  type    = string
  default = "setup"
}

variable "setup_cpu" {
  type = number
}

variable "setup_memory" {
  type = number
}

variable "ASYNC_MAX_AT_ONCE_VT" {
  type    = string
  default = "10"
}

variable "ASYNC_MAX_PER_SECOND_VT" {
  type    = string
  default = "10"
}

variable "INTENT_EXTRACTION_MODEL_DEPLOYMENT" {
  type    = string
  default = "tone-and-intent-model"
}

variable "INTENT_EXTRACTION_MODEL_ENDPOINT" {
  type    = string
  default = "https://ti-service.openai.azure.com"
}

variable "INTENT_EXTRACTION_MODEL_API_VERSION" {
  type    = string
  default = "2024-08-01-preview"
}

variable "MODEL_PATH" {
  type    = string
  default = "/app/data/spacy_model"
}

variable "gotenberg_image_uri" {
  type = string
}

variable "gotenberg_logs_group" {
  type    = string
  default = "/ecs/gotenberg"
}

variable "gotenberg_docker_image_name" {
  type    = string
  default = "gotenberg"
}

variable "ti_go_service_image_uri" {
  type = string
}

variable "ti_go_service_logs_group" {
  type    = string
  default = "/ecs/ti-go-service"
}

variable "ti_go_service_docker_image_name" {
  type = string
}

variable "ti_go_service_cpu" {
  type = number
}

variable "ti_go_service_memory" {
  type = number
}

variable "ingestion_image_uri" {
  type = string
}

variable "ingestion_logs_group" {
  type    = string
  default = "/ecs/ingestion"
}

variable "ingestion_docker_image_name" {
  type = string
}

variable "ingestion_cpu" {
  type = number
}

variable "ingestion_memory" {
  type = number
}

variable "ATTACHMENT_MODEL_API_KEY" {
  type = string
}

variable "ATTACHMENT_MODEL_ENDPOINT" {
  type = string
}

variable "ATTACHMENT_MODEL_DEPLOYMENT" {
  type = string
}

variable "nats_command_image_uri" {
  type = string
}

variable "nats_command_logs_group" {
  type    = string
  default = "/ecs/nats/command"
}

variable "nats_command_docker_image_name" {
  type    = string
  default = "nats-command"
}

variable "nats_command_cpu" {
  type = number
}

variable "nats_command_memory" {
  type = number
}

variable "gateway_subcommand_image_uri" {
  type = string
}

variable "gateway_subcommand_logs_group" {
  type    = string
  default = "/ecs/gateway/subcommand"
}

variable "gateway_subcommand_docker_image_name" {
  type    = string
  default = "gateway-subcommand"
}

variable "gateway_subcommand_cpu" {
  type = number
}

variable "gateway_subcommand_memory" {
  type = number
}

variable "annotation_pipeline_docker_image" {
  type = string
}

variable "inline_image_uri" {
  type = string
}

variable "inline_log_group" {
  type    = string
  default = "/ecs/gateway-inline-prod"
}

variable "analytics_engine_image_uri" {
  type = string
}

variable "analytics_engine_cpu" {
  type = number
}

variable "analytics_engine_memory" {
  type = number
}

variable "ml_inference_ingestion_image_uri" {
  type = string
}
