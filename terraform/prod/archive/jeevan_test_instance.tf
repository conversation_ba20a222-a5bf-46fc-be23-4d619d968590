# data "aws_ami" "testbox_ami" {
#   most_recent = true
#   owners      = ["amazon"]
#   filter {
#     name   = "architecture"
#     values = ["x86_64"]
#   }
#   filter {
#     name   = "name"
#     values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-20240423"]
#   }
# }

# variable "jeevan_c_instance_tags" {
#   type = map(string)
#   default = {
#     Name        = "jeevan-chandra-test-instance"
#     Environment = "test"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
# }

# resource "aws_security_group" "jeevan_c_testbox_sg" {
#   description = "SG for test jeevan_c test box"

#   egress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "0"
#     protocol    = "-1"
#     self        = "false"
#     to_port     = "0"
#   }

#   ingress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "22"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "22"
#   }

#   ingress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "80"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "80"
#   }

#   ingress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "443"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "443"
#   }

#   ingress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "4444"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "4444"
#   }

#   ingress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "8080"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "8080"
#   }

#   ingress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "25"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "25"
#   }

#   name = "jeevan_testbox_sg"
#   tags = {
#     Name        = "jeevan-testbox-sg"
#     Environment = "test"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
# }


# resource "aws_instance" "jeevan_c_dev_box" {
#   ami                         = data.aws_ami.testbox_ami.id
#   associate_public_ip_address = true
#   availability_zone           = "ap-south-1c"
#   key_name                    = "jeevan_chandra_ssh_key"

#   disable_api_stop        = "false"
#   disable_api_termination = "false"
#   ebs_optimized           = "false"


#   get_password_data                    = "false"
#   hibernation                          = "false"
#   instance_initiated_shutdown_behavior = "stop"
#   instance_type                        = "t3a.small"
#   ipv6_address_count                   = 0

#   security_groups = ["${aws_security_group.jeevan_c_testbox_sg.name}"]

#   maintenance_options {
#     auto_recovery = "default"
#   }

#   metadata_options {
#     http_endpoint               = "enabled"
#     http_protocol_ipv6          = "disabled"
#     http_put_response_hop_limit = 2
#     http_tokens                 = "required"
#     instance_metadata_tags      = "disabled"
#   }

#   monitoring                 = false
#   placement_partition_number = 0

#   private_dns_name_options {
#     enable_resource_name_dns_a_record    = false
#     enable_resource_name_dns_aaaa_record = false
#     hostname_type                        = "ip-name"
#   }

#   root_block_device {
#     delete_on_termination = "true"
#     encrypted             = "false"
#     iops                  = 3000
#     throughput            = 125
#     volume_size           = 50
#     volume_type           = "gp3"
#     tags = merge(var.jeevan_c_instance_tags,
#       { Name = "${var.jeevan_c_instance_tags["Name"]}-volume" }
#     )
#   }
#   tags    = var.jeevan_c_instance_tags
#   tenancy = "default"
# }
