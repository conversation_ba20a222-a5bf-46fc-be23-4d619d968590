# variable "swagger_image_uri" {
#   type = string
# }

# module "swagger_task_definition" {
#   source = "../modules/ecs_task_definitions"
#   region = var.region
#   container_definitions_json = jsonencode([
#     {
#       "cpu" : 0,
#       "command" : [
#         "swagger"
#       ],
#       "environment" : [{
#         "name" : "BFF_SWAGGER_PORT",
#         "value" : "8082"
#         }, {
#         "name" : "BFF_HOST",
#         "value" : "ravenclaw.ravenmail.io"
#         }, {
#         "name" : "BFF_PORT",
#         "value" : "443"
#       }],
#       "environmentFiles" : [],
#       "essential" : true,
#       "image" : var.swagger_image_uri,
#       "logConfiguration" : {
#         "logDriver" : "awslogs",
#         "options" : {
#           "awslogs-create-group" : "true",
#           "awslogs-group" : "/ecs/swagger",
#           "awslogs-region" : var.region,
#           "awslogs-stream-prefix" : "ecs"
#         }
#       },
#       "secretOptions" : [],
#       "mountPoints" : [],
#       "name" : "swagger",
#       "portMappings" : [{
#         "appProtocol" : "http",
#         "containerPort" : 8082,
#         "hostPort" : 8082,
#         "name" : "http",
#         "protocol" : "tcp"
#       }],
#       "systemControls" : [],
#       "volumesFrom" : [],
#       "ulimits" : []
#   }])

#   cpu                = 512
#   execution_role_arn = aws_iam_role.task_execution_role.arn
#   family             = "swagger"
#   memory             = 1024
#   task_role_arn      = aws_iam_role.task_role.arn

#   tags = {
#     Name        = "swagger"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
# }

# resource "aws_security_group" "swagger_sg" {
#   description = "SG for prod swagger task"

#   egress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "0"
#     protocol    = "-1"
#     self        = "false"
#     to_port     = "0"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "8082"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "8082"
#   }

#   name = "swagger_sg"
#   tags = {
#     Name        = "swagger-sg"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
#   vpc_id = module.vpc_main.vpc_id
# }

# module "swagger_service" {
#   source                         = "../modules/ecs_service"
#   region                         = var.region
#   namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
#   service_discovery_service_name = "swagger"
#   cluster                        = module.ecs_cluster.cluster_name
#   security_groups_id             = [aws_security_group.swagger_sg.id]
#   subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   name                           = "swagger"
#   desired_count                  = 1
#   task_definition_arn            = module.swagger_task_definition.arn
#   target_group_arn               = aws_lb_target_group.swagger.arn
#   container_name                 = "swagger"
#   container_port                 = 8082

#   service_discovery_svc_tags = {
#     Name        = "ravenclaw-swagger-sd"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }

#   ecs_service_tags = {
#     Name        = "ravenclaw-swagger-svc"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "engineering"
#     Application = "go"
#   }
# }


# resource "aws_lb_target_group" "swagger" {
#   name        = "swagger-target-group"
#   target_type = "ip"
#   port        = 8082
#   protocol    = "HTTP"
#   vpc_id      = module.vpc_main.vpc_id
#   tags = {
#     Name        = "ravenclaw-lb-target-group-swagger"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
#   health_check {
#     path = "/swagger/index.html"
#   }
# }


# resource "aws_lb_listener_rule" "swagger" {
#   listener_arn = aws_lb_listener.main.arn
#   priority     = 102

#   action {
#     type             = "forward"
#     target_group_arn = aws_lb_target_group.swagger.arn
#   }

#   condition {
#     path_pattern {
#       values = ["/swagger/*"]
#     }
#   }

#   condition {
#     source_ip {
#       values = ["*************/32", "*************/32"]
#     }
#   }

#   tags = {
#     Name        = "ravenclaw-lb-listener-rule-swagger"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
# }
