resource "aws_sns_topic" "bff_log_alert" {
  name = "BFF_LOG_ALERT"
  tags = {
    Name        = "BFF_LOG_ALERT"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic" "gateway_log_alert" {
  name = "GATEWAY_LOG_ALERT"
  tags = {
    Name        = "GATEWAY_LOG_ALERT"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic" "remediator_log_alert" {
  name = "REMEDIATOR_LOG_ALERT"
  tags = {
    Name        = "REMEDIATOR_LOG_ALERT"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic" "ml_inference_log_alert" {
  name = "ML_INFERENCE_LOG_ALERT"
  tags = {
    Name        = "ML_INFERENCE_LOG_ALERT"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic" "ti_log_alert" {
  name = "TI_LOG_ALERT"
  tags = {
    Name        = "TI_LOG_ALERT"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}


variable "BFF_LOG_ALERTS_GCHAT_URL" {
  description = "Google Chat webhook URL for BFF service alerts"
  type        = string
}

variable "GATEWAY_LOG_ALERTS_GCHAT_URL" {
  description = "Google Chat webhook URL for Gateway service alerts"
  type        = string
}

variable "REMEDIATOR_LOG_ALERTS_GCHAT_URL" {
  description = "Google Chat webhook URL for Remediator service alerts"
  type        = string
}

variable "ML_INFERENCE_LOG_ALERTS_GCHAT_URL" {
  description = "Google Chat webhook URL for ML Inference service alerts"
  type        = string
}

variable "TI_LOG_ALERTS_GCHAT_URL" {
  description = "Google Chat webhook URL for TI service alerts"
  type        = string
}

# # BFF Lambda Function and related resources
resource "aws_lambda_function" "bff_alerts" {
  filename         = var.lambda_filename
  function_name    = "bffLogAlertsLambda"
  handler          = var.lambda_handler
  runtime          = var.lambda_runtime
  role             = aws_iam_role.lambda_exec.arn
  timeout          = 900
  source_code_hash = filebase64sha256(var.lambda_filename)

  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.BFF_LOG_ALERTS_GCHAT_URL
    }
  }

  tags = {
    Name        = "bffLogAlertsLambda"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_lambda_function" "gateway_alerts" {
  filename         = var.lambda_filename
  function_name    = "gatewayLogAlertsLambda"
  handler          = var.lambda_handler
  runtime          = var.lambda_runtime
  role             = aws_iam_role.lambda_exec.arn
  timeout          = 900
  source_code_hash = filebase64sha256(var.lambda_filename)

  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.GATEWAY_LOG_ALERTS_GCHAT_URL
    }
  }

  tags = {
    Name        = "gatewayLogAlertsLambda"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}


resource "aws_lambda_function" "remediator_alerts" {
  filename         = var.lambda_filename
  function_name    = "remediatorLogAlertsLambda"
  handler          = var.lambda_handler
  runtime          = var.lambda_runtime
  role             = aws_iam_role.lambda_exec.arn
  timeout          = 900
  source_code_hash = filebase64sha256(var.lambda_filename)

  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.REMEDIATOR_LOG_ALERTS_GCHAT_URL
    }
  }

  tags = {
    Name        = "remediatorLogAlertsLambda"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_lambda_function" "ml_inference_alerts" {
  filename         = var.lambda_filename
  function_name    = "mlInferenceLogAlertsLambda"
  handler          = var.lambda_handler
  runtime          = var.lambda_runtime
  role             = aws_iam_role.lambda_exec.arn
  timeout          = 900
  source_code_hash = filebase64sha256(var.lambda_filename)

  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.ML_INFERENCE_LOG_ALERTS_GCHAT_URL
    }
  }

  tags = {
    Name        = "mlInferenceLogAlertsLambda"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_lambda_function" "ti_alerts" {
  filename         = var.lambda_filename
  function_name    = "tiLogAlertsLambda"
  handler          = var.lambda_handler
  runtime          = var.lambda_runtime
  role             = aws_iam_role.lambda_exec.arn
  timeout          = 900
  source_code_hash = filebase64sha256(var.lambda_filename)

  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.TI_LOG_ALERTS_GCHAT_URL
    }
  }

  tags = {
    Name        = "tiLogAlertsLambda"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_lambda_permission" "allow_cloudwatch_bff" {
  statement_id  = var.lambda_permission_statement_id
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.bff_alerts.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.bff_log_alert.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_gateway" {
  statement_id  = var.lambda_permission_statement_id
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.gateway_alerts.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.gateway_log_alert.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_remediator" {
  statement_id  = var.lambda_permission_statement_id
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.remediator_alerts.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.remediator_log_alert.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_ml_inference" {
  statement_id  = var.lambda_permission_statement_id
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.ml_inference_alerts.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.ml_inference_log_alert.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_ti" {
  statement_id  = var.lambda_permission_statement_id
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.ti_alerts.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.ti_log_alert.arn
}

# # SNS Topic Subscriptions
resource "aws_sns_topic_subscription" "bff_alerts" {
  topic_arn = aws_sns_topic.bff_log_alert.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.bff_alerts.arn
}

resource "aws_sns_topic_subscription" "gateway_alerts" {
  topic_arn = aws_sns_topic.gateway_log_alert.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.gateway_alerts.arn
}

resource "aws_sns_topic_subscription" "remediator_alerts" {
  topic_arn = aws_sns_topic.remediator_log_alert.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.remediator_alerts.arn
}

resource "aws_sns_topic_subscription" "ml_inference_alerts" {
  topic_arn = aws_sns_topic.ml_inference_log_alert.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.ml_inference_alerts.arn
}

resource "aws_sns_topic_subscription" "ti_alerts" {
  topic_arn = aws_sns_topic.ti_log_alert.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.ti_alerts.arn
}

module "bff_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.bff_logs_group
  log_filter_name            = var.bff_log_filter_name
  log_pattern                = "[timestamp, level=ERROR*]"
  alarm_name                 = var.bff_log_alarm_name
  sns_topic_arn              = aws_sns_topic.bff_log_alert.arn
  metric_transformation_name = var.bff_metric_transformation_name
}

module "bff_panic_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.bff_logs_group
  log_filter_name            = "bff_panic_log_alarm"
  log_pattern                = "[timestamp, level=panic*]"
  alarm_name                 = "bff_panic_log_alarm"
  sns_topic_arn              = aws_sns_topic.bff_log_alert.arn
  metric_transformation_name = "BFF_PANIC_COUNT"
}

module "gateway_log_alert" {
  source          = "../modules/log_alerting"
  region          = var.region
  log_group_name  = var.gateway_logs_group
  log_filter_name = var.gateway_log_filter_name
  # log_pattern = var.log_pattern
  log_pattern                = "[timestamp, level=ERROR*]"
  alarm_name                 = var.gateway_log_alarm_name
  sns_topic_arn              = aws_sns_topic.gateway_log_alert.arn
  metric_transformation_name = var.gateway_metric_transformation_name
}

module "gateway_log_panic_alert" {
  source          = "../modules/log_alerting"
  region          = var.region
  log_group_name  = var.gateway_logs_group
  log_filter_name = "gateway_panic_log_alarm"
  # log_pattern = var.log_pattern
  log_pattern                = "[timestamp, level=panic*]"
  alarm_name                 = "gateway_panic_log_alarm"
  sns_topic_arn              = aws_sns_topic.gateway_log_alert.arn
  metric_transformation_name = "GATEWAY_PANIC_COUNT"
}

module "ti_go_service_error_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.ti_go_service_logs_group
  log_filter_name            = "TI_SERVICE_ERROR_LOG_FILTER"
  log_pattern                = "[timestamp, level=ERROR*]"
  alarm_name                 = "TI_SERVICE_ERROR_LOG_ALARM"
  sns_topic_arn              = aws_sns_topic.ti_log_alert.arn
  metric_transformation_name = "TI_SERVICE_ERROR_IN_LOG_METRIC"
}

module "ti_go_service_fatal_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.ti_go_service_logs_group
  log_filter_name            = "TI_SERVICE_FATAL_LOG_FILTER"
  log_pattern                = "[timestamp, level=FATAL*]"
  alarm_name                 = "TI_SERVICE_FATAL_LOG_ALARM"
  sns_topic_arn              = aws_sns_topic.ti_log_alert.arn
  metric_transformation_name = "TI_SERVICE_FATAL_IN_LOG_METRIC"
}

module "ti_go_service_panic_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.ti_go_service_logs_group
  log_filter_name            = "TI_SERVICE_PANIC_LOG_FILTER"
  log_pattern                = "[timestamp, level=panic*]"
  alarm_name                 = "TI_SERVICE_PANIC_LOG_ALARM"
  sns_topic_arn              = aws_sns_topic.ti_log_alert.arn
  metric_transformation_name = "TI_SERVICE_PANIC_IN_LOG_METRIC"
}

module "ml_inference_service_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.ml_inference_logs_group
  log_filter_name            = var.ml_inference_log_filter_name
  log_pattern                = "[date, time, status=%ERROR%]"
  alarm_name                 = var.ml_inference_log_alarm_name
  sns_topic_arn              = aws_sns_topic.ml_inference_log_alert.arn
  metric_transformation_name = var.ml_inference_metric_transformation_name
}

module "remediator_log_alert" {
  source                     = "../modules/log_alerting"
  region                     = var.region
  log_group_name             = var.remediator_logs_group
  log_filter_name            = var.remediator_log_filter_name
  log_pattern                = "[timestamp, level=ERROR*]"
  alarm_name                 = var.remediator_log_alarm_name
  sns_topic_arn              = aws_sns_topic.remediator_log_alert.arn
  metric_transformation_name = var.remediator_metric_transformation_name
}
