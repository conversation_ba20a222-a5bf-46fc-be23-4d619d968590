module "bff_service_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = var.bff_service_name
  max_capacity                  = var.bff_service_max_capacity
  min_capacity                  = var.bff_service_min_capacity
  memory_autoscale_target_value = var.bff_service_memory_autoscale_target_value
  cpu_autoscale_target_value    = var.bff_service_cpu_autoscale_target_value

  appautoscaling_target_tags = var.ecs_bff_autoscaling_tags
}

module "gateway_service_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = var.gateway_service_name
  max_capacity                  = var.gateway_service_max_capacity
  min_capacity                  = var.gateway_service_min_capacity
  memory_autoscale_target_value = var.gateway_service_memory_autoscale_target_value
  cpu_autoscale_target_value    = var.gateway_service_cpu_autoscale_target_value

  appautoscaling_target_tags = var.ecs_gateway_autoscaling_tags
}

module "remediator_service_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = var.remediator_service_name
  max_capacity                  = var.remediator_service_max_capacity
  min_capacity                  = var.remediator_service_min_capacity
  memory_autoscale_target_value = var.remediator_service_memory_autoscale_target_value
  cpu_autoscale_target_value    = var.remediator_service_cpu_autoscale_target_value

  appautoscaling_target_tags = var.ecs_remediator_autoscaling_tags
}

module "keycloak_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = var.keycloak_service_name
  max_capacity                  = var.keycloak_max_capacity
  min_capacity                  = var.keycloak_min_capacity
  memory_autoscale_target_value = var.keycloak_memory_autoscale_target_value
  cpu_autoscale_target_value    = var.keycloak_cpu_autoscale_target_value

  appautoscaling_target_tags = var.ecs_keycloak_autoscaling_tags
}

module "ml_inference_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = var.ml_inference_service_name
  max_capacity                  = var.ml_inference_max_capacity
  min_capacity                  = var.ml_inference_min_capacity
  memory_autoscale_target_value = var.ml_inference_memory_autoscale_target_value
  cpu_autoscale_target_value    = var.ml_inference_cpu_autoscale_target_value

  appautoscaling_target_tags = var.ecs_ml_inference_autoscaling_tags
}

module "ti_go_service_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = var.ti_go_service_service_name
  max_capacity                  = var.ti_go_service_max_capacity
  min_capacity                  = var.ti_go_service_min_capacity
  memory_autoscale_target_value = var.ti_go_service_memory_autoscale_target_value
  cpu_autoscale_target_value    = var.ti_go_service_cpu_autoscale_target_value

  appautoscaling_target_tags = var.ecs_ti_go_service_autoscaling_tags
}

module "inline_service_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = "inline"
  max_capacity                  = var.inline_max_autoscaling_capacity 
  min_capacity                  = var.inline_min_autoscaling_capacity
  memory_autoscale_target_value = var.inline_service_memory_autoscale_target_value
  cpu_autoscale_target_value    = var.inline_service_cpu_autoscale_target_value

  appautoscaling_target_tags = {
    Name        = "inline-service-autoscaling-target"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

## Request based autoscaling
// ... existing code ...

# Autoscaling target for the inline service
# resource "aws_appautoscaling_target" "inline_request_based_service_target" {
#   max_capacity       = 10
#   min_capacity       = 1
#   resource_id        = "service/${var.ecs_cluster_name}/${module.inline_service.service_name}"
#   scalable_dimension = "ecs:service:DesiredCount"
#   service_namespace  = "ecs"
# }

# # Autoscaling policy based on NLB request count
# resource "aws_appautoscaling_policy" "inline_request_based_service_policy" {
#   name               = "inline-service-request-based-autoscaling"
#   policy_type        = "TargetTrackingScaling"
#   resource_id        = aws_appautoscaling_target.inline_request_based_service_target.resource_id
#   scalable_dimension = aws_appautoscaling_target.inline_request_based_service_target.scalable_dimension
#   service_namespace  = aws_appautoscaling_target.inline_service_target.service_namespace

#   target_tracking_scaling_policy_configuration {
#     predefined_metric_specification {
#       predefined_metric_type = "ALBRequestCountPerTarget"
#       resource_label        = "${aws_lb.smtp.arn_suffix}/${aws_lb_target_group.gosmtp.arn_suffix}"
#     }
#     target_value = 1000  # Average requests per target
#     scale_in_cooldown  = 300  # 5 minutes
#     scale_out_cooldown = 60   # 1 minute
#   }
# }
