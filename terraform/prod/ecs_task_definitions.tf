module "bff_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "bff-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id

  container_definitions_json = jsonencode([
    {
      "cpu" : 0,
      "command" : [
        "bff"
      ],
      "secrets" : [
        {
          "name" : "GW_AUTH_CLIENT_ID",
          "valueFrom" : aws_secretsmanager_secret.GW_AUTH_CLIENT_ID.arn
          }, {
          "name" : "GW_AUTH_CLIENT_SECRET",
          "valueFrom" : aws_secretsmanager_secret.GW_AUTH_CLIENT_SECRET.arn
          }, {
          "name" : "GW_SERVICE_ACCOUNT",
          "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
          }, {
          "name" : "NATS_SERVER_URL",
          "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
          }, {
          "name" : "PG_PASSWORD",
          "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
          }, {
          "name" : "KEYCLOAK_ADMIN_CLIENT_ID",
          "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_ADMIN_CLIENT_ID.arn
          }, {
          "name" : "KEYCLOAK_ADMIN_CLIENT_SECRET",
          "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_ADMIN_CLIENT_SECRET.arn
          }, {
          "name" : "KEYCLOAK_SERVICE_ACCOUNT_ID",
          "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_SERVICE_ACCOUNT_ID.arn
          }, {
          "name" : "MS_AUTH_CLIENT_ID",
          "valueFrom" : aws_secretsmanager_secret.MS_AUTH_CLIENT_ID.arn
          }, {
          "name" : "MS_AUTH_CLIENT_SECRET",
          "valueFrom" : aws_secretsmanager_secret.MS_AUTH_CLIENT_SECRET.arn
          }, {
          "name" : "MS_AUTH_TENANT_ID",
          "valueFrom" : aws_secretsmanager_secret.MS_AUTH_TENANT_ID.arn
          }, {
          "name" : "JWT_SECRET",
          "valueFrom" : aws_secretsmanager_secret.JWT_SECRET.arn
      }],
      "environment" : [{
        "name" : "BFF_HOST",
        "value" : var.BFF_HOST
        }, {
        "name" : "BFF_PORT",
        "value" : var.BFF_PORT
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "PG_SSL",
        "value" : var.PG_SSL
        }, {
        "name" : "AWS_REGION",
        "value" : var.region
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "PG_MAX_OPEN_CONNECTION",
        "value" : var.PG_MAX_OPEN_CONNECTION
        }, {
        "name" : "PG_MAX_IDLE_CONNECTION",
        "value" : var.PG_MAX_IDLE_CONNECTION
        }, {
        "name" : "BFF_GOTENBERG_HOST",
        "value" : "http://localhost:3000"
        }, {
        "name" : "KEYCLOAK_BASE_URL",
        "value" : "https://auth.ravenmail.io"
        }, {
        "name" : "UI_BASE_URL",
        "value" : "https://dashboard.ravenmail.io"
        }, {
        "name" : "MS_GRAPH_BASE_URL",
        "value" : "https://graph.microsoft.com/v1.0"
        }, {
        "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
        "value" : "false"
        }, {
        "name" : "BFF_PUBLIC_HOST",
        "value" : "https://ravenclaw.ravenmail.io"
        }, {
        "name" : "BFF_PERM_CHECK_RETRY",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "20"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "60"
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.s3-orgs.arn
        }, {
        "name" : "BFF_USE_PRODUCT_TABLES",
        "value" : "true"
        }, {
        "name" : "MAIL_ALERT_SENDER_ADDRESS",
        "value" : "Raven AI alerts<<EMAIL>>"
        },
        {
          "name" : "USERPORTAL_BASE_URL",
          "value" : "https://portal.ravenmail.io"
        }
      ],
      "environmentFiles" : [],
      "essential" : true,
      "image" : var.bff_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : var.bff_logs_group,
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "secretOptions" : [],
      "mountPoints" : [],
      "name" : var.bff_docker_image_name,
      "portMappings" : [{
        "appProtocol" : "http",
        "containerPort" : 8080,
        "hostPort" : 8080,
        "name" : "http",
        "protocol" : "tcp"
      }],
      "systemControls" : [],
      "volumesFrom" : [],
      "ulimits" : []
      }, {
      "cpu" : 0,
      "secrets" : [{
        "name" : "NATS_SERVER_URL",
        "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
        }, {
        "name" : "PG_PASSWORD",
        "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
      }],
      "environment" : [{
        "name" : "BFF_HOST",
        "value" : var.BFF_HOST
        }, {
        "name" : "BFF_PORT",
        "value" : var.BFF_PORT
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "PG_SSL",
        "value" : var.PG_SSL
        }, {
        "name" : "AWS_REGION",
        "value" : var.region
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "PG_MAX_OPEN_CONNECTION",
        "value" : var.PG_MAX_OPEN_CONNECTION
        }, {
        "name" : "PG_MAX_IDLE_CONNECTION",
        "value" : var.PG_MAX_IDLE_CONNECTION
      }],
      "environmentFiles" : [],
      "essential" : true,
      "image" : var.gotenberg_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : var.gotenberg_logs_group,
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "secretOptions" : [],
      "mountPoints" : [
        {
          "sourceVolume" : "bff-logs",
          "containerPath" : "/root/ravenclaw",
          "readOnly" : false
        }
      ],
      "name" : var.gotenberg_docker_image_name,
      "portMappings" : [{
        "appProtocol" : "http",
        "containerPort" : 3000,
        "hostPort" : 3000,
        "name" : "http-gotenberg",
        "protocol" : "tcp"
      }],
      "systemControls" : [],
      "volumesFrom" : [],
      "ulimits" : []
    }
    #   {
    #     "name"      = "ecs-metric-exporter"
    #     "image"     = "771151923073.dkr.ecr.ap-south-1.amazonaws.com/ecs-exporter:1.0.0"
    #     "cpu"       = 0
    #     "essential" = false
    #     "portMappings" = [
    #       {
    #         "containerPort" = 9779
    #         "hostPort"      = 9779
    #         "protocol"      = "tcp"
    #       }
    #     ]
    #     "logConfiguration" = {
    #       "logDriver" = "awslogs"
    #       "options" = {
    #         "awslogs-create-group"  = "true",
    #         "awslogs-group"         = "ecs/bff-ecs-exporter"
    #         "awslogs-region"        = var.region
    #         "awslogs-stream-prefix" = "ecs"
    #       }
    #     }
    # }
  ])

  cpu                = var.bff_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "bff"
  memory             = var.bff_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.bff_task_def_tags
}

module "gateway_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "gateway-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "gateway",
      "start"
    ],
    "secrets" : [{
      "name" : "GW_SERVICE_ACCOUNT",
      "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
      }, {
      "name" : "GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC",
      "valueFrom" : aws_secretsmanager_secret.GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC.arn
      }, {
      "name" : "GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET",
      "valueFrom" : aws_secretsmanager_secret.GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET.arn
      }, {
      "name" : "NATS_SERVER_URL",
      "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      }, {
      "name" : "PG_PASSWORD",
      "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
    }],
    "environment" : [{
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_HOST",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_HOST
      }, {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_PATH",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_PATH
      }, {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_PORT",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_PORT
      }, {
      "name" : "GATEWAY_PUBLIC_HOST",
      "value" : var.GATEWAY_PUBLIC_HOST
      }, {
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "PG_SSL",
      "value" : var.PG_SSL
      }, {
      "name" : "AWS_REGION",
      "value" : var.region
      }, {
      "name" : "DEPLOYMENT_ENV",
      "value" : var.DEPLOYMENT_ENV
      }, {
      "name" : "GATEWAY_MICROSOFT_LIFECYCLE_PATH",
      "value" : var.GATEWAY_MICROSOFT_LIFECYCLE_PATH
      }, {
      "name" : "PG_MAX_OPEN_CONNECTION",
      "value" : var.PG_MAX_OPEN_CONNECTION
      }, {
      "name" : "PG_MAX_IDLE_CONNECTION",
      "value" : var.PG_MAX_IDLE_CONNECTION
      }, {
      "name" : "MS_GRAPH_BASE_URL",
      "value" : "https://graph.microsoft.com/v1.0"
      }, {
      "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
      "value" : "false"
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "30"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "100"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "20"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "3600"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
      }, {
      "name" : "SEED_ORGANIZATION_NAME",
      "value" : "ravenmail"
      }, {
      "name" : "GATEWAY_GOOGLE_WEBHOOK_PATH",
      "value" : "/v0/hooks/google"
      }, {
      "name" : "S3_KMS_KEY_ARN",
      "value" : aws_kms_key.s3-orgs.arn
      }, {
      "name" : "GATEWAY_MAIL_INGESTED_SCHEMA_VERSION",
      "value" : "new"
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.gateway_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.gateway_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [
      {
        "sourceVolume" : "gateway-logs",
        "containerPath" : "/root/ravenclaw",
        "readOnly" : false
      }
    ],
    "name" : var.gateway_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8081,
      "hostPort" : 8081,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
    }
  ])

  cpu                = var.gateway_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "gateway"
  memory             = var.gateway_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.gateway_task_def_tags
}

module "remediator_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "remediator-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id

  container_definitions_json = jsonencode([{
    "cpu" : var.remediator_cpu,
    "command" : [
      "remediator"
    ],
    "secrets" : [
      {
        "name" : "NATS_SERVER_URL",
        "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
        }, {
        "name" : "PG_PASSWORD",
        "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
        }, {
        "name" : "GW_SERVICE_ACCOUNT",
        "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
        }, {
        "name" : "INTENT_EXTRACTION_MODEL_API_KEY",
        "valueFrom" : aws_secretsmanager_secret.INTENT_EXTRACTION_MODEL_API_KEY.arn
        }, {
        "name" : "SPLUNK_ORGANIZATIONS",
        "valueFrom" : aws_secretsmanager_secret.SPLUNK_ORGANIZATIONS_KEY.arn
      }
    ],
    "environment" : [{
      "name" : "AI_AGENT_EMAIL",
      "value" : "<EMAIL>"
      }, {
      "name" : "LOG_ROTATION_FILE",
      "value" : "${var.remediator_LOG_ROTATION_FILE}"
      }, {
      "name" : "LOG_ROTATION_MAX_AGE",
      "value" : "${var.remediator_LOG_ROTATION_MAX_AGE}"
      }, {
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "PG_SSL",
      "value" : var.PG_SSL
      }, {
      "name" : "AWS_REGION",
      "value" : var.region
      }, {
      "name" : "DEPLOYMENT_ENV",
      "value" : var.DEPLOYMENT_ENV
      }, {
      "name" : "PG_MAX_OPEN_CONNECTION",
      "value" : var.PG_MAX_OPEN_CONNECTION
      }, {
      "name" : "PG_MAX_IDLE_CONNECTION",
      "value" : var.PG_MAX_IDLE_CONNECTION
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "30"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "100"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "20"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "60"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
      }, {
      "name" : "MAIL_ALERT_SENDER_ADDRESS",
      "value" : "Raven AI alerts<<EMAIL>>"
      }, {
      "name" : "UI_BASE_URL",
      "value" : "https://dashboard.ravenmail.io"
      }, {
      "name" : "S3_KMS_KEY_ARN",
      "value" : aws_kms_key.s3-orgs.arn
      }, {
      "name" : "MS_GRAPH_BASE_URL",
      "value" : "https://graph.microsoft.com/v1.0"
      }, {
      "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
      "value" : "false"
      }, {
      "name" : "BFF_PUBLIC_HOST",
      "value" : "https://ravenclaw.ravenmail.io"
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_API_VERSION",
      "value" : var.INTENT_EXTRACTION_MODEL_API_VERSION
      }, {
      "name" : "INTENT_EXTRACTION_RETRIES",
      "value" : var.INTENT_EXTRACTION_RETRIES
      }, {
      "name" : "INTENT_EXTRACTION_BACKOFF_IN_SEC",
      "value" : var.INTENT_EXTRACTION_BACKOFF_IN_SEC
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_ENDPOINT",
      "value" : var.INTENT_EXTRACTION_MODEL_ENDPOINT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_DEPLOYMENT",
      "value" : var.INTENT_EXTRACTION_MODEL_DEPLOYMENT
      }, {
      "name" : "SPLUNK_LOG_ROTATION_FILE",
      "value" : "splunk.log",
      }, {
      "name" : "SPLUNK_LOG_ROTATION_MAX_SIZE",
      "value" : "512"
      }, {
      "name" : "SPLUNK_LOG_ROTATION_MAX_BACKUPS",
      "value" : "90"
      }, {
      "name" : "SPLUNK_LOG_ROTATION_MAX_AGE",
      "value" : "120"
      }, {
      "name" : "USERPORTAL_BASE_URL",
      "value" : "https://portal.ravenmail.io"
      }
    ],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.remediator_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.remediator_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [{
      "sourceVolume" : "remediator-logs",
      "containerPath" : "/root/ravenclaw",
      "readOnly" : false
    }],
    "name" : var.remediator_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8081,
      "hostPort" : 8081,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.remediator_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "remediator"
  memory             = var.remediator_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.remediator_task_def_tags
}

module "setup_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "setup"
    ],
    "secrets" : [{
      "name" : "NATS_SERVER_URL",
      "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      }, {
      "name" : "PG_PASSWORD",
      "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
    }],
    "environment" : [{
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "PG_SSL",
      "value" : var.PG_SSL
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "10"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "20"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "60"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.setup_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.setup_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.setup_docker_image_name,
    "portMappings" : [],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.setup_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "setup"
  memory             = var.setup_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.setup_task_def_tags
}

module "ml_inference_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "poetry",
      "run",
      "fastapi",
      "run",
      "--workers",
      "2",
      "/app/server/fastapi-server.py"
    ],
    "secrets" : [{
      "name" : "INTENT_EXTRACTION_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.INTENT_EXTRACTION_MODEL_API_KEY.arn
      }, {
      "name" : "DETECTION_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.DETECTION_MODEL_API_KEY.arn
      }, {
      "name" : "ATTACHMENT_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_MODEL_API_KEY.arn
      }
    ],
    "environment" : [{
      "name" : "EXTRACTION_MODEL_PATH",
      "value" : var.EXTRACTION_MODEL_PATH
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_DEPLOYMENT",
      "value" : var.INTENT_EXTRACTION_MODEL_DEPLOYMENT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_ENDPOINT",
      "value" : var.INTENT_EXTRACTION_MODEL_ENDPOINT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_API_VERSION",
      "value" : var.INTENT_EXTRACTION_MODEL_API_VERSION
      }, {
      "name" : "SPACY_ENTITY_MODEL_PATH",
      "value" : var.SPACY_ENTITY_MODEL_PATH
      }, {
      "name" : "AZURE_LLM_API_VERSION",
      "value" : "2024-02-15-preview"
      }, {
      "name" : "DETECTION_MODEL_ENDPOINT",
      "value" : "https://detectio-model-ptu.openai.azure.com"
      }, {
      "name" : "DETECTION_MODEL_DEPLOYMENT",
      "value" : "detectionmodel"
      }, {
      "name" : "DETECTION_MODEL_DEPLOYMENT_WITH_TOKEN_DEPLOYMENT",
      "value" : "detection-model-token-deployment"
      }, {
      "name" : "LLM_API_CALL_RETRY_ATTEMPT_COUNT",
      "value" : "5"
      }, {
      "name" : "ATTACHMENT_MODEL_ENDPOINT",
      "value" : var.ATTACHMENT_MODEL_ENDPOINT
      }, {
      "name" : "ATTACHMENT_MODEL_DEPLOYMENT",
      "value" : var.ATTACHMENT_MODEL_DEPLOYMENT
      }
    ],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.ml_inference_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.ml_inference_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.ml_inference_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8000,
      "hostPort" : 8000,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.ml_inference_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "ml-inference"
  memory             = var.ml_inference_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.ml_inference_task_def_tags
}

module "ti_go_service_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "ti-go-service-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : ["ti"],
    "secrets" : [{
      "name" : "NATS_SERVER_URL",
      "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      }, {
      "name" : "PG_PASSWORD",
      "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
      }, {
      "name" : "VIRUSTOTAL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.VIRUSTOTAL_API_KEY.arn
      }, {
      "name" : "ATTACHMENT_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_MODEL_API_KEY.arn
      }, {
      "name" : "DLP_MODEL_CREDENTIALS",
      "valueFrom" : aws_secretsmanager_secret.DLP_MODEL_CREDENTIALS.arn
      }, {
      "name" : "CLASSIFICATION_OAI_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.CLASSIFICATION_OAI_API_KEY.arn
      }, {
      "name" : "ATTACHMENT_SCAN_ENABLED_ORGS",
      "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_SCAN_ENABLED_ORGS.arn
      }, {
      "name" : "INTENT_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.INTENT_EXTRACTION_MODEL_API_KEY.arn
    }, ],
    "environment" : [{
      "name" : "ASYNC_MAX_AT_ONCE_VT"
      "value" : var.ASYNC_MAX_AT_ONCE_VT
      }, {
      "name" : "ASYNC_MAX_PER_SECOND_VT"
      "value" : var.ASYNC_MAX_PER_SECOND_VT
      }, {
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "ML_INFERENCE_SERVICE_URL",
      "value" : "http://internal-alb-ml-inference-internal-477817753.ap-south-1.elb.amazonaws.com"
      # "value" : "http://${var.ml_inference_service_discovery_name}.${var.service_discovery_private_dns_namespace}:8000"
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "30"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "100"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "30"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "300"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
      }, {
      "name" : "TI_URL_SCAN_LIMIT",
      "value" : "6"
      }, {
      "name" : "ENABLE_INGESTION_DETECT_VENDOR",
      "value" : "true"
      }, {
      "name" : "SENDER_ANALYSIS_RECURRING_INVOICE_THRESHOLD",
      "value" : "2"
      }, {
      "name" : "SENDER_ANALYSIS_IP_ANOMALY_THRESHOLD",
      "value" : "2"
      }, {
      "name" : "S3_KMS_KEY_ARN",
      "value" : aws_kms_key.s3-orgs.arn
      }, {
      "name" : "ATTACHMENT_SCAN",
      "value" : "false"
      }, {
      "name" : "THREAT_RULES",
      "value" : "true"
      }, {
      "name" : "NEW_VENDOR_SCAN",
      "value" : "false"
      }, {
      "name" : "ATTACHMENT_MODEL_DEPLOYMENT",
      "value" : var.ATTACHMENT_MODEL_DEPLOYMENT
      }, {
      "name" : "ATTACHMENT_MODEL_ENDPOINT",
      "value" : var.ATTACHMENT_MODEL_ENDPOINT
      }, {
      "name" : "AZURE_LLM_API_VERSION",
      "value" : "2024-02-15-preview"
      }, {
      "name" : "DLP_CHECK_RETRIES",
      "value" : "5"
      }, {
      "name" : "DLP_CHECK_BACKOFF",
      "value" : "5"
      }, {
      "name" : "DEPLOYMENT_ENV",
      "value" : var.DEPLOYMENT_ENV
      }, {
      "name" : "CLASSIFICATION_OAI_DEPLOYMENT_ID",
      "value" : "gpt-4o"
      }, {
      "name" : "CLASSIFICATION_OAI_ENDPOINT",
      "value" : "https://email-type-classification.openai.azure.com"
      }, {
      "name" : "CLASSIFICATION_OAI_API_VERSION",
      "value" : "2025-01-01-preview"
      }, {
      "name" : "PG_MAX_OPEN_CONNECTION",
      "value" : "35"
      }, {
      "name" : "DLP_DETECTION_VALIDATORS",
      "value" : var.DLP_DETECTION_VALIDATORS
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_ENDPOINT",
      "value" : var.INTENT_EXTRACTION_MODEL_ENDPOINT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_DEPLOYMENT",
      "value" : var.INTENT_EXTRACTION_MODEL_DEPLOYMENT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_API_VERSION",
      "value" : var.INTENT_EXTRACTION_MODEL_API_VERSION
      }, {
      "name" : "INTENT_EXTRACTION_RETRIES",
      "value" : var.INTENT_EXTRACTION_RETRIES
      }, {
      "name" : "INTENT_EXTRACTION_BACKOFF_IN_SEC",
      "value" : var.INTENT_EXTRACTION_BACKOFF_IN_SEC
      },
    ],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.ti_go_service_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.ti_go_service_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [
      {
        "sourceVolume" : "ti-go-service-logs",
        "containerPath" : "/root/ravenclaw",
        "readOnly" : false
      }
    ],
    "name" : var.ti_go_service_docker_image_name,
    "portMappings" : [],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.ti_go_service_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "ti-go-service"
  memory             = var.ti_go_service_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.ti_go_service_task_def_tags
}

## ml inference just for ingestion task
module "ml_inference_ingestion_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "poetry",
      "run",
      "fastapi",
      "run",
      "--workers",
      "6",
      "/app/server/fastapi-server.py"
    ],
    "secrets" : [{
      "name" : "INTENT_EXTRACTION_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.INTENT_EXTRACTION_MODEL_API_KEY.arn
      }, {
      "name" : "DETECTION_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.DETECTION_MODEL_API_KEY.arn
      }, {
      "name" : "ATTACHMENT_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_MODEL_API_KEY.arn
      }
    ],
    "environment" : [{
      "name" : "EXTRACTION_MODEL_PATH",
      "value" : var.EXTRACTION_MODEL_PATH
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_DEPLOYMENT",
      "value" : var.INTENT_EXTRACTION_MODEL_DEPLOYMENT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_ENDPOINT",
      "value" : var.INTENT_EXTRACTION_MODEL_ENDPOINT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_API_VERSION",
      "value" : var.INTENT_EXTRACTION_MODEL_API_VERSION
      }, {
      "name" : "SPACY_ENTITY_MODEL_PATH",
      "value" : var.SPACY_ENTITY_MODEL_PATH
      }, {
      "name" : "AZURE_LLM_API_VERSION",
      "value" : "2024-02-15-preview"
      }, {
      "name" : "DETECTION_MODEL_ENDPOINT",
      "value" : "https://detectio-model-ptu.openai.azure.com"
      }, {
      "name" : "DETECTION_MODEL_DEPLOYMENT",
      "value" : "detectionmodel"
      }, {
      "name" : "DETECTION_MODEL_DEPLOYMENT_WITH_TOKEN_DEPLOYMENT",
      "value" : "detection-model-token-deployment"
      }, {
      "name" : "LLM_API_CALL_RETRY_ATTEMPT_COUNT",
      "value" : "5"
      }, {
      "name" : "ATTACHMENT_MODEL_ENDPOINT",
      "value" : var.ATTACHMENT_MODEL_ENDPOINT
      }, {
      "name" : "ATTACHMENT_MODEL_DEPLOYMENT",
      "value" : var.ATTACHMENT_MODEL_DEPLOYMENT
      }
    ],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.ml_inference_ingestion_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.ml_inference_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.ml_inference_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8000,
      "hostPort" : 8000,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = 8192
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "ml-inference-ingestion"
  memory             = 16384
  task_role_arn      = aws_iam_role.task_role.arn

  tags = {
    Name        = "ml-inference-ingestion-svc"
    Environment = "prod"
    Team        = "infra"
    Application = "ml_python"
  }
}

module "ingestion_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "historical-ingestion"
    ],
    "secrets" : [{
      "name" : "GW_SERVICE_ACCOUNT",
      "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
      }, {
      "name" : "PG_PASSWORD",
      "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
      }, {
      "name" : "ATTACHMENT_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_MODEL_API_KEY.arn
      }, {
      "name" : "NATS_SERVER_URL",
      "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
    }],
    "environment" : [
      {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "PG_SSL",
        "value" : var.PG_SSL
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "MS_GRAPH_BASE_URL",
        "value" : "https://graph.microsoft.com/v1.0"
        }, {
        "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
        "value" : "false"
        }, {
        "name" : "ML_INFERENCE_SERVICE_URL",
        "value" : "http://internal-prod-alb-717426825.ap-south-1.elb.amazonaws.com"
        }, {
        "name" : "TI_URL_SCAN_LIMIT",
        "value" : "6"
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.s3-orgs.arn
        }, {
        "name" : "ENABLE_INGESTION_DETECT_VENDOR",
        "value" : "true"
        }, {
        "name" : "SENDER_ANALYSIS_IP_ANOMALY_THRESHOLD",
        "value" : "2"
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "10"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "60"
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "ATTACHMENT_MODEL_DEPLOYMENT",
        "value" : "gpt-4o-mini"
        }, {
        "name" : "ATTACHMENT_MODEL_ENDPOINT",
        "value" : "https://attachment-analysis.openai.azure.com/"
        }, {
        "name" : "AZURE_LLM_API_VERSION",
        "value" : "2024-02-15-preview"
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.ingestion_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.ingestion_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      },
      "secretOptions" : []
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.ingestion_docker_image_name,
    "portMappings" : [],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.ingestion_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "ingestion"
  memory             = var.ingestion_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.ingestion_task_def_tags
}

module "nats_command_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : ["nats"],
    "secrets" : [
      {
        "name" : "NATS_SERVER_URL",
        "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      },
    ],
    "environment" : [{
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "30"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "100"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "20"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "300"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.nats_command_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.nats_command_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.nats_command_docker_image_name,
    "portMappings" : [],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.nats_command_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "nats-command"
  memory             = var.nats_command_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = {
    Name        = "nats-command-task-def"
    Environment = "prod"
    Team        = "infra"
    Product     = "nats"
  }
}

module "gateway_subcommand_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "gateway"
    ],
    "secrets" : [
      {
        "name" : "GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET",
        "valueFrom" : aws_secretsmanager_secret.GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET.arn
        }, {
        "name" : "GW_SERVICE_ACCOUNT",
        "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
        }, {
        "name" : "NATS_SERVER_URL",
        "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
        }, {
        "name" : "PG_PASSWORD",
        "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
      },
    ],
    "environment" : [{
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_HOST",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_HOST
      }, {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_PATH",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_PATH
      }, {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_PORT",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_PORT
      }, {
      "name" : "GATEWAY_PUBLIC_HOST",
      "value" : var.GATEWAY_PUBLIC_HOST
      }, {
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "PG_SSL",
      "value" : var.PG_SSL
      }, {
      "name" : "AWS_REGION",
      "value" : var.region
      }, {
      "name" : "DEPLOYMENT_ENV",
      "value" : var.DEPLOYMENT_ENV
      }, {
      "name" : "GATEWAY_MICROSOFT_LIFECYCLE_PATH",
      "value" : var.GATEWAY_MICROSOFT_LIFECYCLE_PATH
      }, {
      "name" : "PG_MAX_OPEN_CONNECTION",
      "value" : var.PG_MAX_OPEN_CONNECTION
      }, {
      "name" : "PG_MAX_IDLE_CONNECTION",
      "value" : var.PG_MAX_IDLE_CONNECTION
      }, {
      "name" : "MS_GRAPH_BASE_URL",
      "value" : "https://graph.microsoft.com/v1.0"
      }, {
      "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
      "value" : "false"
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "10"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "20"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "60"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
      }, {
      "name" : "SEED_ORGANIZATION_NAME",
      "value" : "ravenmail"
      }, {
      "name" : "GATEWAY_GOOGLE_WEBHOOK_PATH",
      "value" : "/v0/hooks/google"
      }, {
      "name" : "GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC",
      "value" : "projects/ravenclaw-442104/topics/gmail-integration"
      }, {
      "name" : "S3_KMS_KEY_ARN",
      "value" : aws_kms_key.s3-orgs.arn
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.gateway_subcommand_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.gateway_subcommand_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.gateway_subcommand_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8081,
      "hostPort" : 8081,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.gateway_subcommand_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "gateway-subcommand"
  memory             = var.gateway_subcommand_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = {
    Name        = "gateway-subcommand-job"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

module "inline_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "inline-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id
  container_definitions_json = jsonencode([
    {
      "command" : [
        "gateway",
        "inline"
      ],
      "cpu" : 0,
      "environment" : [{
        "name" : "AWS_REGION",
        "value" : var.region
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "600"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_MAX_IDLE_CONNECTION",
        "value" : "5"
        }, {
        "name" : "PG_MAX_OPEN_CONNECTION",
        "value" : "20"
        }, {
        "name" : "PG_PASSWORD",
        "value" : module.rds.db_password
        }, {
        "name" : "PG_PORT",
        "value" : "5432"
        }, {
        "name" : "PG_SSL",
        "value" : "disable"
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.s3-orgs.arn
        }, {
        "name" : "SMARTHOST_DOMAIN",
        "value" : "secure.ravenmail.io"
        }, {
        "name" : "SMARTHOST_PORT",
        "value" : "2525"
        }, {
        "name" : "DKIM_ENABLED",
        "value" : "true"
        }, {
        "name" : "DKIM_SELECTOR",
        "value" : "ravenmail"
      }],

      "essential" : true,
      "image" : var.inline_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : var.inline_log_group,
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "mountPoints" : [
        {
          "sourceVolume" : "inline-logs",
          "containerPath" : "/root/ravenclaw",
          "readOnly" : false
        }
      ],
      "name" : "gateway-inline-prod",
      "portMappings" : [
        {
          "appProtocol" : "http",
          "containerPort" : 2525,
          "hostPort" : 2525,
          "name" : "http",
          "protocol" : "tcp"
        }
      ],
      "secrets" : [
        {
          "name" : "NATS_SERVER_URL",
          "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
        },
        {
          "name" : "SMARTHOST_TLS_CERT",
          "valueFrom" : aws_secretsmanager_secret.SMARTHOST_TLS_CERT.arn
        },
        {
          "name" : "SMARTHOST_TLS_KEY",
          "valueFrom" : aws_secretsmanager_secret.SMARTHOST_TLS_KEY.arn
        },
        {
          "name" : "DKIM_PRIVATE_KEY",
          "valueFrom" : aws_secretsmanager_secret.DKIM_PRIVATE_KEY.arn
        },

      ],
      "systemControls" : [],
      "volumesFrom" : []
    }
  ])

  cpu                      = var.inline_cpu
  execution_role_arn       = aws_iam_role.task_execution_role.arn
  family                   = "gateway-inline-prod"
  memory                   = var.inline_memory
  task_role_arn            = aws_iam_role.task_role.arn
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  track_latest             = "false"

  tags = {
    Environment = "prod"
    Name        = "ravenclaw-gateway-inline-task-definition"
    Product     = "inline"
    Team        = "infra"
  }
}
