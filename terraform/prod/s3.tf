resource "aws_s3_bucket" "webui_build" {
  bucket = "webui-build-prod"

  tags = var.webui_build_s3_bucket_tags
}

resource "aws_s3_bucket_versioning" "webui_build" {
  bucket = aws_s3_bucket.webui_build.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "webui_build" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.webui_build]

  bucket = aws_s3_bucket.webui_build.id

  rule {
    id = "lifecycle_configuration"

    noncurrent_version_expiration {
      noncurrent_days = 90
    }

    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "webui_build" {
  bucket = aws_s3_bucket.webui_build.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "webui_build" {
  depends_on = [aws_s3_bucket_ownership_controls.webui_build]

  bucket = aws_s3_bucket.webui_build.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "webui_build" {
  bucket = aws_s3_bucket.webui_build.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_website_configuration" "webui_build" {
  bucket = aws_s3_bucket.webui_build.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "index.html"
  }
}

# data "aws_iam_policy_document" "webui_build" {
#   statement {
#     actions   = ["s3:GetObject"]
#     resources = ["${aws_s3_bucket.webui_build.arn}/*"]

#     principals {
#       type        = "AWS"
#       identifiers = [aws_cloudfront_origin_access_identity.webui_build.iam_arn]
#     }
#   }
# }

resource "aws_s3_bucket_policy" "webui_build_oai" {
  bucket = aws_s3_bucket.webui_build.id

  policy = jsonencode({
    "Version" : "2008-10-17",
    "Id" : "PolicyForCloudFrontPrivateContent",
    "Statement" : [
      {
        "Sid" : "AllowCloudFrontServicePrincipal",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudfront.amazonaws.com"
        },
        "Action" : "s3:GetObject",
        "Resource" : "arn:aws:s3:::webui-build-prod/*",
        "Condition" : {
          "StringEquals" : {
            "AWS:SourceArn" : "arn:aws:cloudfront::771151923073:distribution/E21Q53KLD662VG"
          }
        }
      },
    ]
  })
}

# resource "aws_s3_bucket_policy" "webui_build" {
#   bucket = aws_s3_bucket.webui_build.id
#   policy = data.aws_iam_policy_document.webui_build.json
# }

resource "aws_s3_bucket_server_side_encryption_configuration" "webui_build" {
  bucket = aws_s3_bucket.webui_build.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = "arn:aws:kms:ap-south-1:771151923073:alias/prod-webui-kms"
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}
