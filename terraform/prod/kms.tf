resource "aws_kms_key" "annotations" {
  description             = "KMS key for annotations s3 bucket"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "annotations-s3-kms-key"
    Environment = "prod"
    Team        = "infra"
    Product     = "ML"
  }
}

resource "aws_kms_alias" "annotations" {
  name          = "alias/annotations-bucket-key"
  target_key_id = aws_kms_key.annotations.key_id
}

resource "aws_kms_key" "prod-webui-kms" {
  description             = "KMS key for all webui bucket"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  policy = jsonencode({
    Version = "2012-10-17"
    Id      = "prod-kms-key-webui-access"
    Statement = [
      {
        Sid    = "Enable KMS key access to root user"
        Effect = "Allow"
        Principal = {
          "AWS" : "arn:aws:iam::771151923073:root"
        },
        Action = [
          "kms:*"
        ]
        Resource = "*",
      },
      {
        Sid    = "Enable KMS key access to cloudfront"
        Effect = "Allow"
        Principal = {
          "Service" : "cloudfront.amazonaws.com"
        },
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ]
        Resource = "*",
      },
      {
        "Sid" : "Allow CloudTrail to use the key",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudtrail.amazonaws.com"
        },
        "Action" : [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        "Resource" : "*",
      },
    ]
  })
  tags = {
    Name        = "webui-s3"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "prod-webui-kms" {
  name          = "alias/prod-webui-kms"
  target_key_id = aws_kms_key.prod-webui-kms.key_id
}

resource "aws_kms_key" "secretsmanager" {
  description             = "KMS key for prod secretsmanager"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "secretsmanager-kms"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "secretsmanager" {
  name          = "alias/secretsmanager-key"
  target_key_id = aws_kms_key.secretsmanager.key_id
}

resource "aws_kms_key" "s3-orgs" {
  description             = "KMS key for prod onboarded orgs"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "onboarded-orgs-kms-s3"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "s3-orgs" {
  name          = "alias/s3-orgs-bucket-key"
  target_key_id = aws_kms_key.s3-orgs.key_id
}

resource "aws_kms_key" "codescan" {
  description             = "KMS key for code scanning output files"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "codescan-kms-s3"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "codescan" {
  name          = "alias/codescan-bucket-key"
  target_key_id = aws_kms_key.codescan.key_id
}

resource "aws_kms_key" "logs_s3" {
  description             = "KMS key for logs S3 bucket"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  policy = jsonencode({
    Version = "2012-10-17"
    Id      = "prod-kms-key-logs-s3-access"
    Statement = [
      {
        Sid    = "Enable KMS key access to root user"
        Effect = "Allow"
        Principal = {
          "AWS" : "arn:aws:iam::771151923073:root"
        },
        Action = [
          "kms:*"
        ]
        Resource = "*",
      },
      {
        Sid    = "Enable logs s3 KMS key access to cloudfront"
        Effect = "Allow"
        Principal = {
          "Service" : "cloudfront.amazonaws.com"
        },
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ]
        Resource = "*",
      },
      {
        "Sid" : "Allow CloudTrail to use the key",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudtrail.amazonaws.com"
        },
        "Action" : [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        "Resource" : "*",
      },
      {
        "Sid" : "Allow ELB to use the key",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "elasticloadbalancing.amazonaws.com"
        },
        "Action" : [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        "Resource" : "*",
      },
      {
        Sid    = "Enable KMS key access to cloudwatch logs"
        Effect = "Allow"
        Principal = {
          "Service" : "logs.ap-south-1.amazonaws.com"
        },
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ]
        Resource = "*",
      },
      {
        Sid    = "Enable KMS key access to cloudwatch"
        Effect = "Allow"
        Principal = {
          "Service" : "cloudwatch.amazonaws.com"
        },
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ]
        Resource = "*",
      },

    ]
  })
  tags = {
    Name        = "logs-kms-s3"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "logs_s3" {
  name          = "alias/logs-bucket-key"
  target_key_id = aws_kms_key.logs_s3.key_id
}

resource "aws_kms_key" "assets" {
  description             = "KMS key for assets bucket"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  policy = jsonencode({
    Version = "2012-10-17"
    Id      = "prod-kms-key-assets-bucket-access"
    Statement = [
      {
        Sid    = "Enable KMS key access to root user"
        Effect = "Allow"
        Principal = {
          "AWS" : "arn:aws:iam::771151923073:root"
        },
        Action = [
          "kms:*"
        ]
        Resource = "*",
      },
      {
        Sid    = "Enable KMS key access to cloudfront"
        Effect = "Allow"
        Principal = {
          "Service" : "cloudfront.amazonaws.com"
        },
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ]
        Resource = "*",
      },
      {
        "Sid" : "Allow CloudTrail to use the key",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudtrail.amazonaws.com"
        },
        "Action" : [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        "Resource" : "*",
      }

    ]
  })
  tags = {
    Name        = "assets-s3"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "assets" {
  name          = "alias/assets-kms-s3"
  target_key_id = aws_kms_key.assets.key_id
}

resource "aws_kms_key" "config" {
  description             = "KMS key for config bucket"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  policy = jsonencode({
    Version = "2012-10-17"
    Id      = "prod-kms-key-config-bucket-access"
    Statement = [
      {
        Sid    = "Enable KMS key access to root user"
        Effect = "Allow"
        Principal = {
          "AWS" : "arn:aws:iam::771151923073:root"
        },
        Action = [
          "kms:*"
        ]
        Resource = "*",
      },
      {
        "Sid" : "Allow CloudTrail to use the key",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudtrail.amazonaws.com"
        },
        "Action" : [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        "Resource" : "*",
      },
    ]
  })
  tags = {
    Name        = "config-s3"
    Environment = "prod"
    Team        = "infra"
    Product     = "compliance"
  }
}

resource "aws_kms_alias" "config" {
  name          = "alias/config-kms-s3"
  target_key_id = aws_kms_key.config.key_id
}

resource "aws_kms_key" "ec2-ebs-volume" {
  description             = "KMS key for EC2 EBS Volumes"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "ec2-ebs-volume-key"
    Environment = "prod"
    Team        = "infra"
    Product     = "EC2"
  }
}

resource "aws_kms_alias" "ec2-ebs-volume" {
  name          = "alias/ec2-ebs-volume-key"
  target_key_id = aws_kms_key.ec2-ebs-volume.key_id
}

resource "aws_kms_key" "nats_s3" {
  description             = "KMS key for nats s3 bucket"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "nats-s3-kms-key"
    Environment = "prod"
    Team        = "infra"
    Product     = "nats"
  }
}

resource "aws_kms_alias" "nats_s3" {
  name          = "alias/nats-s3-bucket-key"
  target_key_id = aws_kms_key.nats_s3.key_id
}


