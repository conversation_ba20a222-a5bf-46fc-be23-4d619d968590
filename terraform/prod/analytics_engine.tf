resource "aws_security_group" "analytics_engine_service" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  name = "analytics-engine-service-sg"
  tags = {
    Name        = "analytics-engine-service-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
  vpc_id = module.vpc_main.vpc_id
}



module "analytics_engine_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([
    {
      "cpu" : 0,
      "command" : [
        "analytics",
        "-r"
      ],
      "secrets" : [{
        "name" : "NATS_SERVER_URL",
        "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
        }, {
        "name" : "VIRUSTOTAL_API_KEY",
        "valueFrom" : aws_secretsmanager_secret.VIRUSTOTAL_API_KEY.arn
        }, {
        "name" : "ATTACHMENT_MODEL_API_KEY",
        "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_MODEL_API_KEY.arn
      }],
      "environment" : [{
        "name" : "ASYNC_MAX_AT_ONCE_VT"
        "value" : var.ASYNC_MAX_AT_ONCE_VT
        }, {
        "name" : "ASYNC_MAX_PER_SECOND_VT"
        "value" : var.ASYNC_MAX_PER_SECOND_VT
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_PASSWORD",
        "value" : module.rds.db_password
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "ML_INFERENCE_SERVICE_URL",
        "value" : "http://internal-prod-alb-717426825.ap-south-1.elb.amazonaws.com"
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "10"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "240"
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "TI_URL_SCAN_LIMIT",
        "value" : "5"
        },
        {
          "name" : "SENDER_ANALYSIS_RECURRING_INVOICE_THRESHOLD",
          "value" : "2"
        },
        {
          "name" : "ENABLE_INGESTION_DETECT_VENDOR",
          "value" : "false"
          }, {
          "name" : "S3_KMS_KEY_ARN",
          "value" : aws_kms_key.s3-orgs.arn
          }, {
          "name" : "ATTACHMENT_SCAN",
          "value" : "true"
          }, {
          "name" : "ATTACHMENT_MODEL_ENDPOINT",
          "value" : "https://attachment-analysis.openai.azure.com/"
          }, {
          "name" : "AZURE_LLM_API_VERSION",
          "value" : "2024-02-15-preview"
          }, {
          "name" : "ATTACHMENT_MODEL_DEPLOYMENT",
          "value" : "gpt-4o-mini"
          }, {
          "name" : "NEW_VENDOR_SCAN",
          "value" : "false"
          }, {
          "name" : "THREAT_RULES",
          "value" : "true"
          }, {
          "name" : "DEPLOYMENT_ENV",
          "value" : "dev"
          }, {
          "name" : "INVOICE_EXTRACTION_RETRIES",
          "value" : "1"
          }, {
          "name" : "INVOICE_EXTRACTION_BACKOFF",
          "value" : "5"
      }],

      "environmentFiles" : [],
      "essential" : true,
      "image" : var.analytics_engine_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : "/ecs/analytics-engine",
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "secretOptions" : [],
      "mountPoints" : [],
      "name" : "analytics-engine",
      "portMappings" : [{
        "appProtocol" : "http",
        "containerPort" : 80,
        "hostPort" : 80,
        "name" : "http",
        "protocol" : "tcp"
      }],
      "systemControls" : [],
      "volumesFrom" : [],
      "ulimits" : []
    },
  ])

  cpu                = var.analytics_engine_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "analytics-engine"
  memory             = var.analytics_engine_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = {
    Name        = "analytics-engine-task-definition"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# module "analytics_engine_service" {
#   source                         = "../modules/ecs_service"
#   region                         = var.region
#   namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
#   service_discovery_service_name = "analytics-engine"
#   cluster                        = module.ecs_cluster.cluster_name
#   security_groups_id             = [aws_security_group.analytics_engine_service.id]
#   subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   name                           = "analytics-engine"
#   desired_count                  = 1
#   task_definition_arn            = module.analytics_engine_task_definition.arn

#   service_discovery_svc_tags = {
#     Name        = "analytics-engine-service"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "engineering"
#     Application = "go"
#   }

#   ecs_service_tags = {
#     Name        = "analytics-engine-service"
#     Environment = "dev"
#     Product     = "ravenclaw"
#     Team        = "engineering"
#     Application = "go"
#   }
# }

module "analytics_engine_service_autoscaling" {
  source                        = "../modules/autoscaling"
  region                        = var.region
  ecs_cluster_name              = var.ecs_cluster_name
  ecs_service_name              = "analytics-engine"
  max_capacity                  = 4
  min_capacity                  = 2
  memory_autoscale_target_value = 80
  cpu_autoscale_target_value    = 80

  appautoscaling_target_tags = {
    Name        = "analytics-engine-service-autoscaling-target"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_cloudwatch_metric_alarm" "analytics_engine_ecs_cpu_utilization" {
  alarm_name          = "Analytics_Engine_ECS_CPU_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Analytics Engine Service - Alarm when ECS CPUUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = "analytics-engine"
  }

  tags = {
    Name        = "Analytics_Engine_ECS_CPU_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "analytics_engine_ecs_memory_utilization" {
  alarm_name          = "Analytics_Engine_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Analytics Engine Service - Alarm when ECS MemoryUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = "analytics-engine"
  }

  tags = {
    Name        = "Analytics_Engine_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}
