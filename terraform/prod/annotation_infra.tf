##### EC2 Test Instance #####
# module "annotations" {
#   source                                  = "../modules/ec2_instance"
#   region                                  = var.region
#   availability_zone                       = "ap-south-1c"
#   instance_type                           = "t3a.xlarge"
#   root_block_device_volume_size           = 100
#   root_block_device_iops                  = 3000
#   root_block_device_throughput            = 125
#   root_block_device_volume_type           = "gp3"
#   subnet_id                               = module.main_public_subnet_3.subnet_id
#   vpc_security_group_ids                  = ["${aws_security_group.annotations_sg.id}"]
#   key_name                                = "annotations-ec2"
#   iam_instance_profile                    = aws_iam_instance_profile.annotations_ec2_instance_profile.name
#   associate_public_ip_address             = true
#   root_block_device_encrypted             = true
#   root_block_device_delete_on_termination = false

#   # service_discovery_service_name = "annotations"
#   # namespace_id                   = aws_service_discovery_private_dns_namespace.dev.id

#   tags = {
#     Name        = "annotations-instance"
#     Team        = "infra"
#     Product     = "ML"
#     Environment = "prod"
#   }
# }

##### IAM Role for EC2 ######
# Define the IAM Role
resource "aws_iam_role" "annotation_ec2_role" {
  name = "AnnotationEC2Role"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "ec2.amazonaws.com"
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })
}

# Define the IAM Policy for the EC2 Role
resource "aws_iam_policy" "annotation_ec2_policy" {
  name = "AnnotationEC2Policy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      # Read permission for a specific RDS instance
      {
        "Effect" : "Allow",
        "Action" : [
          "rds:DescribeDBInstances",
          "rds:DescribeDBSnapshots",
          "rds:ListTagsForResource"
        ],
        "Resource" : module.rds.read_replica_arns,
        "Condition" : {
          "StringEquals" : {
            "aws:RequestedRegion" : "ap-south-1"
          }
        }
      },

      # Read permissions to all S3 buckets
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:List*",
          "s3:Get*"
        ],
        "Resource" : [
          "arn:aws:s3:::*"
        ],
        "Condition" : {
          "StringEquals" : {
            "aws:RequestedRegion" : "ap-south-1"
          }
        }
      },

      # Write permission to a specific S3 bucket
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:PutObject",
          "s3:DeleteObject"
        ],
        "Resource" : [
          "${aws_s3_bucket.annotations.arn}/*"
        ],
        "Condition" : {
          "StringEquals" : {
            "aws:RequestedRegion" : "ap-south-1"
          }
        }
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ],
        "Resource" : "${aws_kms_key.annotations.arn}"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey",
          "secretsmanager:Batch*",
          "secretsmanager:Describe*",
          "secretsmanager:Get*",
          "secretsmanager:List*",
          "secretsmanager:Validate*",
          "ssm:*",
          "s3:Describe*",
          "s3:Get*",
          "s3:Put*",
          "s3:Restore*",
          "s3:Tag*",
          "s3:List*",
          "s3:Untag*",
          "s3:Update*",
          "s3:Create*",
          "ecr:Get*",
          "ecr:Describe*",
          "ecr:Batch*",
          "ecr:List*",
          "ecr:Validate*",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "ses:Create*",
          "ses:Send*",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ],
        Resource = "*"
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "annotations_ec2_role_policy_attachment" {
  role       = aws_iam_role.annotation_ec2_role.name
  policy_arn = aws_iam_policy.annotation_ec2_policy.arn
}

# Define the IAM Instance Profile
resource "aws_iam_instance_profile" "annotations_ec2_instance_profile" {
  name = "AnnotationsEC2InstanceProfile"
  role = aws_iam_role.annotation_ec2_role.name
}

###### S3 Bucket ############
resource "aws_s3_bucket" "annotations" {
  bucket        = "annotations-ravenmail"
  force_destroy = false

  tags = {
    Name        = "annotations-s3-bucket"
    Environment = "test"
    Team        = "infra"
    Product     = "ML"
  }
}

###### S3 Bucket Versioning ############
resource "aws_s3_bucket_versioning" "annotations" {
  bucket = aws_s3_bucket.annotations.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "annotations" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.annotations]

  bucket = aws_s3_bucket.annotations.id

  rule {
    id = "lifecycle_configuration"

    noncurrent_version_expiration {
      noncurrent_days = 90
    }

    status = "Enabled"
  }
}

###### S3 Bucket ACLs ############
resource "aws_s3_bucket_ownership_controls" "annotations" {
  bucket = aws_s3_bucket.annotations.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "annotations" {
  depends_on = [aws_s3_bucket_ownership_controls.annotations]

  bucket = aws_s3_bucket.annotations.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "annotations" {
  bucket = aws_s3_bucket.annotations.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

###### S3 Bucket Encryption ############
resource "aws_s3_bucket_server_side_encryption_configuration" "annotations" {
  bucket = aws_s3_bucket.annotations.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.annotations.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}


### CRON 1 task definition
# module "cron_annotation_pipeline_task_definition" {
#   source = "../modules/ecs_task_definitions"
#   region = var.region
#   container_definitions_json = jsonencode([{
#     "cpu" : 0,
#     "command" : [
#       "python3",
#       "main.py"
#     ],
#     "secrets" : [
#       {
#         "name" : "DB_PASSWORD",
#         "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
#         }, {
#         "name" : "ARGILLA_API_KEY",
#         "valueFrom" : aws_secretsmanager_secret.ARGILLA_API_KEY.arn
#       }
#     ],
#     "environment" : [
#       {
#         "name" : "DB_HOST",
#         "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
#         }, {
#         "name" : "DB_NAME",
#         "value" : "ravenclaw"
#         }, {
#         "name" : "DB_USER",
#         "value" : "ravenclaw"
#         }, {
#         "name" : "DB_PORT",
#         "value" : "5432"
#         }, {
#         "name" : "ARGILLA_URL",
#         "value" : "http://${module.annotations.ec2_private_ip}:6900"
#       }
#     ],
#     "environmentFiles" : [],
#     "essential" : true,
#     "image" : var.annotation_pipeline_docker_image,
#     "logConfiguration" : {
#       "logDriver" : "awslogs",
#       "options" : {
#         "awslogs-create-group" : "true",
#         "awslogs-group" : "annotation_pipeline_cron_1",
#         "awslogs-region" : var.region,
#         "awslogs-stream-prefix" : "ecs"
#       }
#     },
#     "secretOptions" : [],
#     "mountPoints" : [],
#     "name" : "annotation-cron-1",
#     "portMappings" : [],
#     "systemControls" : [],
#     "volumesFrom" : [],
#     "ulimits" : []
#   }])

#   cpu                = "512"
#   execution_role_arn = aws_iam_role.task_execution_role.arn
#   family             = "annotation"
#   memory             = "1024"
#   task_role_arn      = aws_iam_role.task_role.arn

#   tags = {
#     Name        = "annotation-cron1-task"
#     Environment = "prod"
#     Team        = "infra"
#     Product     = "annotations"
#   }
# }

# resource "aws_iam_role" "annotation_cron_task_execution_role" {
#   assume_role_policy = <<POLICY
#         {
#             "Statement": [
#                 {
#                     "Action": "sts:AssumeRole",
#                     "Effect": "Allow",
#                     "Principal": {
#                         "Service": "events.amazonaws.com"
#                     },
#                     "Sid": ""
#                 }
#             ],
#             "Version": "2012-10-17"
#         }
#     POLICY

#   description = "Allows ECS tasks to call AWS services on your behalf."

#   inline_policy {
#     name = "ECSEventBridgeTaskExecutionRole"
#     policy = jsonencode({
#       Version : "2012-10-17",
#       Statement : [{
#         Action : [
#           "kms:Decrypt",
#           "kms:Encrypt",
#           "kms:GenerateDataKey",
#           "kms:ReEncrypt*",
#           "kms:DescribeKey",
#           "secretsmanager:Batch*",
#           "secretsmanager:Describe*",
#           "secretsmanager:Get*",
#           "secretsmanager:List*",
#           "secretsmanager:Validate*",
#           "ssm:*",
#           "s3:Describe*",
#           "s3:Get*",
#           "s3:Put*",
#           "s3:Restore*",
#           "s3:Tag*",
#           "s3:List*",
#           "s3:Untag*",
#           "s3:Update*",
#           "s3:Create*",
#           "ecr:Get*",
#           "ecr:Describe*",
#           "ecr:Batch*",
#           "ecr:List*",
#           "ecr:Validate*",
#           "ecs:Create*",
#           "ecs:Deregister*",
#           "ecs:Describe*",
#           "ecs:Execute*",
#           "ecs:Get*",
#           "ecs:List*",
#           "ecs:Put*",
#           "ecs:Register*",
#           "ecs:Run*",
#           "ecs:Start*",
#           "ecs:Stop*",
#           "ecs:Submit*",
#           "ecs:Tag*",
#           "ecs:Untag*",
#           "ecs:Update*",
#           "logs:CreateLogStream",
#           "logs:PutLogEvents",
#           "ses:Create*",
#           "ses:Send*"
#           # "ec2:*"
#         ],
#         Effect : "Allow",
#         Resource : "*",
#         Sid : "ECSEventBridgeTaskExecutionRole"
#       }]
#     })
#   }

#   inline_policy {
#     name = "ecs-log"
#     policy = jsonencode({
#       Statement : [{
#         Action : [
#           "logs:CreateLogGroup",
#           "logs:CreateLogStream",
#           "logs:PutLogEvents",
#           "logs:DescribeLogStreams"
#         ],
#         Effect : "Allow",
#         Resource : [
#           "arn:aws:logs:*:*:*"
#         ]
#         }
#       ],
#       Version : "2012-10-17"
#     })
#   }

#   managed_policy_arns  = ["arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"]
#   max_session_duration = "3600"
#   name                 = "ecs-event-bridge-execution-role"
#   path                 = "/"
#   tags = {
#     Name        = "annotation-event-bridge-role"
#     Environment = "prod"
#     Team        = "infra"
#     Product     = "annotations"
#   }
# }

# resource "aws_cloudwatch_event_rule" "annotation_task_scheduler" {
#   name                = "annotation-cron-scheduler"
#   schedule_expression = "cron(30 18 * * ? *)"
# }

# resource "aws_cloudwatch_event_target" "ecs_task_target" {
#   rule     = aws_cloudwatch_event_rule.annotation_task_scheduler.name
#   arn      = module.ecs_cluster.ecs_cluster_arn
#   role_arn = aws_iam_role.annotation_cron_task_execution_role.arn

#   ecs_target {
#     task_count          = 1
#     task_definition_arn = module.cron_annotation_pipeline_task_definition.arn
#     launch_type         = "FARGATE"

#     network_configuration {
#       subnets          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#       security_groups  = [aws_security_group.annotations_cron_task_sg.id]
#       assign_public_ip = false
#     }
#     propagate_tags = "TASK_DEFINITION"
#     tags = {
#       Name        = "annotation-cron1-task-event-target"
#       Environment = "prod"
#       Team        = "infra"
#       Product     = "annotations"
#     }
#   }
# }
