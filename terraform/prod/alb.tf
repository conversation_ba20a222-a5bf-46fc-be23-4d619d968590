resource "aws_lb" "main" {
  name               = var.lb_name
  internal           = var.lb_internal
  load_balancer_type = var.lb_type
  security_groups    = [aws_security_group.ecs_alb_loadbalancer_sg.id]
  subnets            = [module.main_public_subnet_1.subnet_id, module.main_public_subnet_2.subnet_id, module.main_public_subnet_3.subnet_id]

  enable_deletion_protection = var.lb_deletion_protection
  idle_timeout               = 360

  access_logs {
    bucket  = aws_s3_bucket.logs.bucket
    prefix  = "alb/ravenclaw/access-logs"
    enabled = true
  }

  connection_logs {
    bucket  = aws_s3_bucket.logs.id
    prefix  = "alb/ravenclaw/connection-logs"
    enabled = true
  }

  tags = var.alb_tags
}

resource "aws_lb_target_group" "main" {
  name        = var.bff_lb_target_group_name
  target_type = var.bff_lb_target_group_target_type
  port        = var.bff_lb_target_group_port
  protocol    = var.bff_lb_target_group_protocol
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.target_group_bff_tags
  # health_check {
  #   path = var.bff_lb_target_group_health_check_path
  # }
}

resource "aws_lb_target_group" "gateway" {
  name        = var.gateway_lb_target_group_name
  target_type = var.gateway_lb_target_group_target_type
  port        = var.gateway_lb_target_group_port
  protocol    = var.gateway_lb_target_group_protocol
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.target_group_gateway_tags
  # health_check {
  #   path = var.gateway_lb_target_group_health_check_path
  # }
}



resource "aws_lb_listener" "main" {
  load_balancer_arn = aws_lb.main.arn
  port              = var.lb_listener_port
  protocol          = var.lb_listener_protocol
  ssl_policy        = var.lb_listener_ssl_policy

  certificate_arn                                              = var.certificate_arn
  routing_http_response_server_enabled                         = false
  routing_http_response_strict_transport_security_header_value = "max-age=********; includeSubDomains; preload"
  routing_http_response_x_content_type_options_header_value    = "nosniff"


  # default_action {
  #   type             = var.lb_listener_default_action_type
  #   target_group_arn = aws_lb_target_group.main.arn
  # }

  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "Either IP or endpoint may not be valid!"
      status_code  = "404"
    }
  }

  tags = var.alb_listener_tags
}

resource "aws_lb_listener_rule" "main" {
  listener_arn = aws_lb_listener.main.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.main.arn
  }

  condition {
    path_pattern {
      values = ["/v0/api/*"]
    }
  }

  tags = var.alb_listener_rule_bff_tags
}

resource "aws_lb_listener_rule" "gateway" {
  listener_arn = aws_lb_listener.main.arn
  priority     = 101

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.gateway.arn
  }

  condition {
    path_pattern {
      values = ["/v0/hooks/*"]
    }
  }

  tags = var.alb_listener_rule_gateway_tags
}



# resource "aws_alb_listener" "alb_http" {
#   load_balancer_arn = aws_lb.main.arn
#   port              = "80"
#   protocol          = "HTTP"

#   default_action {
#     type = "redirect"
#     redirect {
#       host        = "ravenclaw.ravenmail.io"
#       port        = "443"
#       protocol    = "HTTPS"
#       status_code = "HTTP_301"
#     }
#   }

#   tags = var.keycloak_alb_listener_tags
# }

# Keycloak ALB
resource "aws_lb" "keycloak" {
  name               = var.keycloak_lb_name
  internal           = var.keycloak_lb_internal
  load_balancer_type = var.keycloak_lb_type
  security_groups    = [aws_security_group.ecs_alb_loadbalancer_sg.id]
  subnets            = [module.main_public_subnet_1.subnet_id, module.main_public_subnet_2.subnet_id, module.main_public_subnet_3.subnet_id]

  enable_deletion_protection = var.keycloak_lb_deletion_protection
  idle_timeout               = 360

  access_logs {
    bucket  = aws_s3_bucket.logs.id
    prefix  = "alb/keycloak/access-logs"
    enabled = true
  }

  connection_logs {
    bucket  = aws_s3_bucket.logs.id
    prefix  = "alb/keycloak/connection-logs"
    enabled = true
  }

  tags = var.keycloak_alb_tags
}

resource "aws_lb_target_group" "keycloak" {
  name        = var.keycloak_lb_target_group_name
  target_type = var.keycloak_lb_target_group_target_type
  port        = var.keycloak_lb_target_group_port
  protocol    = var.keycloak_lb_target_group_protocol
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.target_group_keycloak_tags
  health_check {
    path     = var.keycloak_lb_target_group_health_check_path
    port     = var.kc_alb_health_check_port
    protocol = var.kc_alb_health_check_protocol
    matcher  = var.kc_alb_health_check_matcher
  }
}

resource "aws_lb_listener" "keycloak" {
  load_balancer_arn = aws_lb.keycloak.arn
  port              = var.keycloak_lb_listener_port
  protocol          = var.keycloak_lb_listener_protocol
  ssl_policy        = var.keycloak_lb_listener_ssl_policy

  certificate_arn                                              = var.keycloak_certificate_arn
  routing_http_response_server_enabled                         = false
  routing_http_response_strict_transport_security_header_value = "max-age=********; includeSubDomains; preload"
  routing_http_response_x_content_type_options_header_value    = "nosniff"

  default_action {
    type = var.kc_alb_listener_default_action_type

    target_group_arn = aws_lb_target_group.keycloak.id
  }

  tags = var.keycloak_alb_listener_tags
}

# resource "aws_alb_listener" "keycloak_http" {
#   load_balancer_arn = aws_lb.keycloak.arn
#   port              = var.kc_alb_http_listener_port
#   protocol          = var.kc_alb_http_listener_protocol

#   default_action {
#     type = var.kc_alb_http_listener_default_action_type
#     redirect {
#       host        = var.keycloak_hostname == "" ? aws_lb.keycloak.dns_name : var.keycloak_hostname
#       port        = var.kc_alb_http_listener_default_action_redirect_port
#       protocol    = var.kc_alb_http_listener_default_action_redirect_protocol
#       status_code = var.kc_alb_http_listener_default_action_redirect_status_code
#     }
#   }

#   tags = var.keycloak_alb_listener_tags
# }



resource "aws_lb_listener_rule" "keycloak_https_redirect" {
  listener_arn = aws_lb_listener.keycloak.arn
  count        = var.keycloak_hostname == "" ? 0 : 1

  action {
    type = var.kc_alb_https_redirect_action_type
    redirect {
      host        = var.keycloak_hostname
      port        = var.kc_alb_https_redirect_action_redirect_port
      protocol    = var.kc_alb_https_redirect_action_redirect_protocol
      status_code = var.kb_alb_https_redirect_action_redirect_status_code
    }
  }

  condition {
    host_header {
      values = [aws_lb.keycloak.dns_name]
    }
  }

  tags = var.alb_listener_rule_keycloak_tags
}

resource "aws_lb_listener_rule" "keycloak" {
  listener_arn = aws_lb_listener.keycloak.arn
  priority     = 100

  action {
    type             = var.kc_alb_listener_rule_100_action_type
    target_group_arn = aws_lb_target_group.keycloak.arn
  }

  condition {
    path_pattern {
      values = var.kc_alb_listener_rule_100_condition_path_pattern
    }
  }

  tags = var.alb_listener_rule_keycloak_tags
}
