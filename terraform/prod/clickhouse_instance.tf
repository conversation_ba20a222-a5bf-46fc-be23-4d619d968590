resource "tls_private_key" "clickhouse" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "aws_key_pair" "clickhouse" {
  key_name   = "clickhouse-ssh-key"
  public_key = tls_private_key.clickhouse.public_key_openssh
  tags = {
    Name        = "clickhouse-ssh-key"
    Environment = "prod"
    Application = "clickhouse"
    Team        = "infra"
    Product     = "clickhouse"
  }
}

# resource "local_file" "clickhouse-ssh-key" {
#   content              = tls_private_key.clickhouse.private_key_pem
#   filename             = "clickhouse-ssh-key.pem"
#   file_permission      = "0600"
#   directory_permission = "0700"
# }

data "aws_ami" "clickhouse_ami" {
  most_recent = true
  owners      = ["amazon"]
  filter {
    name   = "architecture"
    values = ["arm64"]
  }
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*"]
  }
}

module "clickhouse_sg" {
  source      = "../modules/security_groups"
  region      = "ap-south-1"
  name        = "clickhouse_sg"
  description = "SG for ClickHouse HTTP, native and Prometheus metrics interfaces"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "clickhouse-security-groups"
    Team        = "infra"
    Product     = "clickhouse"
    Environment = "prod"
  }

  # ───────────────────────────────────────────────────────────
  # Ingress: HTTP interface (port 8123) restricted to the VPC
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 8123
  ingress_to_port                       = 8123
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  # ───────────────────────────────────────────────────────────
  # Custom ingress: Native TCP (9000) & Prometheus metrics (9363)
  # ───────────────────────────────────────────────────────────
  ingress_custom_rules = [
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 9000
      to_port     = 9000
      ip_protocol = "tcp"
      description = "ClickHouse native TCP interface"
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 9363
      to_port     = 9363
      ip_protocol = "tcp"
      description = "Prometheus /metrics endpoint"
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 22
      to_port     = 22
      ip_protocol = "tcp"
      description = "SSH Port"
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 9100
      to_port     = 9100
      ip_protocol = "tcp"
      description = "node exporter"
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 1514
      to_port     = 1514
      ip_protocol = "tcp"
      description = "Agent connection service - TCP"
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 1514
      to_port     = 1514
      ip_protocol = "udp"
      description = "Agent connection service - UDP"
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 1515
      to_port     = 1515
      ip_protocol = "tcp"
      description = "Agent enrollment service"
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 1516
      to_port     = 1516
      ip_protocol = "tcp"
      description = "Wazuh cluster daemon"
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 55000
      to_port     = 55000
      ip_protocol = "tcp"
      description = "wazuh server RESTful API"
    },
  ]

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}


module "clickhouse" {
  ami                                     = data.aws_ami.clickhouse_ami.id
  source                                  = "../modules/ec2_instance"
  region                                  = var.region
  associate_public_ip_address             = false
  availability_zone                       = "ap-south-1b"
  instance_type                           = "t4g.2xlarge"
  root_block_device_volume_size           = "20"
  root_block_device_iops                  = 3000
  root_block_device_throughput            = 125
  root_block_device_volume_type           = "gp3"
  subnet_id                               = module.main_private_subnet_2.subnet_id
  vpc_security_group_ids                  = ["${module.clickhouse_sg.security_group_id}"]
  key_name                                = aws_key_pair.clickhouse.key_name
  monitoring                              = true
  root_block_device_encrypted             = true
  root_block_device_delete_on_termination = false
  ebs_optimized                           = true

  service_discovery_service_name = "clickhouse"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  iam_instance_profile = aws_iam_instance_profile.clickhouse_ec2_instance_profile.name

  attach_external_disk       = true
  external_volume_size       = 250
  external_volume_type       = "gp3"
  external_volume_encrypted  = true
  external_volume_iops       = 3000
  external_volume_throughput = 125

  tags = {
    Name        = "clickhouse"
    Environment = "prod"
    Application = "clickhouse"
    Team        = "infra"
    Product     = "clickhouse"
  }

  root_block_device_tags = {
    Snapshot = "true"
  }
}

resource "aws_iam_role" "clickhouse_ec2_role" {
  name = "ClickhouseEC2Role"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "ec2.amazonaws.com"
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })
}

# Define the IAM Policy for the EC2 Role
resource "aws_iam_policy" "clickhouse_ec2_policy" {
  name = "ClickhouseEC2Policy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      # Read permission for a specific RDS instance
      {
        "Effect" : "Allow",
        "Action" : [
          "rds:DescribeDBInstances",
          "rds:DescribeDBSnapshots",
          "rds:ListTagsForResource"
        ],
        "Resource" : module.rds.read_replica_arns,
        "Condition" : {
          "StringEquals" : {
            "aws:RequestedRegion" : "ap-south-1"
          }
        }
      },

      # Read permissions to all S3 buckets
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:List*",
          "s3:Get*",
          "s3:Describe*",
          "s3:Tag*",

        ],
        "Resource" : [
          "arn:aws:s3:::*"
        ],
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ],
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "ecr:Get*",
          "ecr:Describe*",
          "ecr:Batch*",
          "ecr:List*",
          "ecr:Validate*",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "ses:Create*",
          "ses:Send*",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ],
        Resource = "*"
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "clickhouse_ec2_role_policy_attachment" {
  role       = aws_iam_role.clickhouse_ec2_role.name
  policy_arn = aws_iam_policy.clickhouse_ec2_policy.arn
}

# Define the IAM Instance Profile
resource "aws_iam_instance_profile" "clickhouse_ec2_instance_profile" {
  name = "ClickhouseEC2InstanceProfile"
  role = aws_iam_role.clickhouse_ec2_role.name
}
