resource "aws_s3_bucket" "vulnerability-sast-codescan-files" {
  bucket = "vulnerability-sast-codescan-files"

  tags = {
    Name        = "Vulnerability-SAST-Codescan-Files"
    Environment = "prod"
    Team        = "infra"
    Product     = "compliance"
  }
}

resource "aws_s3_bucket_versioning" "vulnerability-sast-codescan-files" {
  bucket = aws_s3_bucket.vulnerability-sast-codescan-files.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "vulnerability-sast-codescan-files" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.vulnerability-sast-codescan-files]

  bucket = aws_s3_bucket.vulnerability-sast-codescan-files.id

  rule {
    id = "lifecycle_configuration"

    noncurrent_version_expiration {
      noncurrent_days = 365
    }

    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "vulnerability-sast-codescan-files" {
  bucket = aws_s3_bucket.vulnerability-sast-codescan-files.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "vulnerability-sast-codescan-files" {
  depends_on = [aws_s3_bucket_ownership_controls.vulnerability-sast-codescan-files]

  bucket = aws_s3_bucket.vulnerability-sast-codescan-files.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "vulnerability-sast-codescan-files" {
  bucket = aws_s3_bucket.vulnerability-sast-codescan-files.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_server_side_encryption_configuration" "vulnerability-sast-codescan-files" {
  bucket = aws_s3_bucket.vulnerability-sast-codescan-files.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.codescan.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}


data "tls_certificate" "github" {
  url = "https://token.actions.githubusercontent.com/.well-known/openid-configuration"
}

resource "aws_iam_openid_connect_provider" "github" {
  url             = "https://token.actions.githubusercontent.com"
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = data.tls_certificate.github.certificates[*].sha1_fingerprint

  tags = {
    Name        = "Github-OIDC-S3"
    Environment = "prod"
    Team        = "infra"
    Product     = "compliance"
  }
}

resource "aws_iam_role" "github_oidc_role" {
  name = "github-actions-s3-access-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Federated = aws_iam_openid_connect_provider.github.arn
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          StringLike = {
            "token.actions.githubusercontent.com:sub" = "repo:ravenmailio/ops:*"
          },
          "ForAllValues:StringEquals" : {
            "token.actions.githubusercontent.com:iss" : "https://token.actions.githubusercontent.com",
            "token.actions.githubusercontent.com:aud" : "sts.amazonaws.com"
          }
        }
      },
      {
        Effect = "Allow"
        Principal = {
          Federated = aws_iam_openid_connect_provider.github.arn
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          StringLike = {
            "token.actions.githubusercontent.com:sub" = "repo:ravenmailio/ravenclaw:*",
          },
          "ForAllValues:StringEquals" : {
            "token.actions.githubusercontent.com:iss" : "https://token.actions.githubusercontent.com",
            "token.actions.githubusercontent.com:aud" : "sts.amazonaws.com"
          }
        }
      }
    ]
  })
}

resource "aws_iam_policy" "s3_access_policy" {
  name = "github-actions-s3-access-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.vulnerability-sast-codescan-files.arn,
          "${aws_s3_bucket.vulnerability-sast-codescan-files.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:DescribeKey"
        ]
        Resource = "${aws_kms_key.codescan.arn}"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "github_oidc_role_policy_attachment" {
  role       = aws_iam_role.github_oidc_role.name
  policy_arn = aws_iam_policy.s3_access_policy.arn
}
