# HTTPCode_ELB_4XX_Count
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_http_code_4xx_count" {
  alarm_name          = "RAVENCLAW_ALB_HTTP_CODE_4XX_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = "1"
  metric_name         = "HTTPCode_ELB_4XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 30
  alarm_description   = "Alarm when Ravenclaw ALB 4XX Count is greater than 30 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_HTTP_CODE_4XX_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_http_code_4xx_count" {
  alarm_name          = "KEYCLOAK_ALB_HTTP_CODE_4XX_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = "1"
  metric_name         = "HTTPCode_ELB_4XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 30
  alarm_description   = "Alarm when Keycloak ALB 4XX Count is greater than 30 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_HTTP_CODE_4XX_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_http_code_4xx_count" {
#   alarm_name          = "INTERNAL_ALB_HTTP_CODE_4XX_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 10
#   datapoints_to_alarm = "5"
#   metric_name         = "HTTPCode_ELB_4XX_Count"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Internal ALB 4XX Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_HTTP_CODE_4XX_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }



# HTTPCode_ELB_5XX_Count
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_http_code_5xx_count" {
  alarm_name          = "RAVENCLAW_ALB_HTTP_CODE_5XX_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = "1"
  metric_name         = "HTTPCode_ELB_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 30
  alarm_description   = "Alarm when Ravenclaw ALB 5XX Count is greater than 30 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_HTTP_CODE_5XX_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_http_code_5xx_count" {
  alarm_name          = "KEYCLOAK_ALB_HTTP_CODE_5XX_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = "1"
  metric_name         = "HTTPCode_ELB_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 30
  alarm_description   = "Alarm when Keycloak ALB 5XX Count is greater than 30 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_HTTP_CODE_5XX_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_http_code_5xx_count" {
#   alarm_name          = "INTERNAL_ALB_HTTP_CODE_5XX_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 10
#   datapoints_to_alarm = "5"
#   metric_name         = "HTTPCode_ELB_5XX_Count"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Internal ALB 5XX Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#   }

#   tags = {
#     Name        = "KEYCLOAK_ALB_HTTP_CODE_5XX_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }



# AnomalousHostCount
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_bff_target_group_anomalous_host_count" {
  alarm_name          = "RAVENCLAW_ALB_BFF_TARGET_GROUP_ANOMALOUS_HOST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "AnomalousHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Ravenclaw ALB BFF Target Group has at least 5 anomalous host in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/bff-target-group/7e39e96f80330248"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_BFF_TARGET_GROUP_ANOMALOUS_HOST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_gateway_target_group_anomalous_host_count" {
  alarm_name          = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_ANOMALOUS_HOST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "AnomalousHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Ravenclaw ALB Gateway Target Group has at least 5 anomalous host in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/gateway-target-group/837c9a768a311e51"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_BFF_TARGET_GROUP_ANOMALOUS_HOST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_target_group_anomalous_host_count" {
  alarm_name          = "KEYCLOAK_ALB_TARGET_GROUP_ANOMALOUS_HOST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "AnomalousHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Keycloak ALB Target Group has equal or more than 5 anomalous host in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
    TargetGroup  = "targetgroup/keycloak-target-group/6506fd254dacc48b"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_TARGET_GROUP_ANOMALOUS_HOST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_bff_target_group_anomalous_host_count" {
#   alarm_name          = "INTERNAL_ALB_ML_INGESTION_TARGET_GROUP_ANOMALOUS_HOST_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "AnomalousHostCount"
#   namespace           = "AWS/ApplicationELB"
#   period              = 300
#   statistic           = "Sum"
#   threshold           = 5
#   alarm_description   = "Alarm when Internal ALB ML Inference - Ingestion Target Group has at least 5 anomalous host in last 5 minute"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#     TargetGroup  = "targetgroup/ml-ingestion-target-group/bb2da62d41893f20"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_ML_INGESTION_TARGET_GROUP_ANOMALOUS_HOST_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }



# RejectedConnectionCount
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_rejected_connection_count" {
  alarm_name          = "RAVENCLAW_ALB_REJECTED_CONNECTION_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "RejectedConnectionCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 10
  alarm_description   = "Alarm when Ravenclaw ALB has equal or more than 10 connection rejections in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_REJECTED_CONNECTION_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_rejected_connection_count" {
  alarm_name          = "KEYCLOAK_ALB_REJECTED_CONNECTION_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "RejectedConnectionCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 10
  alarm_description   = "Alarm when Keycloak ALB has equal or more than 10 connection rejections in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_REJECTED_CONNECTION_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_rejected_connection_count" {
#   alarm_name          = "INTERNAL_ALB_REJECTED_CONNECTION_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "RejectedConnectionCount"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Internal ALB has equal or more than 10 connection rejections in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_REJECTED_CONNECTION_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }





# HTTPCode_Target_4XX_Count
# resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_http_code_target_4xx_count" {
#   alarm_name          = "RAVENCLAW_ALB_HTTP_CODE_TARGET_4XX_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 10
#   datapoints_to_alarm = "5"
#   metric_name         = "HTTPCode_Target_4XX_Count"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Ravenclaw ALB 4XX Target Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
#   }

#   tags = {
#     Name        = "RAVENCLAW_ALB_HTTP_CODE_TARGET_4XX_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }

# resource "aws_cloudwatch_metric_alarm" "keycloak_alb_http_code_target_4xx_count" {
#   alarm_name          = "KEYCLOAK_ALB_HTTP_CODE_TARGET_4XX_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 10
#   datapoints_to_alarm = "5"
#   metric_name         = "HTTPCode_Target_4XX_Count"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Keycloak ALB 4XX Target Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
#   }

#   tags = {
#     Name        = "KEYCLOAK_ALB_HTTP_CODE_TARGET_4XX_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }

# resource "aws_cloudwatch_metric_alarm" "internal_alb_http_code_target_4xx_count" {
#   alarm_name          = "INTERNAL_ALB_HTTP_CODE_TARGET_4XX_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 10
#   datapoints_to_alarm = "5"
#   metric_name         = "HTTPCode_Target_4XX_Count"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Internal ALB 4XX Target Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_HTTP_CODE_TARGET_4XX_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }

# HTTPCode_Target_5XX_Count
# resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_http_code_target_5xx_count" {
#   alarm_name          = "RAVENCLAW_ALB_HTTP_CODE_TARGET_5XX_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 10
#   datapoints_to_alarm = "5"
#   metric_name         = "HTTPCode_Target_5XX_Count"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Ravenclaw ALB 5XX Target Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
#   }

#   tags = {
#     Name        = "RAVENCLAW_ALB_HTTP_CODE_5XX_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }

# resource "aws_cloudwatch_metric_alarm" "keycloak_alb_http_code_target_5xx_count" {
#   alarm_name          = "KEYCLOAK_ALB_HTTP_CODE_TARGET_5XX_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 10
#   datapoints_to_alarm = "5"
#   metric_name         = "HTTPCode_Target_5XX_Count"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Keycloak ALB 5XX Target Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
#   }

#   tags = {
#     Name        = "KEYCLOAK_ALB_HTTP_CODE_TARGET_5XX_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }

# resource "aws_cloudwatch_metric_alarm" "internal_alb_http_code_target_5xx_count" {
#   alarm_name          = "INTERNAL_ALB_HTTP_CODE_TARGET_5XX_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 10
#   datapoints_to_alarm = "5"
#   metric_name         = "HTTPCode_Target_5XX_Count"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Internal ALB 5XX Target Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_HTTP_CODE_5XX_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }



# TargetConnectionErrorCount
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_target_connection_error_count" {
  alarm_name          = "RAVENCLAW_ALB_TARGET_CONNECTION_ERROR_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "TargetConnectionErrorCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 10
  alarm_description   = "Alarm when Ravenclaw ALB Target Connection Error Count is greater than 10 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_TARGET_CONNECTION_ERROR_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_target_connection_error_count" {
  alarm_name          = "KEYCLOAK_ALB_TARGET_CONNECTION_ERROR_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "TargetConnectionErrorCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 10
  alarm_description   = "Alarm when Keycloak ALB Target Connection Error Count is greater than 10 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_TARGET_CONNECTION_ERROR_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_target_connection_error_count" {
#   alarm_name          = "INTERNAL_ALB_TARGET_CONNECTION_ERROR_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "TargetConnectionErrorCount"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Internal ALB Target Connection Error Count is greater than 10 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_TARGET_CONNECTION_ERROR_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }




# TargetResponseTime
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_target_response_time" {
  alarm_name          = "RAVENCLAW_ALB_TARGET_RESPONSE_TIME"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "TargetResponseTime"
  namespace           = "AWS/ApplicationELB"
  period              = 120
  statistic           = "Average"
  threshold           = 10
  alarm_description   = "Alarm when Ravenclaw ALB Target Response Time is more than 10 sec (avg) in last 2 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_TARGET_RESPONSE_TIME"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_target_response_time" {
  alarm_name          = "KEYCLOAK_ALB_TARGET_RESPONSE_TIME"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "TargetResponseTime"
  namespace           = "AWS/ApplicationELB"
  period              = 120
  statistic           = "Average"
  threshold           = 10
  alarm_description   = "Alarm when Keycloak ALB Target Response Time is more than 10 sec (avg) in last 2 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_TARGET_RESPONSE_TIME"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_target_response_time" {
#   alarm_name          = "INTERNAL_ALB_TARGET_RESPONSE_TIME"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "TargetResponseTime"
#   namespace           = "AWS/ApplicationELB"
#   period              = 120
#   statistic           = "Average"
#   threshold           = 10
#   alarm_description   = "Alarm when Internal ALB Target Response Time is more than 10 sec (avg) in last 2 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_TARGET_RESPONSE_TIME"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }



# TargetTLSNegotiationErrorCount
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_target_tls_negotiation_error_count" {
  alarm_name          = "RAVENCLAW_ALB_TARGET_TLS_NEGOTIATION_ERROR_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "TargetTLSNegotiationErrorCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 10
  alarm_description   = "Alarm when Ravenclaw ALB Target TLS Negotiation Error Count is equal to or more than 10 times in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_TARGET_TLS_NEGOTIATION_ERROR_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_target_tls_negotiation_error_count" {
  alarm_name          = "KEYCLOAK_ALB_TARGET_TLS_NEGOTIATION_ERROR_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "TargetTLSNegotiationErrorCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 10
  alarm_description   = "Alarm when Keycloak ALB Target TLS Negotiation Error Count is equal to or more than 10 times in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_TARGET_TLS_NEGOTIATION_ERROR_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_target_tls_negotiation_error_count" {
#   alarm_name          = "INTERNAL_ALB_TARGET_TLS_NEGOTIATION_ERROR_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "TargetTLSNegotiationErrorCount"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 10
#   alarm_description   = "Alarm when Internal ALB Target TLS Negotiation Error Count is equal to or more than 10 times in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_TARGET_TLS_NEGOTIATION_ERROR_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }



# UnHealthyHostCount
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_bff_target_grp_unhealthy_host_count" {
  alarm_name          = "RAVENCLAW_ALB_BFF_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Ravenclaw ALB BFF Target Group - Unhealthy host count is at least 5"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/bff-target-group/7e39e96f80330248"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_BFF_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_gateway_target_grp_unhealthy_host_count" {
  alarm_name          = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Ravenclaw ALB Gateway Target Group - Unhealthy host count is at least 5"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/gateway-target-group/837c9a768a311e51"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_target_grp_unhealthy_host_count" {
  alarm_name          = "KEYCLOAK_ALB_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Keycloak ALB Gateway Target Group - Unhealthy host count is at least 5"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
    TargetGroup  = "targetgroup/keycloak-target-group/6506fd254dacc48b"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_ml_ingestion_target_grp_unhealthy_host_count" {
#   alarm_name          = "INTERNAL_ALB_BFF_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "UnHealthyHostCount"
#   namespace           = "AWS/ApplicationELB"
#   period              = 300
#   statistic           = "Sum"
#   threshold           = 5
#   alarm_description   = "Alarm when Internal ALB BFF Target Group - Unhealthy host count is at least 5"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#     TargetGroup  = "targetgroup/ml-ingestion-target-group/bb2da62d41893f20"
#   }

#   tags = {
#     Name        = "INGESTION_ALB_ML_INGESTION_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }



# UnhealthyRoutingRequestCount
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_bff_target_grp_unhealthy_routing_request_count" {
  alarm_name          = "RAVENCLAW_ALB_BFF_TARGET_GROUP_UNHEALTHY_ROUTING_REQUEST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyRoutingRequestCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 100
  alarm_description   = "Alarm when Ravenclaw ALB BFF Target Group - Unhealthy Routing Request Count is at least 100 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/bff-target-group/7e39e96f80330248"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_BFF_TARGET_GROUP_UNHEALTHY_ROUTING_REQUEST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_gateway_target_grp_unhealthy_routing_request_count" {
  alarm_name          = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_UNHEALTHY_ROUTING_REQUEST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyRoutingRequestCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 100
  alarm_description   = "Alarm when Ravenclaw ALB Gateway Target Group - Unhealthy routing request count is at least 100 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/gateway-target-group/837c9a768a311e51"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_UNHEALTHY_ROUTING_REQUEST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_target_grp_unhealty_routing_request_count" {
  alarm_name          = "KEYCLOAK_ALB_TARGET_GROUP_UNHEALTHY_ROUTING_REQUEST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyRoutingRequestCount"
  namespace           = "AWS/ApplicationELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 100
  alarm_description   = "Alarm when Keycloak ALB Gateway Target Group - Unhealthy Routing Request Count is at least 100 in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
    TargetGroup  = "targetgroup/keycloak-target-group/6506fd254dacc48b"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_TARGET_GROUP_UNHEALTHY_ROUTING_REQUEST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_ml_ingestion_target_grp_unhealthy_routing_request_count" {
#   alarm_name          = "INTERNAL_ALB_ML_INGESTION_TARGET_GROUP_UNHEALTHY_ROUTING_REQUEST_COUNT"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "UnhealthyRoutingRequestCount"
#   namespace           = "AWS/ApplicationELB"
#   period              = 600
#   statistic           = "Sum"
#   threshold           = 100
#   alarm_description   = "Alarm when Internal ALB ML Ingestion Target Group - Unhealthy Routing Request Count is at least 100 in last 10 minutes"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#     TargetGroup  = "targetgroup/ml-inference-target-group/bb2da62d41893f20"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_BFF_TARGET_GROUP_UNHEALTHY_ROUTING_REQUEST_COUNT"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }


# UnhealthyStateDNS
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_bff_target_grp_unhealthy_state_dns" {
  alarm_name          = "RAVENCLAW_ALB_BFF_TARGET_GROUP_UNHEALTHY_STATE_DNS"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyStateDNS"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Ravenclaw ALB BFF Target Group - Unhealthy State DNS Count is at least 5 in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/bff-target-group/7e39e96f80330248"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_BFF_TARGET_GROUP_UNHEALTHY_STATE_DNS"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_gateway_target_grp_unhealthy_state_dns" {
  alarm_name          = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_UNHEALTHY_STATE_DNS"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyStateDNS"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Ravenclaw ALB Gateway Target Group - Unhealthy State DNS is at least 5 in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/gateway-target-group/837c9a768a311e51"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_UNHEALTHY_STATE_DNS"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_target_grp_unhealty_state_dns" {
  alarm_name          = "KEYCLOAK_ALB_TARGET_GROUP_UNHEALTHY_STATE_DNS"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyStateDNS"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Keycloak ALB Gateway Target Group - Unhealthy State DNS is at least 5 in last 5 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
    TargetGroup  = "targetgroup/keycloak-target-group/6506fd254dacc48b"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_TARGET_GROUP_UNHEALTHY_STATE_DNS"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_ml_ingestion_target_grp_unhealthy_state_dns" {
#   alarm_name          = "INTERNAL_ALB_ML_INGESTION_TARGET_GROUP_UNHEALTHY_STATE_DNS"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "UnhealthyStateDNS"
#   namespace           = "AWS/ApplicationELB"
#   period              = 300
#   statistic           = "Sum"
#   threshold           = 5
#   alarm_description   = "Alarm when INTERNAL ALB ML Ingestion Target Group - Unhealthy State DNS Count is at least 1 in last 1 minute"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#     TargetGroup  = "targetgroup/ml-inference-target-group/bb2da62d41893f20"
#   }

#   tags = {
#     Name        = "INTERNAL_ALB_ML_INGESTION_TARGET_GROUP_UNHEALTHY_STATE_DNS"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }



# UnhealthyStateRouting
resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_bff_target_grp_unhealthy_state_routing" {
  alarm_name          = "RAVENCLAW_ALB_BFF_TARGET_GROUP_UNHEALTHY_STATE_ROUTING"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyStateRouting"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Ravenclaw ALB BFF Target Group - Unhealthy State Routes is at least 5 in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/bff-target-group/7e39e96f80330248"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_BFF_TARGET_GROUP_UNHEALTHY_STATE_ROUTING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "ravenclaw_alb_gateway_target_grp_unhealthy_state_routing" {
  alarm_name          = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_UNHEALTHY_STATE_ROUTING"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyStateRouting"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Ravenclaw ALB Gateway Target Group - Unhealthy State Routes is at least 5 in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-ravenclaw-alb/b406e97cdb25ea22"
    TargetGroup  = "targetgroup/gateway-target-group/837c9a768a311e51"
  }

  tags = {
    Name        = "RAVENCLAW_ALB_GATEWAY_TARGET_GROUP_UNHEALTHY_STATE_ROUTING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_alb_target_grp_unhealty_state_routing" {
  alarm_name          = "KEYCLOAK_ALB_TARGET_GROUP_UNHEALTHY_STATE_ROUTING"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnhealthyStateRouting"
  namespace           = "AWS/ApplicationELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Keycloak ALB Target Group - Unhealthy State Routes is at least 5 in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "app/ecs-keycloak-alb/8967f95b354d91fd"
    TargetGroup  = "targetgroup/keycloak-target-group/6506fd254dacc48b"
  }

  tags = {
    Name        = "KEYCLOAK_ALB_TARGET_GROUP_UNHEALTHY_STATE_ROUTING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "alb"
  }
}

# resource "aws_cloudwatch_metric_alarm" "internal_alb_ml_ingestion_target_grp_unhealthy_state_routing" {
#   alarm_name          = "INTERNAL_ALB_ML_INGESTION_TARGET_GROUP_UNHEALTHY_STATE_ROUTING"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "UnhealthyStateRouting"
#   namespace           = "AWS/ApplicationELB"
#   period              = 300
#   statistic           = "Sum"
#   threshold           = 5
#   alarm_description   = "Alarm when Internal ALB ML Ingestion Target Group - Unhealthy State Routes is at least 5 in last 5 minute"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.alb_alerts.arn]
#   dimensions = {
#     LoadBalancer = "app/prod-alb/faf2b785fdbd0154"
#     TargetGroup  = "targetgroup/ml-inference-target-group/bb2da62d41893f20"
#   }

#   tags = {
#     Name        = "INGESTION_ALB_ML_INGESTION_TARGET_GROUP_UNHEALTHY_STATE_ROUTING"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "alb"
#   }
# }
