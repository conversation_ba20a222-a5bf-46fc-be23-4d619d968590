# ALB
variable "alb_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "target_group_gateway_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-target-group-gateway"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "target_group_bff_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-target-group-bff"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_listener_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-listener"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_listener_rule_bff_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-listener-rule-bff"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_listener_rule_gateway_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-listener-rule-gateway"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# ECS Cluster
variable "ecs_cluster_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ecs-cluster"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_namespace_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ecs-service-discovery-dns-ns"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "bff_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-svc"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "bff_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-sd"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-svc"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "gateway_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-sd"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "remediator_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-svc"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "remediator_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-sd"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ml_inference_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ml-inference-svc"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "python"
  }
}

variable "ml_inference_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ml-inference-sd"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Route table
variable "rt_nat_private_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rt-private-1"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "rt_nat_private_2_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rt-private-2"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "rt_nat_private_3_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rt-private-3"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "rt_ig_public_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rt-public"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Secrets Manager
variable "nat_ti_username_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-ti-username"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nat_ti_password_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-ti-password"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_microsoft_suscription_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-microsoft-subscription-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pg_password_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-pg-password"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "groq_api_key_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-groq-api-key"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "intent_extraction_model_api_key_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-intent-extraction-model-api-key"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "url_scan_api_key_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-url-scan-api-key"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "virustotal_api_key_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-virustotal-api-key"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Security groups

variable "bastion_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bastion-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-alb-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ecs-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "bff_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "setup_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-setup-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nats_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "rds_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_rds_sg_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-rds-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "remediator_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ml_inference_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ml-inference-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "monitoring_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-monitoring-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

#VPC
variable "vpc_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-vpc"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Subnets
variable "pvt_subnet_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-1-pvt-subnet"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pvt_subnet_2_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-2-pvt-subnet"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pvt_subnet_3_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-3-pvt-subnet"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pub_subnet_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-1-pub-subnet"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pub_subnet_2_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-2-pub-subnet"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pub_subnet_3_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-3-pub-subnet"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# RDS
variable "ravenclaw_rds_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-instance"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ravenclaw_rds_subnet_group_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-subnet-group"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ravenclaw_rds_kms_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-kms"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "postgresql"
  }
}

# NAT Gateway
variable "nat_gateway_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-gateway-1"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nat_gateway_2_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-gateway-2"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nat_gateway_3_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-gateway-3"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Internet Gateway
variable "internet_gateway_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-internet-gateway"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# IAM roles
variable "ecs_task_role_iam_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-task-role-iam"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_task_execution_role_iam_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-task-execution-role-iam"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Elastic IPs
variable "nat_gateway_1_eip_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-1-gateway-eip"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nat_gateway_2_eip_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-2-gateway-eip"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nat_gateway_3_eip_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-3-gateway-eip"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "bastion_eip_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bastion-eip"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# EC2
variable "nats_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-instance"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "bastion_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bastion-instance"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Autoscaling

variable "ecs_bff_autoscaling_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-autoscaling-target"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_gateway_autoscaling_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-autoscaling-target"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_remediator_autoscaling_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-autoscaling-target"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}


variable "bff_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-task-definition"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-task-definition"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "remediator_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-task-definition"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ml_inference_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ml-inference-task-definition"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "setup_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-setup-task-definition"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# s3
variable "webui_build_s3_bucket_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-webui-build-s3-bucket"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "webui_build_cloudfront_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-webui-build-cloudfront"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "monitoring_instance_tags" {
  type = map(string)
  default = {
    Name        = "monitoring-prod-instance"
    Environment = "prod"
    Product     = "monitoring"
    Team        = "infra"
    application = "alertmanager_grafana_prometheus"
  }
}

variable "keycloak_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-td"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-sd"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-ecs-service"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "keycloak"
  }
}

variable "keycloak_admin_username_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-admin-username"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_admin_password_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-admin-password"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

## Keycloak ALB
variable "keycloak_alb_listener_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-alb-listener"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "target_group_keycloak_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-alb-target-group"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_alb_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-alb"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_admin_client_secret_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-admin-client-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_listener_rule_keycloak_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-alb-listener-rule"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "rds_replica_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-read-replica"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}


variable "keycloak_pg_password_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-rds-password"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_rds_instance_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-rds-instance"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_rds_subnet_group_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-rds-subnet-group"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_rds_kms_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-rds-kms"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "postgresql"
  }
}

variable "keycloak_rds_replica_instance_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-rds-read-replica"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_keycloak_autoscaling_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-autoscaling-target"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "detection_model_api_key_tags" {
  type = map(string)
  default = {
    Name        = "detection-model-api-key"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "detection_model_endpoint_tags" {
  type = map(string)
  default = {
    Name        = "detection-model-endpoint"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "detection_model_deployment_tags" {
  type = map(string)
  default = {
    Name        = "detection-model-deployment"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ti_go_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ti-go-service-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ti_go_service_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ti-go-service"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "engineering"
  }
}

variable "ti_go_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ti-go-service-sd"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ti_go_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ti-go-service"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "ecs_ml_inference_autoscaling_tags" {
  type = map(string)
  default = {
    Name        = "ml-inference-autoscaling-target"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_ti_go_service_autoscaling_tags" {
  type = map(string)
  default = {
    Name        = "ti-go-service-autoscaling-target"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_admin_client_id_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-admin-client-id-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_service_account_id_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-service-account-id-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ms_auth_client_id_secret_tags" {
  type = map(string)
  default = {
    Name        = "ms-auth-client-id-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ms_auth_client_secret_tags" {
  type = map(string)
  default = {
    Name        = "ms-auth-client-secret"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ms_auth_tenant_id_tags" {
  type = map(string)
  default = {
    Name        = "ms-auth-tenant-id"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ingestion_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ingestion-sg"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ingestion_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ingestion-task-definition"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

## NATS clustering
variable "nats_cluster_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-1"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "nats_cluster_2_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-2"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "nats_cluster_3_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-3"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "nats_cluster_4_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-4"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}
