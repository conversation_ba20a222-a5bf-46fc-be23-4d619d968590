provider "aws" {
  region = var.region
}

terraform {

  backend "s3" {
    bucket                   = "terraform-test-bucket-2024-1"
    key                      = "test-tf"
    region                   = "us-east-1"
    shared_credentials_files = ["~/.aws/credentials"]
  }

  required_providers {
    aws = {
      version = ">= 5.85.0"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "4.0.6"
    }
    local = {
      source  = "hashicorp/local"
      version = "2.5.2"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "5.0.0-alpha1"
    }
  }
}
