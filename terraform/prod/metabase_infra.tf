resource "tls_private_key" "metabase_rsa" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "aws_key_pair" "metabase_ssh_key" {
  key_name   = "metabase-ec2"
  public_key = tls_private_key.metabase_rsa.public_key_openssh
}

# resource "local_file" "tf_key" {
#   content  = tls_private_key.metabase_rsa.private_key_pem
#   filename = "metabase_prod.pem"
# }

data "aws_ami" "metabase_ami" {
  most_recent = true
  owners      = ["amazon"]
  filter {
    name   = "architecture"
    values = ["arm64"]
  }
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*"]
  }
}


module "metabase_ec2" {
  source                        = "../modules/ec2_instance"
  region                        = var.region
  availability_zone             = "ap-south-1c"
  instance_type                 = "t4g.medium"
  root_block_device_volume_size = 50
  root_block_device_iops        = 3000
  root_block_device_throughput  = 125
  root_block_device_volume_type = "gp3"
  subnet_id                     = module.main_public_subnet_3.subnet_id
  vpc_security_group_ids        = ["${aws_security_group.metabase_sg.id}"]
  key_name                      = aws_key_pair.metabase_ssh_key.key_name
  associate_public_ip_address   = true
  ami                           = data.aws_ami.metabase_ami.id
  root_block_device_encrypted   = true
  monitoring                    = true

  service_discovery_service_name = "metabase"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  tags = {
    Name        = "metabase-instance"
    Team        = "infra"
    Product     = "analytics"
    Environment = "prod"
  }

  root_block_device_tags = {
    Snapshot = "true"
  }
}

resource "aws_security_group" "metabase_sg" {
  description = "SG for metabase ec2 instance"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "9101"
    protocol    = "tcp"
    self        = "false"
    to_port     = "9101"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "9192"
    protocol    = "tcp"
    self        = "false"
    to_port     = "9192"
  }

  ingress {
    description = "Agent connection service - TCP"
    from_port   = 1514
    to_port     = 1514
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "Agent connection service - UDP"
    from_port   = 1514
    to_port     = 1514
    protocol    = "udp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "Agent enrollment service"
    from_port   = 1515
    to_port     = 1515
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "Wazuh cluster daemon"
    from_port   = 1516
    to_port     = 1516
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  ingress {
    description = "wazuh server RESTful API"
    from_port   = 55000
    to_port     = 55000
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  name = "metabase_sg"
  tags = {
    Name        = "metabase-sg"
    Environment = "prod"
    Team        = "infra"
    Product     = "analytics"
  }
  vpc_id = module.vpc_main.vpc_id
}

########### Disk Snapshots #################
data "aws_iam_policy_document" "metabase_snapshots_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["dlm.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "metabase_snapshots_dlm_lifecycle_role" {
  name               = "metabase-snapshots-dlm-lifecycle-role"
  assume_role_policy = data.aws_iam_policy_document.metabase_snapshots_assume_role.json
}

data "aws_iam_policy_document" "metabase_snapshots_dlm_lifecycle" {
  statement {
    effect = "Allow"

    actions = [
      "ec2:CreateSnapshot",
      "ec2:CreateSnapshots",
      "ec2:DeleteSnapshot",
      "ec2:DescribeInstances",
      "ec2:DescribeVolumes",
      "ec2:DescribeSnapshots",
    ]

    resources = ["*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["ec2:CreateTags"]
    resources = ["arn:aws:ec2:*::snapshot/*"]
  }
}

resource "aws_iam_role_policy" "metabase_snapshots_dlm_lifecycle" {
  name   = "metabase-snapshots-dlm-lifecycle-policy"
  role   = aws_iam_role.metabase_snapshots_dlm_lifecycle_role.id
  policy = data.aws_iam_policy_document.metabase_snapshots_dlm_lifecycle.json
}

resource "aws_dlm_lifecycle_policy" "metabase_snapshots" {
  description        = "Metabase Snapshots DLM lifecycle policy"
  execution_role_arn = aws_iam_role.metabase_snapshots_dlm_lifecycle_role.arn
  state              = "ENABLED"

  policy_details {
    resource_types = ["VOLUME"]

    schedule {
      name = "daily snapshots"

      create_rule {
        interval      = 24
        interval_unit = "HOURS"
        times         = ["04:30"]
      }

      retain_rule {
        count = 1
      }

      tags_to_add = {
        SnapshotCreator = "DLM"
        SnapshotType    = "Daily"
        Environment     = "prod"
        Team            = "infra"
      }

      copy_tags = false
    }

    target_tags = {
      Snapshot = "true"
    }
  }

  tags = {
    Name        = "snapshots-dlm-lifecycle-policy"
    Environment = "prod"
    Team        = "infra"
    Product     = "analytics"
  }
}
