import json
import requests
import os
import boto3
import logging
import time
from datetime import datetime
import pytz

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize the CloudWatch Logs client
logs_client = boto3.client('logs')

MAX_GOOGLE_CHAT_MESSAGE_SIZE = 3800

def covert_time(time_str):
    dt_utc = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%S.%f%z")
    timezone_530 = pytz.timezone('Asia/Kolkata')
    dt_530 = dt_utc.astimezone(timezone_530)
    return dt_530.strftime("%Y-%m-%d %H:%M:%S %Z%z")

def filter_error_logs(log_lines):
    """Filters the log lines to only include lines containing 'error', 'Error', or 'ERROR'."""
    error_keywords = ['error', 'Error', 'ERROR']
    return "\n".join(line for line in log_lines if any(keyword in line for keyword in error_keywords))

def lambda_handler(event, context):
    google_chat_webhook_url = os.environ['GOOGLE_CHAT_WEBHOOK_URL']

    try:
        for record in event['Records']:
            sns_message = json.loads(record['Sns']['Message'])
            alarm_name = sns_message['AlarmName']
            alarm_description = sns_message['AlarmDescription']
            state_change_time = sns_message['StateChangeTime']
            log_group_name = sns_message['Trigger']['Namespace'].split('_')[1]
            region = sns_message['AlarmArn'].split(':')[3]

            if not log_group_name:
                logger.error("Log group name not found in dimensions")
                raise ValueError("Log group name not found in dimensions")

            # Convert the state change time to a timestamp
            alarm_timestamp = datetime.strptime(state_change_time, '%Y-%m-%dT%H:%M:%S.%f%z').timestamp() * 1000
            # Generate log stream link
            log_stream_link = f"https://{region}.console.aws.amazon.com/cloudwatch/home?region={region}#logsV2:log-groups/log-group/{log_group_name.replace('/', '$252F')}/log-events"


                        
            # Format the message for Google Chat
            message = f"*Alarm Triggered: {alarm_name}*\n\n*Description:* {alarm_description}\n\n- *Trigger Reason:* {sns_message['NewStateReason']}\n- *Trigger Time:* {covert_time(sns_message['StateChangeTime'])}\n- *Metric Name:* {sns_message['Trigger']['MetricName']}\n- *Log Group Name:* {log_group_name}\n- *CloudWatch Logs Link:* <{log_stream_link}|Link> \n\n"

            headers = {
                'Content-Type': 'application/json; charset=UTF-8',
            }

            data = {
                'text': message,
            }

            response = requests.post(google_chat_webhook_url, headers=headers, data=json.dumps(data))


            # TODO: make this piece of code modular
            if response.status_code != 200:
                # taking only the google chat API's character limit
                message = f"*Alarm Triggered: {alarm_name}*\n\n*Description:* {alarm_description}\n\n- *Trigger Reason:* {sns_message['NewStateReason']}\n- *Trigger Time:* {covert_time(sns_message['StateChangeTime'])}\n- *Metric Name:* {sns_message['Trigger']['MetricName']}\n- *Log Group Name:* {log_group_name}\n- *CloudWatch Logs Link:* <{log_stream_link}|Link> \n\n"
                data = {
                    'text': message,
                }

                responseFirstRetry = requests.post(google_chat_webhook_url, headers=headers, data=json.dumps(data))

        logger.info("Notification sent to Google Chat")
        return {
            'statusCode': 200,
            'body': json.dumps('Notification sent to Google Chat')
        }

    except Exception as e:
        logger.error("Error processing event: %s", str(e))
        raise e

def get_relevant_log_lines(log_group_name, start_time, end_time):
    log_lines = []
    kwargs = {}

    try:
        while True:
            response = logs_client.filter_log_events(
                logGroupName=log_group_name,
                startTime=int(start_time),
                endTime=int(end_time),
                limit=100,  # Adjust limit as needed
                **kwargs
            )

            for event in response.get('events', []):
                log_lines.append(event.get('message', ''))

            kwargs['nextToken'] = response.get('nextToken')
            if not kwargs['nextToken']:
                break

    except Exception as e:
        logger.error("Error fetching log events: %s", str(e))

    return "\n".join(log_lines)
