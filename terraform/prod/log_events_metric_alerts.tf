resource "aws_cloudwatch_metric_alarm" "ti_logs_absent_alert_warning" {
  alarm_name          = "TI_LOGS_ABSENT_12_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 43200
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no TI Logs since last 12 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/ti-go-service"
  }

  tags = {
    Name        = "TI_LOGS_ABSENT_12_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "bff_logs_absent_alert_warning" {
  alarm_name          = "BFF_LOGS_ABSENT_12_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 43200
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no BFF Logs since last 12 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/bff"
  }

  tags = {
    Name        = "BFF_LOGS_ABSENT_12_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "gateway_logs_absent_alert_warning" {
  alarm_name          = "GATEWAY_LOGS_ABSENT_12_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 43200
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no GATEWAY Logs since last 12 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/gateway"
  }

  tags = {
    Name        = "GATEWAY_LOGS_ABSENT_12_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "ml_inference_logs_absent_alert_warning" {
  alarm_name          = "ML_INFERENCE_LOGS_ABSENT_12_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 43200
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no ML Inference Logs since last 12 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/ml-inference"
  }

  tags = {
    Name        = "ML_INFERENCE_LOGS_ABSENT_12_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "remediator_logs_absent_alert_warning" {
  alarm_name          = "REMEDIATOR_LOGS_ABSENT_12_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 43200
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no REMEDIATOR Logs since last 12 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/remediator"
  }

  tags = {
    Name        = "REMEDIATOR_LOGS_ABSENT_12_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "ti_logs_absent_high_warning" {
  alarm_name          = "TI_LOGS_ABSENT_24_HOURS_HIGH"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 86400
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no TI Logs since last 24 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/ti-go-service"
  }

  tags = {
    Name        = "TI_LOGS_ABSENT_24_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "bff_logs_absent_alert_high" {
  alarm_name          = "BFF_LOGS_ABSENT_24_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 86400
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no BFF Logs since last 24 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/bff"
  }

  tags = {
    Name        = "BFF_LOGS_ABSENT_24_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "gateway_logs_absent_alert_high" {
  alarm_name          = "GATEWAY_LOGS_ABSENT_24_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 86400
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no GATEWAY Logs since last 24 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/gateway"
  }

  tags = {
    Name        = "GATEWAY_LOGS_ABSENT_24_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "ml_inference_logs_absent_alert_high" {
  alarm_name          = "ML_INFERENCE_LOGS_ABSENT_24_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 86400
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no ML Inference Logs since last 24 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/ml-inference"
  }

  tags = {
    Name        = "ML_INFERENCE_LOGS_ABSENT_24_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_metric_alarm" "remediator_logs_absent_alert_high" {
  alarm_name          = "REMEDIATOR_LOGS_ABSENT_24_HOURS_WARNING"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "IncomingLogEvents"
  namespace           = "AWS/Logs"
  period              = 86400
  statistic           = "Average"
  threshold           = 1
  alarm_description   = "Alarm when there are no REMEDIATOR Logs since last 24 hours"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.log_events_alerts.arn]
  dimensions = {
    LogGroupName = "/ecs/remediator"
  }

  tags = {
    Name        = "REMEDIATOR_LOGS_ABSENT_24_HOURS_WARNING"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
  }
}
