########### go smtp

resource "aws_lb" "smtp" {
  name               = "gosmtp-server-nlb"
  internal           = false
  load_balancer_type = "network"
  security_groups    = [module.microsoft_smtp_nlb_sg.security_group_id, module.google_smtp_nlb_sg.security_group_id]
  subnets            = [module.main_public_subnet_1.subnet_id, module.main_public_subnet_2.subnet_id, module.main_public_subnet_3.subnet_id]

  enable_deletion_protection = true

  access_logs {
    bucket  = aws_s3_bucket.logs.bucket
    prefix  = "nlb/gosmtp/access-logs"
    enabled = true
  }


  tags = {
    Name        = "gosmtp-server-nlb"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_lb_target_group" "gosmtp" {
  name                               = "gosmtp-server-tg-2525"
  target_type                        = "ip"
  port                               = 2525
  protocol                           = "TCP"
  lambda_multi_value_headers_enabled = false
  slow_start                         = 0
  vpc_id                             = module.vpc_main.vpc_id
  tags = {
    Name        = "gosmtp-server-tg-2525"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
  health_check {
    protocol = "TCP"
  }
}

resource "aws_lb_listener" "smtp" {
  load_balancer_arn                                            = aws_lb.smtp.arn
  port                                                         = 25
  protocol                                                     = "TCP"
  tcp_idle_timeout_seconds                                     = 350
  routing_http_response_server_enabled                         = false
  routing_http_response_strict_transport_security_header_value = "max-age=********; includeSubDomains; preload"
  routing_http_response_x_content_type_options_header_value    = "nosniff"
  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.gosmtp.arn
  }

  tags = {
    Name        = "gosmtp-nlb-listener"
    Environment = "prod"
    Product     = "gosmtp"
    Team        = "infra"
  }
}

