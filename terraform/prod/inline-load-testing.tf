# module "nats-load-test" {
#   source                                  = "../modules/ec2_instance"
#   region                                  = var.region
#   availability_zone                       = "ap-south-1c"
#   instance_type                           = "t3a.large"
#   root_block_device_volume_size           = 200
#   root_block_device_iops                  = var.nats_root_block_device_iops
#   root_block_device_throughput            = var.nats_root_block_device_throughput
#   root_block_device_volume_type           = var.nats_root_block_device_volume_type
#   subnet_id                               = module.main_private_subnet_3.subnet_id
#   vpc_security_group_ids                  = ["${aws_security_group.nats_sg.id}"]
#   key_name                                = "nats-load-test-ssh-key"
#   root_block_device_encrypted             = true
#   root_block_device_delete_on_termination = false

#   iam_instance_profile           = aws_iam_instance_profile.CloudWatchAgentServerProfile.name
#   service_discovery_service_name = "nats-load-test"
#   namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

#   tags = {
#     Name        = "nats-load-test"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Application = "nats"
#   }
# }


variable "inline_load_testing_image_uri" {
  type = string
}

variable "inline_load_testing_log_group" {
  type    = string
  default = "/ecs/inline-load-testing"
}

variable "NATS_LOAD_TESTING_SERVER_URL" {
  type = string
}

variable "setup_load_testing_image_uri" {
  type = string
}



# resource "aws_secretsmanager_secret_version" "NATS_LOAD_TESTING_SERVER_URL" {
#   secret_id     = aws_secretsmanager_secret.NATS_LOAD_TESTING_SERVER_URL.arn
#   secret_string = var.NATS_LOAD_TESTING_SERVER_URL
#   lifecycle {
#     ignore_changes = [
#       secret_string
#     ]
#   }
# }

# resource "aws_secretsmanager_secret" "NATS_LOAD_TESTING_SERVER_URL" {
#   name                    = "NATS_LOAD_TESTING_SERVER_URL"
#   kms_key_id              = aws_kms_key.secretsmanager.key_id
#   recovery_window_in_days = 7

#   tags = {
#     Environment = "prod"
#     Name        = "nats-load-testing-server-url"
#     Product     = "nats"
#     Team        = "infra"
#   }
# }

# module "inline_load_testing_task_definition" {
#   source = "../modules/ecs_task_definitions"
#   region = var.region
#   container_definitions_json = jsonencode([
#     {
#       "command" : [
#         "gateway",
#         "inline"
#       ],
#       "cpu" : 0,
#       "environment" : [{
#         "name" : "AWS_REGION",
#         "value" : var.region
#         }, {
#         "name" : "DEPLOYMENT_ENV",
#         "value" : var.DEPLOYMENT_ENV
#         }, {
#         "name" : "NATS_FETCH_SIZE",
#         "value" : "5"
#         }, {
#         "name" : "NATS_MAX_BACKOFF_IN_SEC",
#         "value" : "5"
#         }, {
#         "name" : "NATS_MAX_PENDING",
#         "value" : "10"
#         }, {
#         "name" : "NATS_MAX_RECONNECT",
#         "value" : "10"
#         }, {
#         "name" : "NATS_MAX_RETRIES",
#         "value" : "0"
#         }, {
#         "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
#         "value" : "60"
#         }, {
#         "name" : "NATS_RECONNECT_WAIT_IN_SEC",
#         "value" : "5"
#         }, {
#         "name" : "PG_DB",
#         "value" : var.PG_DB
#         }, {
#         "name" : "PG_HOST",
#         "value" : module.rds.db_host
#         }, {
#         "name" : "PG_MAX_IDLE_CONNECTION",
#         "value" : "5"
#         }, {
#         "name" : "PG_MAX_OPEN_CONNECTION",
#         "value" : "20"
#         }, {
#         "name" : "PG_PASSWORD",
#         "value" : module.rds.db_password
#         }, {
#         "name" : "PG_PORT",
#         "value" : "5432"
#         }, {
#         "name" : "PG_SSL",
#         "value" : "disable"
#         }, {
#         "name" : "PG_USERNAME",
#         "value" : module.rds.db_username
#         }, {
#         "name" : "S3_KMS_KEY_ARN",
#         "value" : aws_kms_key.s3-orgs.arn
#         }, {
#         "name" : "SMARTHOST_DOMAIN",
#         "value" : "secure.ravenmail.io"
#         }, {
#         "name" : "SMARTHOST_PORT",
#         "value" : "2525"
#         }, {
#         "name" : "DKIM_ENABLED",
#         "value" : "true"
#       }],

#       "essential" : true,
#       "image" : var.inline_load_testing_image_uri,
#       "logConfiguration" : {
#         "logDriver" : "awslogs",
#         "options" : {
#           "awslogs-create-group" : "true",
#           "awslogs-group" : var.inline_load_testing_log_group,
#           "awslogs-region" : var.region,
#           "awslogs-stream-prefix" : "ecs"
#         }
#       },
#       "mountPoints" : [],
#       "name" : "inline-load-testing",
#       "portMappings" : [
#         {
#           "appProtocol" : "http",
#           "containerPort" : 2525,
#           "hostPort" : 2525,
#           "name" : "http",
#           "protocol" : "tcp"
#         }
#       ],
#       "secrets" : [
#         {
#           "name" : "NATS_SERVER_URL",
#           "valueFrom" : aws_secretsmanager_secret.NATS_LOAD_TESTING_SERVER_URL.arn
#         },
#         {
#           "name" : "SMARTHOST_TLS_CERT",
#           "valueFrom" : aws_secretsmanager_secret.SMARTHOST_TLS_CERT.arn
#         },
#         {
#           "name" : "SMARTHOST_TLS_KEY",
#           "valueFrom" : aws_secretsmanager_secret.SMARTHOST_TLS_KEY.arn
#         },
#         {
#           "name" : "DKIM_PRIVATE_KEY",
#           "valueFrom" : aws_secretsmanager_secret.DKIM_PRIVATE_KEY.arn
#         },

#       ],
#       "systemControls" : [],
#       "volumesFrom" : []
#     }
#   ])

#   cpu                      = "512"
#   execution_role_arn       = aws_iam_role.task_execution_role.arn
#   family                   = "inline-load-testing"
#   memory                   = "1024"
#   task_role_arn            = aws_iam_role.task_role.arn
#   network_mode             = "awsvpc"
#   requires_compatibilities = ["FARGATE"]
#   track_latest             = "false"

#   tags = {
#     Environment = "prod"
#     Name        = "inline-load-testing-task-definition"
#     Product     = "inline"
#     Team        = "infra"
#   }
# }

# module "inline_load_testing_service" {
#   source                         = "../modules/ecs_service"
#   region                         = var.region
#   namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
#   service_discovery_service_name = "inline-load-testing"
#   cluster                        = module.ecs_cluster.ecs_cluster_arn
#   security_groups_id             = [aws_security_group.gosmtp_service_sg.id]
#   subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   name                           = "inline-load-testing"
#   desired_count                  = 1
#   task_definition_arn            = module.inline_load_testing_task_definition.arn
#   availability_zone_rebalancing  = "ENABLED"

#   target_group_arn = aws_lb_target_group.gosmtp-load-testing.arn
#   container_name   = "inline-load-testing"
#   container_port   = 2525

#   service_discovery_svc_tags = {
#     Name        = "inline-load-testing-service-sd"
#     Team        = "infra"
#     Product     = "inline"
#     Environment = "prod"
#   }

#   ecs_service_tags = {
#     Name        = "inline-load-testing-service"
#     Team        = "infra"
#     Product     = "inline"
#     Environment = "prod"
#   }
# }

########### go smtp

# resource "aws_lb" "gosmtp-load-testing" {
#   name               = "gosmtp-server-nlb-load-testing"
#   internal           = false
#   load_balancer_type = "network"
#   security_groups    = [aws_security_group.smtp_nlb_loadbalancer_sg.id]
#   subnets            = [module.main_public_subnet_1.subnet_id, module.main_public_subnet_2.subnet_id, module.main_public_subnet_3.subnet_id]

#   enable_deletion_protection = false

#   access_logs {
#     bucket  = aws_s3_bucket.logs.bucket
#     prefix  = "nlb/gosmtp-load-testing/access-logs"
#     enabled = true
#   }


#   tags = {
#     Name        = "gosmtp-server-nlb-load-testing"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
# }

# resource "aws_lb_target_group" "gosmtp-load-testing" {
#   name                               = "gosmtp-server-tg-load-testing"
#   target_type                        = "ip"
#   port                               = 2525
#   protocol                           = "TCP"
#   lambda_multi_value_headers_enabled = false
#   slow_start                         = 0
#   vpc_id                             = module.vpc_main.vpc_id
#   tags = {
#     Name        = "gosmtp-server-tg-2525-load-testing"
#     Environment = "prod"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
#   health_check {
#     protocol = "TCP"
#   }
# }

# resource "aws_lb_listener" "gosmtp-load-testing" {
#   load_balancer_arn                                            = aws_lb.gosmtp-load-testing.arn
#   port                                                         = 2525
#   protocol                                                     = "TCP"
#   tcp_idle_timeout_seconds                                     = 350
#   routing_http_response_server_enabled                         = false
#   routing_http_response_strict_transport_security_header_value = "max-age=********; includeSubDomains; preload"
#   routing_http_response_x_content_type_options_header_value    = "nosniff"
#   default_action {
#     type             = "forward"
#     target_group_arn = aws_lb_target_group.gosmtp-load-testing.arn
#   }

#   tags = {
#     Name        = "gosmtp-nlb-listener-load-testing"
#     Environment = "prod"
#     Product     = "gosmtp"
#     Team        = "infra"
#   }
# }

# module "setup_load_testing_task_definition" {
#   source = "../modules/ecs_task_definitions"
#   region = var.region
#   container_definitions_json = jsonencode([{
#     "cpu" : 0,
#     "command" : [
#       "setup"
#     ],
#     "secrets" : [{
#       "name" : "NATS_SERVER_URL",
#       "valueFrom" : aws_secretsmanager_secret.NATS_LOAD_TESTING_SERVER_URL.arn
#       }, {
#       "name" : "PG_PASSWORD",
#       "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
#     }],
#     "environment" : [{
#       "name" : "PG_DB",
#       "value" : var.PG_DB
#       }, {
#       "name" : "PG_HOST",
#       "value" : module.rds.db_host
#       # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
#       }, {
#       "name" : "PG_PORT",
#       "value" : var.PG_PORT
#       }, {
#       "name" : "PG_USERNAME",
#       "value" : module.rds.db_username
#       }, {
#       "name" : "PG_SSL",
#       "value" : var.PG_SSL
#       }, {
#       "name" : "NATS_MAX_RETRIES",
#       "value" : "0"
#       }, {
#       "name" : "NATS_MAX_BACKOFF_IN_SEC",
#       "value" : "5"
#       }, {
#       "name" : "NATS_RECONNECT_WAIT_IN_SEC",
#       "value" : "5"
#       }, {
#       "name" : "NATS_MAX_RECONNECT",
#       "value" : "10"
#       }, {
#       "name" : "NATS_MAX_PENDING",
#       "value" : "20"
#       }, {
#       "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
#       "value" : "60"
#       }, {
#       "name" : "NATS_FETCH_SIZE",
#       "value" : "5"
#     }],
#     "environmentFiles" : [],
#     "essential" : true,
#     "image" : var.setup_load_testing_image_uri,
#     "logConfiguration" : {
#       "logDriver" : "awslogs",
#       "options" : {
#         "awslogs-create-group" : "true",
#         "awslogs-group" : "/ecs/setup-load-testing",
#         "awslogs-region" : var.region,
#         "awslogs-stream-prefix" : "ecs"
#       }
#     },
#     "secretOptions" : [],
#     "mountPoints" : [],
#     "name" : "setup-load-testing",
#     "portMappings" : [],
#     "systemControls" : [],
#     "volumesFrom" : [],
#     "ulimits" : []
#   }])

#   cpu                = var.setup_cpu
#   execution_role_arn = aws_iam_role.task_execution_role.arn
#   family             = "setup-load-testing"
#   memory             = var.setup_memory
#   task_role_arn      = aws_iam_role.task_role.arn

#   tags = {
# 		Environment = "prod"
# 		Name        = "setup-load-testing-task-definition"
# 		Product     = "inline"
# 		Team        = "infra"
# 	}
# }
