resource "aws_sns_topic" "rds_alerts" {
  name = var.rds_sns_topic
  tags = {
    Name        = var.rds_sns_topic
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

# resource "aws_sns_topic" "elasticache_alerts" {
#   name = var.elasticache_sns_topic
# }

resource "aws_sns_topic" "ecs_alerts" {
  name = var.ecs_sns_topic
  tags = {
    Name        = var.ecs_sns_topic
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic" "keycloak_rds_alerts" {
  name = var.keycloak_rds_sns_topic
  tags = {
    Name        = var.keycloak_rds_sns_topic
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic" "ec2_alerts" {
  name = var.ec2_sns_topic
  tags = {
    Name        = var.ec2_sns_topic
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic" "log_events_alerts" {
  name = "LOG_EVENTS_ALERT_SNS"
  tags = {
    Name        = "LOG_EVENTS_ALERT_SNS"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_lambda_function" "sns_to_google_chat" {
  filename         = var.metrics_alarm_lambda_filename
  function_name    = var.metrics_alarm_lambda_function_name
  role             = aws_iam_role.lambda_execution_role.arn
  handler          = var.metrics_alarm_lambda_handler
  source_code_hash = filebase64sha256(var.metrics_alarm_lambda_filename)
  runtime          = var.metrics_alarm_lambda_python_runtime
  environment {
    variables = {
      GOOGLE_CHAT_WEBHOOK_URL = var.GOOGLE_CHAT_WEBHOOK_URL
    }
  }

  tags = {
    Name        = var.metrics_alarm_lambda_function_name
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_iam_role" "lambda_execution_role" {
  name = "lambda_execution_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = "sts:AssumeRole",
      Effect = "Allow",
      Principal = {
        Service = "lambda.amazonaws.com",
      },
    }],
  })
}

resource "aws_iam_role_policy_attachment" "lambda_execution_policy" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = var.lambda_policy_arn
}

resource "aws_sns_topic_subscription" "rds_alerts_subscription" {
  topic_arn = aws_sns_topic.rds_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.sns_to_google_chat.arn
}

resource "aws_lambda_permission" "allow_sns_rds" {
  statement_id  = "AllowExecutionFromSNSRDS"
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.sns_to_google_chat.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.rds_alerts.arn
}

resource "aws_sns_topic_subscription" "ecs_alerts_subscription" {
  topic_arn = aws_sns_topic.ecs_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.sns_to_google_chat.arn
}

resource "aws_lambda_permission" "allow_sns_ecs" {
  statement_id  = "AllowExecutionFromSNSECS"
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.sns_to_google_chat.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.ecs_alerts.arn
}

resource "aws_sns_topic_subscription" "keycloak_rds_alerts_subscription" {
  topic_arn = aws_sns_topic.keycloak_rds_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.sns_to_google_chat.arn
}

resource "aws_lambda_permission" "allow_sns_keycloak_rds" {
  statement_id  = "AllowExecutionFromSNSKEYCLOAKRDS"
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.sns_to_google_chat.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.keycloak_rds_alerts.arn
}

resource "aws_sns_topic_subscription" "ec2_alerts_subscription" {
  topic_arn = aws_sns_topic.ec2_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.sns_to_google_chat.arn
}

resource "aws_lambda_permission" "allow_sns_ec2" {
  statement_id  = "AllowExecutionFromSNSEC2"
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.sns_to_google_chat.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.ec2_alerts.arn
}

resource "aws_sns_topic_subscription" "log_events_alerts_subscription" {
  topic_arn = aws_sns_topic.log_events_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.sns_to_google_chat.arn
}

resource "aws_lambda_permission" "allow_sns_log_events" {
  statement_id  = "AllowExecutionFromSNSLOGEVENTS"
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.sns_to_google_chat.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.log_events_alerts.arn
}

resource "aws_sns_topic" "alb_alerts" {
  name = "ALB_ALERT_SNS"
  tags = {
    Name        = "ALB_ALERT_SNS"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic_subscription" "alb_alerts_subscription" {
  topic_arn = aws_sns_topic.alb_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.sns_to_google_chat.arn
}

resource "aws_lambda_permission" "allow_sns_alb" {
  statement_id  = "AllowExecutionFromSNSALB"
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.sns_to_google_chat.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.alb_alerts.arn
}

resource "aws_sns_topic" "cloudfront_alerts" {
  name = "ALB_CLOUDFRONT_SNS"
  tags = {
    Name        = "ALB_CLOUDFRONT_SNS"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_sns_topic_subscription" "cloudfront_alerts_subscription" {
  topic_arn = aws_sns_topic.cloudfront_alerts.arn
  protocol  = var.sns_topic_subscription_protocol
  endpoint  = aws_lambda_function.sns_to_google_chat.arn
}

resource "aws_lambda_permission" "allow_sns_cloudfront" {
  statement_id  = "AllowExecutionFromSNSCloudfront"
  action        = var.lambda_permission_action
  function_name = aws_lambda_function.sns_to_google_chat.function_name
  principal     = var.lambda_permission_principal
  source_arn    = aws_sns_topic.cloudfront_alerts.arn
}
