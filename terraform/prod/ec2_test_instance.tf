data "aws_ami" "rohit_dombi_testbox_ami" {
  most_recent = true
  owners      = ["amazon"]
  filter {
    name   = "architecture"
    values = ["arm64"]
  }
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*"]
  }
}

variable "rohit_dombi_instance_tags" {
  type = map(string)
  default = {
    Name        = "rohit-dombi-test-instance"
    Environment = "test"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_security_group" "rohit_dombi_testbox_sg" {
  description = "SG for test rohit_dombi test box"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  name   = "rohit-dombi-testbox-sg"
  vpc_id = module.vpc_main.vpc_id
  tags = {
    Name        = "rohit-dombi-testbox-sg"
    Environment = "test"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}


resource "aws_instance" "rohit_dombi_dev_box" {
  ami                         = "ami-029aef112d0ed08ae"
  associate_public_ip_address = false
  availability_zone           = "ap-south-1c"
  key_name                    = "rohit_dombi_test_instance_key"

  disable_api_stop        = "false"
  disable_api_termination = "false"
  ebs_optimized           = "false"
  subnet_id               = module.main_private_subnet_3.subnet_id


  get_password_data                    = "false"
  hibernation                          = "false"
  instance_initiated_shutdown_behavior = "stop"
  instance_type                        = "t4g.xlarge"
  ipv6_address_count                   = 0

  vpc_security_group_ids = ["${aws_security_group.rohit_dombi_testbox_sg.id}"]

  maintenance_options {
    auto_recovery = "default"
  }

  metadata_options {
    http_endpoint               = "enabled"
    http_protocol_ipv6          = "disabled"
    http_put_response_hop_limit = 2
    http_tokens                 = "required"
    instance_metadata_tags      = "disabled"
  }

  monitoring                 = false
  placement_partition_number = 0

  private_dns_name_options {
    enable_resource_name_dns_a_record    = false
    enable_resource_name_dns_aaaa_record = false
    hostname_type                        = "ip-name"
  }

  root_block_device {
    delete_on_termination = "false"
    encrypted             = "true"
    kms_key_id            = "arn:aws:kms:ap-south-1:771151923073:key/mrk-7b20b72283a94e3a933613ace66acb78"
    iops                  = 3000
    throughput            = 125
    volume_size           = 100
    volume_type           = "gp3"
    tags = merge(var.rohit_dombi_instance_tags,
      { Name = "${var.rohit_dombi_instance_tags["Name"]}-volume" }
    )
  }
  iam_instance_profile = aws_iam_instance_profile.RohitTestInstanceProfile.name
  tags                 = var.rohit_dombi_instance_tags
  tenancy              = "default"
}

resource "aws_iam_role" "RohitTestInstanceRole" {
  name = "RohitTestInstanceRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "RohitTestInstancePolicy" {
  name        = "RohitTestInstancePolicy"
  description = "Policy for EC2 Test Instace with necessary permissions"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "rds:Describe*",
          "rds:ListTagsForResource",
          "ec2:DescribeAccountAttributes",
          "ec2:DescribeAvailabilityZones",
          "ec2:DescribeInternetGateways",
          "ec2:DescribeSecurityGroups",
          "ec2:DescribeSubnets",
          "ec2:DescribeVpcAttribute",
          "ec2:DescribeVpcs",
          "cloudwatch:GetMetricStatistics",
          "cloudwatch:ListMetrics",
          "cloudwatch:GetMetricData",
          "logs:DescribeLogStreams",
          "logs:GetLogEvents",
          "devops-guru:GetResourceCollection",
          "devops-guru:SearchInsights",
          "devops-guru:ListAnomaliesForInsight",
          "s3:Get*",
          "s3:List*",
          "s3:Describe*",
          "s3-object-lambda:Get*",
          "s3-object-lambda:List*",
          "kms:*",
          "bedrock:invokeModel"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "RohitTestInstancePolicyAttachment" {
  role       = aws_iam_role.RohitTestInstanceRole.name
  policy_arn = aws_iam_policy.RohitTestInstancePolicy.arn
}

resource "aws_iam_instance_profile" "RohitTestInstanceProfile" {
  name = "RohitTestInstanceProfile"
  role = aws_iam_role.RohitTestInstanceRole.name
}
