module "bff_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.bff_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.ecs_ravenclaw_bff_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.bff_service_name
  desired_count                  = var.bff_service_desired_count
  task_definition_arn            = module.bff_task_definition.arn
  target_group_arn               = aws_lb_target_group.main.arn
  container_name                 = "bff"
  container_port                 = 8080

  service_discovery_svc_tags = var.bff_service_discovery_tags

  ecs_service_tags = var.bff_service_tags
}

module "gateway_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.gateway_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.gateway_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.gateway_service_name
  desired_count                  = var.gateway_service_desired_count
  task_definition_arn            = module.gateway_task_definition.arn
  target_group_arn               = aws_lb_target_group.gateway.arn
  container_name                 = "gateway"
  container_port                 = 8081
  enable_execute_command         = true

  service_discovery_svc_tags = var.gateway_service_discovery_tags

  ecs_service_tags = var.gateway_service_tags
}

module "remediator_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.remediator_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.remediator_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.remediator_service_name
  desired_count                  = var.remediator_service_desired_count
  task_definition_arn            = module.remediator_task_definition.arn

  service_discovery_svc_tags = var.remediator_service_discovery_tags

  ecs_service_tags = var.remediator_service_tags
}

module "ml_inference_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.ml_inference_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.ml_inference_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.ml_inference_service_name
  desired_count                  = var.ml_inference_service_desired_count
  task_definition_arn            = module.ml_inference_task_definition.arn
  target_group_arn               = aws_lb_target_group.internal_ml_inference_service_alb.arn
  container_name                 = "ml-inference"
  container_port                 = 8000


  service_discovery_svc_tags = var.ml_inference_service_discovery_tags

  ecs_service_tags = var.ml_inference_service_tags
}

module "ti_go_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.ti_go_service_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.ravenclaw_ti_go_service.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.ti_go_service_service_name
  desired_count                  = var.ti_go_service_service_desired_count
  task_definition_arn            = module.ti_go_service_task_definition.arn

  service_discovery_svc_tags = var.ti_go_service_discovery_tags

  ecs_service_tags = var.ti_go_service_tags
}

module "ml_inference_ingestion_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = "ml-inference-ingestion"
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.ml_inference_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = "ml-inference-ingestion-service"
  desired_count                  = 8
  task_definition_arn            = module.ml_inference_ingestion_task_definition.arn
  target_group_arn               = aws_lb_target_group.internal_ml_inference_ingestion.arn
  container_name                 = "ml-inference"
  container_port                 = 8000

  service_discovery_svc_tags = {
    Name        = "ml-inference-ingestion-svc"
    Environment = "prod"
    Team        = "infra"
    Application = "ml_python"
  }

  ecs_service_tags = {
    Name        = "ml-inference-ingestion-svc"
    Environment = "prod"
    Team        = "infra"
    Application = "ml_python"
  }
}

module "inline_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = "inline"
  cluster                        = module.ecs_cluster.ecs_cluster_arn
  security_groups_id             = [aws_security_group.gosmtp_service_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = "inline"
  desired_count                  = 1
  task_definition_arn            = module.inline_task_definition.arn
  availability_zone_rebalancing  = "ENABLED"

  target_group_arn = aws_lb_target_group.gosmtp.arn
  container_name   = "gateway-inline-prod"
  container_port   = 2525

  service_discovery_svc_tags = {
    Name        = "inline-prod-service-sd"
    Team        = "infra"
    Product     = "inline"
    Environment = "prod"
  }

  ecs_service_tags = {
    Name        = "inline-prod-service"
    Team        = "infra"
    Product     = "inline"
    Environment = "prod"
  }
}
