resource "aws_cloudwatch_metric_alarm" "inline_nlb_zonal_health_status" {
  alarm_name          = "INLINE_NLB_ZONAL_UNHEALTHY_STATUS"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 1
  metric_name         = "ZonalHealthStatus"
  namespace           = "AWS/NetworkELB"
  period              = 300
  statistic           = "Minimum"
  threshold           = 1
  alarm_description   = "Alarm when Inline NLB Zonal Health Status is Unhealthy in one of the Availability Zone in last 5 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "net/gosmtp-server-nlb/902078144bae815f"
  }

  tags = {
    Name        = "INLINE_NLB_ZONAL_UNHEALTHY_STATUS"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "inline"
  }
}

resource "aws_cloudwatch_metric_alarm" "inline_nlb_client_tls_negotiation_error_count" {
  alarm_name          = "INLINE_NLB_CLIENT_TLS_NEGOTIATION_ERROR_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "ClientTLSNegotiationErrorCount"
  namespace           = "AWS/NetworkELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 10
  alarm_description   = "Alarm when Inine NLB Client TLS Negotiation Error Count is more than 10 in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "net/gosmtp-server-nlb/902078144bae815f"
  }

  tags = {
    Name        = "INLINE_NLB_CLIENT_TLS_NEGOTIATION_ERROR_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "inline"
  }
}

resource "aws_cloudwatch_metric_alarm" "inline_nlb_rejected_flow_count" {
  alarm_name          = "INLINE_NLB_REJECTED_FLOW_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "RejectedFlowCount"
  namespace           = "AWS/NetworkELB"
  period              = 300
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "Alarm when Inline NLB has more than 5 rejected flow count in last 5 minute"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "net/gosmtp-server-nlb/902078144bae815f"
    TargetGroup  = "targetgroup/gosmtp-server-tg-2525/c7929179d86e9010"
  }

  tags = {
    Name        = "INLINE_NLB_REJECTED_FLOW_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "inline"
  }
}

resource "aws_cloudwatch_metric_alarm" "inline_nlb_target_tls_negotiation_error_count" {
  alarm_name          = "INLINE_NLB_TARGET_TLS_NEGOTIATION_ERROR_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "TargetTLSNegotiationErrorCount"
  namespace           = "AWS/NetworkELB"
  period              = 600
  statistic           = "Sum"
  threshold           = 10
  alarm_description   = "Alarm when Inline NLB Target TLS Negotiation Error Count is equal to or more than 10 times in last 10 minutes"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "net/gosmtp-server-nlb/902078144bae815f"
  }

  tags = {
    Name        = "INLINE_NLB_TARGET_TLS_NEGOTIATION_ERROR_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "inline"
  }
}

resource "aws_cloudwatch_metric_alarm" "inline_nlb_target_grp_unhealthy_host_count" {
  alarm_name          = "INLINE_NLB_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/NetworkELB"
  period              = 300
  statistic           = "Minimum"
  threshold           = 1
  alarm_description   = "Alarm when Inline NLB Target Group - Unhealthy host count is at least 1"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alb_alerts.arn]
  dimensions = {
    LoadBalancer = "net/gosmtp-server-nlb/902078144bae815f"
    TargetGroup  = "targetgroup/gosmtp-server-tg-2525/c7929179d86e9010"
  }

  tags = {
    Name        = "INLINE_NLB_TARGET_GROUP_UNHEALTHY_HOST_COUNT"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "inline"
  }
}
