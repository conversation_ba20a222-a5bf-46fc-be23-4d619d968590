# IAM Roles
# Updated IAM Role for CloudTrail to send logs to CloudWatch
resource "aws_iam_role" "cloudtrail_role" {
  name = "ProdCloudtrailToCloudwatchRole"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudtrail.amazonaws.com"
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })
}


# Policy for CloudTrail Role to access CloudWatch Logs and S3 Bucket
resource "aws_iam_policy" "cloudtrail_policy" {
  name = "ProdCloudtrailPolicy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        "Resource" : "${aws_cloudwatch_log_group.cloudtrail.arn}:*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:GetBucketAcl",
          "s3:PutObject",
          "s3:GetObject",
        ],
        "Resource" : "*"
      },
      {
        "Sid" : "AllowCloudTrailProdKMSAccess",
        "Effect" : "Allow",
        "Action" : [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        "Resource" : "*"
      }
    ]
  })
}

# Attach the policy to the CloudTrail role
resource "aws_iam_role_policy_attachment" "cloudtrail_policy_attachment" {
  role       = aws_iam_role.cloudtrail_role.name
  policy_arn = aws_iam_policy.cloudtrail_policy.arn
}

##########################################################
### S3
resource "aws_s3_bucket" "cloudtrail_s3" {
  bucket        = "cloudtrail-prod-trail"
  force_destroy = false

  tags = {
    Name        = "cloudtrail-s3-prod-bucket"
    Environment = "prod"
    Product     = "AWS"
    Team        = "infra"
  }
}

resource "aws_s3_bucket_versioning" "cloudtrail_s3" {
  bucket = aws_s3_bucket.cloudtrail_s3.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "cloudtrail_s3" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.cloudtrail_s3]

  bucket = aws_s3_bucket.cloudtrail_s3.id

  rule {
    id = "lifecycle_configuration"

    noncurrent_version_expiration {
      noncurrent_days = 180
    }

    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "cloudtrail_s3" {
  bucket = aws_s3_bucket.cloudtrail_s3.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "cloudtrail_s3" {
  depends_on = [aws_s3_bucket_ownership_controls.cloudtrail_s3]

  bucket = aws_s3_bucket.cloudtrail_s3.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "cloudtrail_s3" {
  bucket = aws_s3_bucket.cloudtrail_s3.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

data "aws_iam_policy_document" "cloudtrail_s3_policy" {
  statement {
    sid    = "AWSProdCloudTrailAclCheck"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["cloudtrail.amazonaws.com"]
    }

    actions   = ["s3:GetBucketAcl", "s3:GetBucketPolicy"]
    resources = [aws_s3_bucket.cloudtrail_s3.arn]
  }

  statement {
    sid    = "AWSProdCloudTrailWrite"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["cloudtrail.amazonaws.com"]
    }

    actions   = ["s3:PutObject"]
    resources = ["${aws_s3_bucket.cloudtrail_s3.arn}/prod/AWSLogs/*"]

    condition {
      test     = "StringEquals"
      variable = "s3:x-amz-acl"
      values   = ["bucket-owner-full-control"]
    }
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "cloudtrail_s3" {
  bucket = aws_s3_bucket.cloudtrail_s3.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_policy" "cloudtrail_s3_policy" {
  bucket = aws_s3_bucket.cloudtrail_s3.id
  policy = data.aws_iam_policy_document.cloudtrail_s3_policy.json
}

#########################################################
### Cloudtrail
resource "aws_cloudtrail" "main" {
  depends_on = [aws_s3_bucket_policy.cloudtrail_s3_policy]

  name                          = "cloudtrail-prod"
  s3_bucket_name                = aws_s3_bucket.cloudtrail_s3.id
  s3_key_prefix                 = "prod"
  include_global_service_events = true

  # advanced_event_selector {
  #   name = "S3"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::S3::Object",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "lambda"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::Lambda::Function",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "cloudmap-ns"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::ServiceDiscovery::Namespace",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "cloudmap-svc"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::ServiceDiscovery::Service",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "cloudfront-kv"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::CloudFront::KeyValueStore",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "cloudwatch-metric"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::CloudWatch::Metric",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "s3-access-pt"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::S3::AccessPoint",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "sns-topic"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::SNS::Topic",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "sys-manager"
  #
  #   field_selector {
  #
  #     equals = [
  #       "AWS::SSMMessages::ControlChannel",
  #     ]
  #     field = "resources.type"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "Data",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "ec2"
  #
  #   field_selector {
  #
  #     equals = [
  #       "NetworkActivity",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "ec2.amazonaws.com",
  #     ]
  #     field = "eventSource"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "kms"
  #
  #   field_selector {
  #
  #     equals = [
  #       "NetworkActivity",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "kms.amazonaws.com",
  #     ]
  #     field = "eventSource"
  #
  #   }
  # }
  # advanced_event_selector {
  #   name = "secretsmanager"
  #
  #   field_selector {
  #
  #     equals = [
  #       "NetworkActivity",
  #     ]
  #     field = "eventCategory"
  #
  #   }
  #   field_selector {
  #
  #     equals = [
  #       "secretsmanager.amazonaws.com",
  #     ]
  #     field = "eventSource"
  #
  #   }
  # }
  advanced_event_selector {
    name = "Management events selector"

    field_selector {

      equals = [
        "Management",
      ]
      field = "eventCategory"

    }
  }

  cloud_watch_logs_group_arn = "${aws_cloudwatch_log_group.cloudtrail.arn}:*" # CloudTrail requires the Log Stream wildcard
  cloud_watch_logs_role_arn  = aws_iam_role.cloudtrail_role.arn
  enable_logging             = true
  enable_log_file_validation = true

  insight_selector {
    insight_type = "ApiCallRateInsight"
  }

  insight_selector {
    insight_type = "ApiErrorRateInsight"
  }

  is_organization_trail = true

  tags = {
    Name        = "cloudtrail-prod"
    Environment = "prod"
    Team        = "infra"
    Product     = "AWS"
  }
}

data "aws_caller_identity" "current" {}

data "aws_partition" "current" {}

data "aws_region" "current" {}
