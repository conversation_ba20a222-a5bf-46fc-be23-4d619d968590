module "ml_inference_service_internal_alb_sg" {
  source      = "../modules/security_groups"
  region      = var.region
  name        = "ml-inference-service-internal-alb-sg"
  description = "For ML Inference Internal ALB"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "ml-inference-service-internal-alb-sg"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }

  # ───────────────────────────────────────────────────────────
  # Ingress: HTTPS (443) from VPC only
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 443
  ingress_to_port                       = 443
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  # ───────────────────────────────────────────────────────────
  # Custom ingress: HTTP (80) from VPC only
  # ───────────────────────────────────────────────────────────
  ingress_custom_rules = [
    {
      cidr_ipv4   = [var.vpc_cidr_block]
      cidr_ipv6   = []
      from_port   = 80
      to_port     = 80
      ip_protocol = "tcp"
      description = ""
    }
  ]

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}


resource "aws_lb" "internal_ml_inference_service_alb" {
  name               = "alb-ml-inference-internal"
  internal           = "true"
  load_balancer_type = "application"
  security_groups    = [module.ml_inference_service_internal_alb_sg.security_group_id]
  subnets            = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]

  enable_deletion_protection = "true"
  idle_timeout               = 360

  access_logs {
    bucket  = aws_s3_bucket.logs.bucket
    prefix  = "alb/ml-inference-internal/access-logs"
    enabled = true
  }

  connection_logs {
    bucket  = aws_s3_bucket.logs.id
    prefix  = "alb/ml-inference-internal/connection-logs"
    enabled = true
  }

  tags = {
    Name        = "alb-ml-inference-internal"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_lb_target_group" "internal_ml_inference_service_alb" {
  name                          = "ml-inference-target-group"
  target_type                   = "ip"
  port                          = 8000
  protocol                      = "HTTP"
  vpc_id                        = module.vpc_main.vpc_id
  load_balancing_algorithm_type = "least_outstanding_requests"
  tags = {
    Name        = "ml-inference-target-group"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
  health_check {
    path                = "/health"
    healthy_threshold   = 2
    interval            = 180
    unhealthy_threshold = 5
  }
}

resource "aws_lb_listener" "internal_ml_inference_service_alb" {
  load_balancer_arn = aws_lb.internal_ml_inference_service_alb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "Either endpoint or IP may not be valid"
      status_code  = "200"
    }
  }

  tags = {
    Name        = "ml-inference-service-alb-listener"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_lb_listener_rule" "internal_ml_inference_service_alb" {
  listener_arn = aws_lb_listener.internal_ml_inference_service_alb.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.internal_ml_inference_service_alb.arn
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }

  tags = {
    Name        = "internal-ml-inference-service-listener-rule"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}
