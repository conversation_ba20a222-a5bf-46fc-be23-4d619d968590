variable "analytics_exporter_image_uri" {
  description = "The image URI for the analytics exporter"
  type        = string
}

variable "analytics_exporter_cpu" {
  description = "The CPU units for the analytics exporter task"
  type        = number
  default     = 1024
}

variable "analytics_exporter_memory" {
  description = "The memory in MiB for the analytics exporter task"
  type        = number
  default     = 2048
}

resource "aws_security_group" "analytics_exporter_sg" {
  description = "analytics exporter sg"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  name = "analytics-exporter-sg"
  tags = {
    name        = "analytics-exporter-sg"
    environment = "prod"
    product     = "ravenclaw"
    team        = "infra"
  }
  vpc_id = module.vpc_main.vpc_id
}

module "analytics_exporter_task_definition" {
  source          = "../modules/ecs_task_definitions"
  region          = var.region
  volume_name     = "analytics-exporter-logs"
  file_system_id  = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id
  container_definitions_json = jsonencode([
    {
      "cpu" : 0,
      "command" : [
        "exporter"
      ],
      "secrets" : [
        {
          "name" : "PG_PASSWORD",
          "valueFrom" : aws_secretsmanager_secret.ANALYTICS_EXPORTER_RDS_PASSWORD.arn
        },
        {
          "name" : "CLICKHOUSE_PASSWORD",
          "valueFrom" : aws_secretsmanager_secret.ANALYTICS_EXPORTER_CLICKHOUSE_PASSWORD.arn
        },
        {
          "name" : "CLICKHOUSE_USER",
          "valueFrom" : aws_secretsmanager_secret.ANALYTICS_EXPORTER_CLICKHOUSE_USER.arn
        },
        {
          "name" : "PG_USERNAME",
          "valueFrom" : aws_secretsmanager_secret.ANALYTICS_EXPORTER_RDS_USER.arn
        }
      ],
      "environment" : [{
        "name" : "LOG_ROTATION_FILE",
        "value" : "analytics-exporter.log"
        }, {
        "name" : "CLICKHOUSE_HOST",
        "value" : "clickhouse.ravenclaw-ns:9000"
        }, {
        "name" : "CLICKHOUSE_TLS_ENABLE",
        "value" : "false"
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_SSL",
        "value" : var.PG_SSL
        }, {
        "name" : "AWS_REGION",
        "value" : var.region
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "PG_MAX_OPEN_CONNECTION",
        "value" : var.PG_MAX_OPEN_CONNECTION
        }, {
        "name" : "PG_MAX_IDLE_CONNECTION",
        "value" : var.PG_MAX_IDLE_CONNECTION
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.s3-orgs.arn
        }, {
        "name" : "CLICKHOUSE_DB",
        "value" : "analytics"
      }],

      "environmentFiles" : [],
      "essential" : true,
      "image" : var.analytics_exporter_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : "analytics-exporter-logs",
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "secretOptions" : [],
      "mountPoints" : [{
        "sourceVolume" : "analytics-exporter-logs",
        "containerPath" : "/root/ravenclaw",
        "readOnly" : false
      }],
      "name" : "analytics-exporter",
      "portMappings" : [{
        "appProtocol" : "http",
        "containerPort" : 80,
        "hostPort" : 80,
        "name" : "http",
        "protocol" : "tcp"
      }],
      "systemControls" : [],
      "volumesFrom" : [],
      "ulimits" : []
  }])

  cpu                = var.analytics_exporter_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "analytics-exporter"
  memory             = var.analytics_exporter_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = {
    Name        = "analytics-exporter"
    Environment = "prod"
    Team        = "ravenclaw"
    Project     = "analytics"
  }
}

