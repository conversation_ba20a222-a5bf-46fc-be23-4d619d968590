resource "aws_security_group" "internal_alb_loadbalancer_sg" {
  description = "For Internal ALB"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  name = "internal_alb_loadbalancer_sg"
  tags = {
    Name        = "internal-alb-sg"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_lb" "internal" {
  name               = "prod-alb"
  internal           = "true"
  load_balancer_type = "application"
  security_groups    = [aws_security_group.internal_alb_loadbalancer_sg.id]
  subnets            = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]

  enable_deletion_protection = "true"
  access_logs {
    bucket  = aws_s3_bucket.logs.bucket
    prefix  = "alb/internal/access-logs"
    enabled = true
  }

  connection_logs {
    bucket  = aws_s3_bucket.logs.id
    prefix  = "alb/internal/connection-logs"
    enabled = true
  }



  tags = {
    Name        = "internal-alb"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_lb_target_group" "internal_ml_inference_ingestion" {
  name                          = "ml-ingestion-target-group"
  target_type                   = "ip"
  port                          = 8000
  protocol                      = "HTTP"
  vpc_id                        = module.vpc_main.vpc_id
  load_balancing_algorithm_type = "least_outstanding_requests"
  tags = {
    Name        = "ml-inference-ingestion-target-group"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
  health_check {
    path = "/health"
  }
}

resource "aws_lb_listener" "internal_ml_inference_ingestion" {
  load_balancer_arn = aws_lb.internal.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "Either endpoint or IP may not be valid"
      status_code  = "200"
    }
  }

  tags = {
    Name        = "ml-inference-ingestion-alb-listener"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_lb_listener_rule" "internal_ml_inference_ingestion" {
  listener_arn = aws_lb_listener.internal_ml_inference_ingestion.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.internal_ml_inference_ingestion.arn
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }

  tags = {
    Name        = "internal-ml-inference-ingestion-listener-rule"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}


