module "vpc_main" {
  source     = "../modules/vpc"
  region     = var.region
  cidr_block = var.vpc_cidr_block

  tags = var.vpc_tags
}

## Logging
# resource "aws_iam_role" "vpc_flow_logs_role" {
#   name = "vpc-flow-logs-role"

#   assume_role_policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Action = "sts:AssumeRole"
#         Effect = "Allow"
#         Principal = {
#           Service = "vpc-flow-logs.amazonaws.com"
#         }
#       }
#     ]
#   })
# }

# resource "aws_iam_role_policy" "vpc_flow_logs_policy" {
#   name = "vpc-flow-logs-policy"
#   role = aws_iam_role.vpc_flow_logs_role.id

#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Action = [
#           "s3:PutObject",
#           "s3:GetObject",
#           "s3:ListBucket"
#         ]
#         Effect   = "Allow"
#         Resource = [
#           "*"
#         ]
#       },
#       {
#         Action = [
#           "logs:CreateLogDelivery",
#           "logs:DeleteLogDelivery",
#           "ecs:List*",
#         ],
#         Resource = "*"
#       }
#     ]
#   })
# }

resource "aws_flow_log" "main" {
  log_destination      = "${aws_s3_bucket.logs.arn}/vpc/flowlogs"
  log_destination_type = "s3"
  traffic_type         = "ALL"
  vpc_id               = module.vpc_main.vpc_id
  log_format           = "$${version} $${account-id} $${interface-id} $${srcaddr} $${dstaddr} $${srcport} $${dstport} $${protocol} $${packets} $${bytes} $${start} $${end} $${action} $${log-status} $${vpc-id} $${subnet-id} $${instance-id} $${tcp-flags} $${type} $${pkt-srcaddr} $${pkt-dstaddr} $${region} $${az-id} $${sublocation-type} $${sublocation-id} $${pkt-src-aws-service} $${pkt-dst-aws-service} $${flow-direction} $${traffic-path} $${ecs-cluster-arn} $${ecs-cluster-name} $${ecs-container-instance-arn} $${ecs-container-instance-id} $${ecs-container-id} $${ecs-second-container-id} $${ecs-service-name} $${ecs-task-definition-arn} $${ecs-task-arn} $${ecs-task-id} $${reject-reason}"

  tags = {
    Name        = "vpc-flow-logs"
    Team        = "infra"
    Product     = "compliance"
    Environment = "prod"
  }
}


## VPC endpoint
resource "aws_vpc_endpoint" "s3_vpc_ep_gateway" {
  vpc_id            = module.vpc_main.vpc_id
  vpc_endpoint_type = "Gateway"
  service_name      = "com.amazonaws.${var.region}.s3"
  route_table_ids = [
    aws_route_table.rt_private_1.id,
    aws_route_table.rt_private_2.id,
    aws_route_table.rt_private_3.id
  ]
  tags = {
    Name        = "ravenclaw-s3-vpc-endpoint"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_vpc_endpoint" "cloudwatch" {
  vpc_id              = module.vpc_main.vpc_id
  service_name        = "com.amazonaws.${var.region}.logs"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  private_dns_enabled = true
  security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

  tags = {
    Name        = "ravenclaw-cloudwatch-vpc-endpoint"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_vpc_endpoint" "ecr-dkr" {
  vpc_id              = module.vpc_main.vpc_id
  service_name        = "com.amazonaws.${var.region}.ecr.dkr"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  private_dns_enabled = true
  security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

  tags = {
    Name        = "ravenclaw-ecr-dkr-vpc-endpoint"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_vpc_endpoint" "kms" {
  vpc_id              = module.vpc_main.vpc_id
  service_name        = "com.amazonaws.${var.region}.kms"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  private_dns_enabled = true
  security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

  tags = {
    Name        = "ravenclaw-kms-vpc-endpoint"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_vpc_endpoint" "secretsmanager" {
  vpc_id              = module.vpc_main.vpc_id
  service_name        = "com.amazonaws.${var.region}.secretsmanager"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  private_dns_enabled = true
  security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

  tags = {
    Name        = "ravenclaw-secretsmanager-vpc-endpoint"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

resource "aws_vpc_endpoint" "cloudtrail" {
  vpc_id              = module.vpc_main.vpc_id
  service_name        = "com.amazonaws.${var.region}.cloudtrail"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  private_dns_enabled = true
  security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

  tags = {
    Name        = "ravenclaw-cloudtrail-vpc-endpoint"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}
