######################################### ALB WAF ##################################
resource "aws_wafv2_web_acl" "ravenclaw_alb" {
  name        = "web-acl-ravenclaw-alb"
  description = "WAF Web ACL for Ravenclaw ALB with OWASP Top 10 and DDoS protection"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  rule {
    name     = "AllowInternalVPC"
    priority = 1
    action {
      allow {}
    }
    statement {
      ip_set_reference_statement {
        arn = aws_wafv2_ip_set.internal_vpc_ips.arn
      }
    }
    visibility_config {
      sampled_requests_enabled   = true
      cloudwatch_metrics_enabled = true
      metric_name                = "AllowInternalVPC"
    }
  }


  # Rule 1: AWS Managed OWASP core rule set
  rule {
    name     = "AWSManagedRulesCommonRuleSet"
    priority = 21
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
        rule_action_override {
          name = "NoUserAgent_HEADER"
          action_to_use {
            allow {}
          }
        }
      }

    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesCommonRuleSetMetric"
      sampled_requests_enabled   = true
    }
  }

  # Rule 2: SQL Injection Protection
  rule {
    name     = "AWSManagedRulesSQLiRuleSet"
    priority = 31
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesSQLiRuleSetMetric"
      sampled_requests_enabled   = true
    }
  }

  # Rule 3: Rate Limiting for DDoS Protection
  rule {
    name     = "RateLimitRule"
    priority = 41
    action {
      block {}
    }
    statement {
      rate_based_statement {
        limit              = 5000
        aggregate_key_type = "IP"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimitRuleMetric"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "BlockMaliciousIPs"
    priority = 51
    action {
      block {}
    }
    statement {
      ip_set_reference_statement {
        arn = aws_wafv2_ip_set.blocked_ips.arn
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "BlockMaliciousIPsMetric"
      sampled_requests_enabled   = true
    }
  }

  # rule {
  #   name     = "AllowHooksEndpoint"
  #   priority = 55

  #   action {
  #     allow {}
  #   }

  #   statement {
  #     byte_match_statement {
  #       search_string = "/v0/hooks/"
  #       field_to_match {
  #         uri_path {}
  #       }
  #       positional_constraint = "STARTS_WITH"
  #       text_transformation {
  #         priority = 0
  #         type     = "NONE"
  #       }
  #     }
  #   }

  #   visibility_config {
  #     cloudwatch_metrics_enabled = true
  #     metric_name                = "AllowHooksRule"
  #     sampled_requests_enabled   = true
  #   }
  # }

  rule {
    name     = "AWSManagedRulesBotControlRuleSet"
    priority = 61
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesBotControlRuleSet"
        vendor_name = "AWS"
        managed_rule_group_configs {
          aws_managed_rules_bot_control_rule_set {
            inspection_level        = "COMMON"
            enable_machine_learning = false
          }
        }
        rule_action_override {
          name = "CategoryHttpLibrary"
          action_to_use {
            allow {}
          }
        }

        rule_action_override {
          name = "CategoryMiscellaneous"
          action_to_use {
            count {}
          }
        }
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesBotControlRuleSetMetric"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "CustomGoogleBotRule"
    priority = 65 # Priority between BotControl (61) and IP Reputation (71)

    action {
      block {}
    }

    statement {
      and_statement {
        statement {
          label_match_statement {
            scope = "LABEL"
            key   = "awswaf:managed:aws:bot-control:bot:name:google_user_triggered_fetcher"
          }
        }
        statement {
          not_statement {
            statement {
              byte_match_statement {
                search_string = "/v0/hooks/google"
                field_to_match {
                  uri_path {}
                }
                positional_constraint = "STARTS_WITH"
                text_transformation {
                  priority = 1
                  type     = "NONE"
                }
              }
            }
          }
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "CustomGoogleBotRuleMetric"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWSManagedRulesAmazonIpReputationList"
    priority = 71
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      sampled_requests_enabled   = true
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesAmazonIpReputationListMetric"
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "WebACLMetric"
    sampled_requests_enabled   = true
  }

  tags = {
    Name        = "ravenclaw-alb-waf-acl"
    Team        = "infra"
    Product     = "compliance"
    Environment = "prod"
  }
}

resource "aws_wafv2_web_acl_association" "ravenclaw_alb" {
  resource_arn = aws_lb.main.arn
  web_acl_arn  = aws_wafv2_web_acl.ravenclaw_alb.arn
}
