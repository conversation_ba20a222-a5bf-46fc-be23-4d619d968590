resource "aws_iam_role" "task_role" {
  assume_role_policy = <<POLICY
{
        "Statement": [
            {
                "Action": "sts:AssumeRole",
                "Effect": "Allow",
                "Principal": {
                    "Service": "ecs-tasks.amazonaws.com"
                },
                "Sid": ""
            }
        ],
        "Version": "2012-10-17"
}
POLICY

  description = "Allows ECS tasks to call AWS services on your behalf."

  inline_policy {
    name = "ECSRavenclawTaskRole"
    policy = jsonencode(
      {
        Version : "2012-10-17",
        Statement : [
          {
            Action : [
              "kms:Decrypt",
              "kms:Encrypt",
              "kms:GenerateDataKey",
              "kms:ReEncrypt*",
              "kms:DescribeKey",
              "secretsmanager:Batch*",
              "secretsmanager:Describe*",
              "secretsmanager:Get*",
              "secretsmanager:List*",
              "secretsmanager:Validate*",
              "ssm:*",
              "s3:Describe*",
              "s3:Get*",
              "s3:Put*",
              "s3:Restore*",
              "s3:Tag*",
              "s3:List*",
              "s3:Untag*",
              "s3:Update*",
              "s3:Create*",
              "ecr:Get*",
              "ecr:Describe*",
              "ecr:Batch*",
              "ecr:List*",
              "ecr:Validate*",
              "ecs:Create*",
              "ecs:Deregister*",
              "ecs:Describe*",
              "ecs:Execute*",
              "ecs:Get*",
              "ecs:List*",
              "ecs:Put*",
              "ecs:Register*",
              "ecs:Run*",
              "ecs:Start*",
              "ecs:Stop*",
              "ecs:Submit*",
              "ecs:Tag*",
              "ecs:Untag*",
              "ecs:Update*",
              "logs:CreateLogStream",
              "logs:PutLogEvents",
              "ses:Create*",
              "ses:Send*"
              # "ec2:*"
            ],
            Effect : "Allow",
            Resource : "*",
            Sid : "ECSRavenclawTaskRole"
          }
        ]
      }
    )
  }

  max_session_duration = "3600"
  name                 = var.ecs_task_role_name
  path                 = "/"
  tags                 = var.ecs_task_role_iam_tags
}

resource "aws_iam_role" "task_execution_role" {
  assume_role_policy = <<POLICY
        {
            "Statement": [
                {
                    "Action": "sts:AssumeRole",
                    "Effect": "Allow",
                    "Principal": {
                        "Service": "ecs-tasks.amazonaws.com"
                    },
                    "Sid": ""
                }
            ],
            "Version": "2012-10-17"
        }
    POLICY

  description = "Allows ECS tasks to call AWS services on your behalf."

  inline_policy {
    name = "ECSRavenclawTaskExecutionRole"
    policy = jsonencode({
      Version : "2012-10-17",
      Statement : [{
        Action : [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey",
          "secretsmanager:Batch*",
          "secretsmanager:Describe*",
          "secretsmanager:Get*",
          "secretsmanager:List*",
          "secretsmanager:Validate*",
          "ssm:*",
          "s3:Describe*",
          "s3:Get*",
          "s3:Put*",
          "s3:Restore*",
          "s3:Tag*",
          "s3:List*",
          "s3:Untag*",
          "s3:Update*",
          "s3:Create*",
          "ecr:Get*",
          "ecr:Describe*",
          "ecr:Batch*",
          "ecr:List*",
          "ecr:Validate*",
          "ecs:Create*",
          "ecs:Deregister*",
          "ecs:Describe*",
          "ecs:Execute*",
          "ecs:Get*",
          "ecs:List*",
          "ecs:Put*",
          "ecs:Register*",
          "ecs:Run*",
          "ecs:Start*",
          "ecs:Stop*",
          "ecs:Submit*",
          "ecs:Tag*",
          "ecs:Untag*",
          "ecs:Update*",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "ses:Create*",
          "ses:Send*"
          # "ec2:*"
        ],
        Effect : "Allow",
        Resource : "*",
        Sid : "ECSRavenclawTaskExecutionRole"
      }]
    })
  }

  inline_policy {
    name = "ecs-log"
    policy = jsonencode({
      Statement : [{
        Action : [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ],
        Effect : "Allow",
        Resource : [
          "arn:aws:logs:*:*:*"
        ]
        }
      ],
      Version : "2012-10-17"
    })
  }

  managed_policy_arns  = ["arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"]
  max_session_duration = "3600"
  name                 = var.ecs_task_execution_role_name
  path                 = "/"
  tags                 = var.ecs_task_execution_role_iam_tags
}


resource "aws_iam_role" "ec2_role" {
  name = "ec2-prod-role"

  assume_role_policy = <<EOF
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Principal": {
          "Service": "ec2.amazonaws.com"
        },
        "Action": "sts:AssumeRole"
      }
    ]
  }
  EOF
}

resource "aws_iam_policy" "cloudwatch_policy" {
  name        = "cloudwatch-prod-policy"
  description = "Policy for CloudWatch Prod access"

  policy = <<EOF
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Action": [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ],
        "Resource": "*"
      }
    ]
  }
  EOF
}

resource "aws_iam_role_policy_attachment" "ec2_role_policy_attachment" {
  role       = aws_iam_role.ec2_role.name
  policy_arn = aws_iam_policy.cloudwatch_policy.arn
}

resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "ec2-prod-instance-profile"
  role = aws_iam_role.ec2_role.name
}
