module "main_private_subnet_1" {
  source            = "../modules/subnets"
  region            = var.region
  cidr_block        = var.private_subnet_1_cidr_block
  vpc_id            = module.vpc_main.vpc_id
  availability_zone = var.private_subnet_1_cidr_block_az

  tags = var.pvt_subnet_1_tags
}

module "main_private_subnet_2" {
  source            = "../modules/subnets"
  region            = var.region
  cidr_block        = var.private_subnet_2_cidr_block
  vpc_id            = module.vpc_main.vpc_id
  availability_zone = var.private_subnet_2_cidr_block_az
  tags              = var.pvt_subnet_2_tags
}

module "main_private_subnet_3" {
  source            = "../modules/subnets"
  region            = var.region
  cidr_block        = var.private_subnet_3_cidr_block
  vpc_id            = module.vpc_main.vpc_id
  availability_zone = var.private_subnet_3_cidr_block_az
  tags              = var.pvt_subnet_3_tags
}

module "main_public_subnet_1" {
  source            = "../modules/subnets"
  region            = var.region
  cidr_block        = var.public_subnet_1_cidr_block
  vpc_id            = module.vpc_main.vpc_id
  availability_zone = var.public_subnet_1_cidr_block_az
  tags              = var.pub_subnet_1_tags
}

module "main_public_subnet_2" {
  source            = "../modules/subnets"
  region            = var.region
  cidr_block        = var.public_subnet_2_cidr_block
  vpc_id            = module.vpc_main.vpc_id
  availability_zone = var.public_subnet_2_cidr_block_az
  tags              = var.pub_subnet_2_tags
}

module "main_public_subnet_3" {
  source            = "../modules/subnets"
  region            = var.region
  cidr_block        = var.public_subnet_3_cidr_block
  vpc_id            = module.vpc_main.vpc_id
  availability_zone = var.public_subnet_3_cidr_block_az
  tags              = var.pub_subnet_3_tags
}
