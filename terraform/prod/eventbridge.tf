resource "aws_cloudwatch_event_rule" "oom_or_cpu_event_rule" {
  name        = "ecs-task-OOM-or-CPU-stop"
  description = "Alert when an ECS task stops because of OOM kill or CPU exhaustion"
  event_pattern = jsonencode(
    {
      "source" : ["aws.ecs"],
      "detail-type" : ["ECS Task State Change"],
      "detail" : {
        "lastStatus" : ["STOPPED"],
        "$or" : [
          {
            "stoppedReason" : [{
              "wildcard" : "*Error*"
              }, {
              "wildcard" : "*error*"
              }, {
              "wildcard" : "*Failed*"
            }]
          },
          {
            "containers" : {
              "exitCode" : [{
                "anything-but" : [0]
              }]
            }
          }
        ]
      }
    }
  )

  tags = {
    Name        = "oom_or_cpu_event"
    Environment = "prod"
    Team        = "infra"
    Product     = "monitoring"
  }
}

resource "aws_cloudwatch_event_target" "oom_or_cpu_event_target" {
  rule      = aws_cloudwatch_event_rule.oom_or_cpu_event_rule.name
  target_id = "OOMOrCPUEvent"
  arn       = aws_sns_topic.oom_or_cpu_event_sns.arn
}

resource "aws_sns_topic" "oom_or_cpu_event_sns" {
  name = "oom-or-cpu-event-sns"

  tags = {
    Name        = "oom-or-cpu-event-sns"
    Environment = "prod"
    Team        = "infra"
    Product     = "monitoring"
  }
}

resource "aws_sns_topic_policy" "oom_or_cpu_event_policy" {
  arn    = aws_sns_topic.oom_or_cpu_event_sns.arn
  policy = data.aws_iam_policy_document.oom_or_cpu_event_policy_document.json
}

data "aws_iam_policy_document" "oom_or_cpu_event_policy_document" {
  statement {
    effect  = "Allow"
    actions = ["SNS:Publish"]

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }

    resources = [aws_sns_topic.oom_or_cpu_event_sns.arn]
  }
}
