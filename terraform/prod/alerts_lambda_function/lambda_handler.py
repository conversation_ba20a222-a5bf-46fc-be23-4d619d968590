import json
import urllib3
import os
import logging

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

http = urllib3.PoolManager()

def lambda_handler(event, context):
    try:
        webhook_url = os.environ['GOOGLE_CHAT_WEBHOOK_URL']
        logger.info("Webhook URL retrieved from environment variable")
        
        sns_message = event['Records'][0]['Sns']
        logger.info(f"Received SNS message: {sns_message}")
        
        message = {
            'text': f"Alert: {sns_message['Subject']} - {sns_message['Message']}"
        }
        encoded_message = json.dumps(message).encode('utf-8')
        
        response = http.request('POST', webhook_url, body=encoded_message, headers={'Content-Type': 'application/json'})
        logger.info(f"Posted message to Google Chat, response status: {response.status}")
        
        return {
            'statusCode': response.status,
            'body': response.data.decode('utf-8')
        }
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        raise e