import json
import requests
import os
import boto3
import logging
import time
from datetime import datetime
import pytz

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize the CloudWatch Logs client
logs_client = boto3.client('logs')

MAX_GOOGLE_CHAT_MESSAGE_SIZE = 3800

def covert_time(time_str):
    dt_utc = datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%S.%f%z")
    timezone_530 = pytz.timezone('Asia/Kolkata')
    dt_530 = dt_utc.astimezone(timezone_530)
    return dt_530.strftime("%Y-%m-%d %H:%M:%S %Z%z")

def filter_error_logs(log_lines):
    """Filters the log lines to only include lines containing 'error', 'Error', or 'ERROR'."""
    error_keywords = ['error', 'Error', 'ERROR']
    return "\n".join(line for line in log_lines if any(keyword in line for keyword in error_keywords))

def lambda_handler(event, context):
    google_chat_webhook_url = os.environ['GOOGLE_CHAT_WEBHOOK_URL']

    try:
        for record in event['Records']:
            sns_message = json.loads(record['Sns']['Message'])
            alarm_name = sns_message['AlarmName']
            alarm_description = sns_message['AlarmDescription']
            state_change_time = sns_message['StateChangeTime']
            log_group_name = sns_message['Trigger']['Namespace'].split('_')[1]
            region = sns_message['AlarmArn'].split(':')[3]

            if not log_group_name:
                logger.error("Log group name not found in dimensions")
                raise ValueError("Log group name not found in dimensions")

            # Convert the state change time to a timestamp
            alarm_timestamp = datetime.strptime(state_change_time, '%Y-%m-%dT%H:%M:%S.%f%z').timestamp() * 1000
            # Generate log stream link
            log_stream_link = f"https://{region}.console.aws.amazon.com/cloudwatch/home?region={region}#logsV2:log-groups/log-group/{log_group_name.replace('/', '$252F')}/log-events"

            print(log_stream_link)

            # Get log lines from the last 2 minutes
            end_time = alarm_timestamp
            start_time = end_time - (5 * 60 * 1000)
            log_lines = get_relevant_log_lines(log_group_name, start_time, end_time)

            # Format the message for Google Chat
            message = f"*Alarm Triggered: {alarm_name}*\n\n*Description:* {alarm_description}\n\n- *Trigger Reason:* {sns_message['NewStateReason']}\n- *Trigger Time:* {covert_time(sns_message['StateChangeTime'])}\n- *Metric Name:* {sns_message['Trigger']['MetricName']}\n- *Log Group Name:* {log_group_name}\n- *CloudWatch Logs Link:* <{log_stream_link}|Link> \n\n*Recent log lines (last two minutes):*\n```{log_lines}```"

            headers = {
                'Content-Type': 'application/json; charset=UTF-8',
            }

            data = {
                'text': message,
            }

            response = requests.post(google_chat_webhook_url, headers=headers, data=json.dumps(data))


            # TODO: make this piece of code modular
            if response.status_code != 200:
                # taking only the google chat API's character limit
                log_lines_retry = ""
                if len(log_lines) > MAX_GOOGLE_CHAT_MESSAGE_SIZE:
                    log_lines_retry = log_lines[len(log_lines)-MAX_GOOGLE_CHAT_MESSAGE_SIZE:] + '...'
                message = f"*Alarm Triggered: {alarm_name}*\n\n*Description:* {alarm_description}\n\n- *Trigger Reason:* {sns_message['NewStateReason']}\n- *Trigger Time:* {covert_time(sns_message['StateChangeTime'])}\n- *Metric Name:* {sns_message['Trigger']['MetricName']}\n- *Log Group Name:* {log_group_name}\n- *CloudWatch Logs Link:* <{log_stream_link}|Link> \n\n*Error Sending all log lines:*\n```{log_lines_retry}```"
                data = {
                    'text': message,
                }

                responseFirstRetry = requests.post(google_chat_webhook_url, headers=headers, data=json.dumps(data))
                if responseFirstRetry.status_code != 200:
                    message = f"*Alarm Triggered: {alarm_name}*\n\n*Description:* {alarm_description}\n\n- *Trigger Reason:* {sns_message['NewStateReason']}\n- *Trigger Time:* {covert_time(sns_message['StateChangeTime'])}\n- *Metric Name:* {sns_message['Trigger']['MetricName']}\n- *Log Group Name:* {log_group_name}\n- *CloudWatch Logs Link:* <{log_stream_link}|Link> \n\n*Not able to send log lines, please use the above link to see the error logs!!```{responseFirstRetry.text}```"
                    data = {
                        'text': message,
                    }
                    responseSecondRetry = requests.post(google_chat_webhook_url, headers=headers, data=json.dumps(data))

                    if responseSecondRetry.status_code != 200:
                        logger.error("Request to Google Chat returned an error after second retry %d, the response is:\n%s", responseSecondRetry.status_code, responseSecondRetry.text)
                        raise Exception(f"Request to Google Chat returned an error after second retry {responseSecondRetry.status_code}, the response is:\n{responseSecondRetry.text}")

                    logger.error("Request to Google Chat returned an error after first retry %d, the response is:\n%s", responseFirstRetry.status_code, responseFirstRetry.text)
                    raise Exception(f"Request to Google Chat returned an error after first retry {responseFirstRetry.status_code}, the response is:\n{responseFirstRetry.text}")

                logger.error("Request to Google Chat returned an error %d, the response is:\n%s", response.status_code, response.text)
                raise Exception(f"Request to Google Chat returned an error {response.status_code}, the response is:\n{response.text}")

        logger.info("Notification sent to Google Chat")
        return {
            'statusCode': 200,
            'body': json.dumps('Notification sent to Google Chat')
        }

    except Exception as e:
        logger.error("Error processing event: %s", str(e))
        raise e

def get_relevant_log_lines(log_group_name, start_time, end_time):
    log_lines = []
    kwargs = {}

    try:
        while True:
            response = logs_client.filter_log_events(
                logGroupName=log_group_name,
                startTime=int(start_time),
                endTime=int(end_time),
                limit=10000,  # Adjust limit as needed
                **kwargs
            )

            for event in response.get('events', []):
                message = event.get('message', '')
                if "ERROR" in message:
                    log_lines.append(message)

            kwargs['nextToken'] = response.get('nextToken')
            if not kwargs['nextToken']:
                break

    except Exception as e:
        logger.error("Error fetching log events: %s", str(e))

    return "\n".join(log_lines)
