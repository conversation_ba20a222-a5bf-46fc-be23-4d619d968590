# NATS Standalone server
resource "aws_cloudwatch_metric_alarm" "nats_ec2_cpu_utilization" {
  alarm_name          = "NATS_EC2_CPU_UTILIZATION_ALARM"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 180
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Alarm when NATS CPU utilization exceeds 80%"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ec2_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = module.nats.ec2_instance_id
  }

  tags = {
    Name        = "NATS_EC2_CPU_UTILIZATION_ALARM"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

## Memory Utilization Alarm (requires CloudWatch Agent)
resource "aws_cloudwatch_metric_alarm" "memory_utilization_alarm" {
  alarm_name          = "NATS_EC2_MEMORY_UTILIZATION_ALARM"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "mem_used_percent"
  namespace           = "CWAgent"
  period              = 180
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Alarm when NATS EC2 memory utilization exceeds 80%"
  dimensions = {
    InstanceId = module.nats.ec2_instance_id
  }
  actions_enabled = true
  alarm_actions   = [aws_sns_topic.ec2_alerts.arn]

  tags = {
    Name        = "NATS_EC2_MEMORY_UTILIZATION_ALARM"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

# NATS Node 1 server
resource "aws_cloudwatch_metric_alarm" "nats_node_1_ec2_cpu_utilization" {
  alarm_name          = "NATS_NODE_1_EC2_CPU_UTILIZATION_ALARM"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 180
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Alarm when NATS Node 1 CPU utilization exceeds 80%"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ec2_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = module.nats-1.ec2_instance_id
  }

  tags = {
    Name        = "NATS_NODE_1_EC2_CPU_UTILIZATION_ALARM"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

# NATS Node 2 server
resource "aws_cloudwatch_metric_alarm" "nats_node_2_ec2_cpu_utilization" {
  alarm_name          = "NATS_NODE_2_EC2_CPU_UTILIZATION_ALARM"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 180
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Alarm when NATS Node 2 CPU utilization exceeds 80%"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ec2_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = module.nats-2.ec2_instance_id
  }

  tags = {
    Name        = "NATS_NODE_2_EC2_CPU_UTILIZATION_ALARM"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

# NATS Node 3 server
resource "aws_cloudwatch_metric_alarm" "nats_node_3_ec2_cpu_utilization" {
  alarm_name          = "NATS_NODE_3_EC2_CPU_UTILIZATION_ALARM"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 180
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Alarm when NATS Node 3 CPU utilization exceeds 80%"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ec2_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = module.nats-3.ec2_instance_id
  }

  tags = {
    Name        = "NATS_NODE_3_EC2_CPU_UTILIZATION_ALARM"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

# NATS Node 4 server
resource "aws_cloudwatch_metric_alarm" "nats_node_4_ec2_cpu_utilization" {
  alarm_name          = "NATS_NODE_4_EC2_CPU_UTILIZATION_ALARM"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 180
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Alarm when NATS Node 4 CPU utilization exceeds 80%"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ec2_alerts.arn]
  dimensions = {
    DBInstanceIdentifier = module.nats-4.ec2_instance_id
  }

  tags = {
    Name        = "NATS_NODE_4_EC2_CPU_UTILIZATION_ALARM"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}
