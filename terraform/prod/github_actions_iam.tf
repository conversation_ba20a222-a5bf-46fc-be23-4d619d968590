resource "aws_iam_user" "github_actions_user" {
  name = "GithubActionsCIUser"
}

# S3 Read Only Access
resource "aws_iam_policy" "github_actions_s3_read_only_iam_policy" {
  name        = "GithubActionsS3ReadOnlyIAMPolicy"
  path        = "/"
  description = "IAM policy for Github Actions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:Get*",
          "s3:List*",
          "s3:Describe*",
          "s3-object-lambda:Get*",
          "s3-object-lambda:List*"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_user_policy_attachment" "github_actions_s3_read_only_user_policy" {
  user       = aws_iam_user.github_actions_user.name
  policy_arn = aws_iam_policy.github_actions_s3_read_only_iam_policy.arn
}

# ECR Access
resource "aws_iam_policy" "github_actions_ecr_access_iam_policy" {
  name        = "GithubActionsECRAccessIAMPolicy"
  path        = "/"
  description = "IAM policy for Github Actions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:DescribeImages",
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:Get*",
          "ecr:Describe*",
          "ecr:List*",
          "ecr:Batch*",
          "ecr:Validate*"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_user_policy_attachment" "github_actions_ecr_access_user_policy" {
  user       = aws_iam_user.github_actions_user.name
  policy_arn = aws_iam_policy.github_actions_ecr_access_iam_policy.arn
}

# ECR Image Builder
resource "aws_iam_policy" "github_actions_ecr_image_builder_iam_policy" {
  name        = "GithubActionsECRImageBuilderIAMPolicy"
  path        = "/"
  description = "IAM policy for Github Actions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "imagebuilder:GetComponent",
          "imagebuilder:GetContainerRecipe",
          "ecr:GetAuthorizationToken",
          "ecr:BatchGetImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:PutImage"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt"
        ],
        Resource = "*",
        "Condition" : {
          "ForAnyValue:StringEquals" : {
            "kms:EncryptionContextKeys" : "aws:imagebuilder:arn",
            "aws:CalledVia" : [
              "imagebuilder.amazonaws.com"
            ]
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject"
        ],
        Resource = "arn:aws:s3:::ec2imagebuilder*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogStream",
          "logs:CreateLogGroup",
          "logs:PutLogEvents"
        ],
        Resource = "arn:aws:logs:*:*:log-group:/aws/imagebuilder/*"
      }
    ]
  })
}

resource "aws_iam_user_policy_attachment" "github_actions_ecr_image_builder_user_policy" {
  user       = aws_iam_user.github_actions_user.name
  policy_arn = aws_iam_policy.github_actions_ecr_image_builder_iam_policy.arn
}

# S3 Cloudfront object
resource "aws_iam_policy" "github_actions_s3_cloudfront_iam_policy" {
  name        = "GithubActionsS3CloudfrontIAMPolicy"
  path        = "/"
  description = "IAM policy for Github Actions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:DeleteObject",
          "cloudfront:CreateInvalidation"
        ],
        Resource = [
          "arn:aws:cloudfront::771151923073:distribution/E21Q53KLD662VG",
          "arn:aws:cloudfront::771151923073:distribution/E3U8OPRT8II9HZ",
          "arn:aws:cloudfront::771151923073:distribution/E34T66PW6TSE8S",
          "arn:aws:cloudfront::771151923073:distribution/E3JYQ9FX876PSW",
          "arn:aws:s3:::webui-build-dev/*",
          "arn:aws:s3:::webui-build-prod/*",
          "arn:aws:s3:::user-portal-build-dev/*",
          "arn:aws:s3:::user-portal-build-prod/*",
        ]
      }
    ]
  })
}

resource "aws_iam_user_policy_attachment" "github_actions_s3_cloudfront_user_policy" {
  user       = aws_iam_user.github_actions_user.name
  policy_arn = aws_iam_policy.github_actions_s3_cloudfront_iam_policy.arn
}

# KMS key access to decrypt s3 bucket
resource "aws_iam_policy" "github_actions_webui_s3_kms_access_iam_policy" {
  name        = "GithubUserWebUIBucketKMSKey"
  path        = "/"
  description = "IAM policy for Github Actions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncryptTo",
          "kms:GenerateDataKeyWithoutPlaintext",
          "kms:DescribeKey",
          "kms:GenerateDataKeyPairWithoutPlaintext",
          "kms:GenerateDataKeyPair",
          "kms:ReEncryptFrom"
        ],
        Resource = [
          "arn:aws:kms:us-east-1:771151923073:key/mrk-9c38a60638da43109468949c4d50e78c",
          aws_kms_key.prod-webui-kms.arn
        ]
      }
    ]
  })
}

resource "aws_iam_user_policy_attachment" "github_actions_webui_s3_kms_access_iam_policy" {
  user       = aws_iam_user.github_actions_user.name
  policy_arn = aws_iam_policy.github_actions_webui_s3_kms_access_iam_policy.arn
}
