resource "aws_route53_zone" "prod-dashboard" {
  name = "dashboard.ravenmail.io"

  tags = {
    Name        = "ravenclaw-prod-dashboard-route53"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_route53_record" "prod-dashboard" {
  zone_id = aws_route53_zone.prod-dashboard.zone_id
  name    = "dashboard.ravenmail.io"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.s3_distribution.domain_name
    zone_id                = aws_cloudfront_distribution.s3_distribution.hosted_zone_id
    evaluate_target_health = false
  }

}
