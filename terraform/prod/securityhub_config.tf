variable "product_subscriptions" {
  type = list(string)
  default = [
    "aws/guardduty",
    "aws/inspector",
    "aws/macie",
    "aws/config",
    "aws/firewall-manager",
    "aws/health"
  ]
}

locals {
  product_arns = [
    for product in var.product_subscriptions :
    "arn:aws:securityhub:${var.region}::product/${product}"
  ]
}

resource "aws_securityhub_account" "main" {
  enable_default_standards  = true
  control_finding_generator = "STANDARD_CONTROL"
  auto_enable_controls      = true
}

resource "aws_securityhub_standards_subscription" "cis_benchmark" {
  depends_on    = [aws_securityhub_account.main]
  standards_arn = "arn:aws:securityhub:::ruleset/cis-aws-foundations-benchmark/v/1.2.0"
}

resource "aws_securityhub_standards_subscription" "aws_best_practices" {
  depends_on    = [aws_securityhub_account.main]
  standards_arn = "arn:aws:securityhub:${var.region}::standards/aws-foundational-security-best-practices/v/1.0.0"
}

resource "aws_securityhub_standards_subscription" "aws_resource_tagging_standard" {
  depends_on    = [aws_securityhub_account.main]
  standards_arn = "arn:aws:securityhub:${var.region}::standards/aws-resource-tagging-standard/v/1.0.0"
}

resource "aws_securityhub_standards_subscription" "pci_dss" {
  standards_arn = "arn:aws:securityhub:${var.region}::standards/pci-dss/v/3.2.1"
  depends_on    = [aws_securityhub_account.main]
}

resource "aws_securityhub_standards_subscription" "nist_standard" {
  standards_arn = "arn:aws:securityhub:${var.region}::standards/nist-800-53/v/5.0.0"
  depends_on    = [aws_securityhub_account.main]
}

resource "aws_securityhub_finding_aggregator" "main" {

  linking_mode = "ALL_REGIONS"
  specified_regions = [
    "ap-south-1",
    "us-east-1"
  ]
}

resource "aws_securityhub_product_subscription" "subscriptions" {
  for_each = toset(local.product_arns)

  product_arn = each.value
}

resource "aws_securityhub_organization_admin_account" "org_admin" {
  admin_account_id = "************"
}

resource "aws_securityhub_organization_configuration" "org_config" {
  auto_enable           = true
  auto_enable_standards = "DEFAULT"
  organization_configuration {
    configuration_type = "LOCAL"
  }
}

########################### AWS Config ######################################
data "aws_caller_identity" "config" {}
data "aws_partition" "config" {}
data "aws_region" "config" {}

locals {
  account_id  = data.aws_caller_identity.config.account_id
  bucket_name = "${local.account_id}-${local.region}-config"
  partition   = data.aws_partition.config.partition
  region      = data.aws_region.config.name

}

############## IAM Role ######################
data "aws_iam_policy_document" "config" {
  statement {
    effect  = "Allow"
    actions = ["s3:PutObject"]
    resources = [
      "${aws_s3_bucket.config.arn}/*",
    ]
    condition {
      test     = "StringEquals"
      variable = "s3:x-amz-acl"
      values   = ["bucket-owner-full-control"]
    }
  }
  statement {
    effect  = "Allow"
    actions = ["s3:GetBucketAcl"]
    resources = [
      aws_s3_bucket.config.arn
    ]
  }
}

resource "aws_iam_policy" "config" {
  name_prefix = "awsconfig-${local.region}"
  policy      = data.aws_iam_policy_document.config.json
}

data "aws_iam_policy_document" "config_assume" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["config.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "config" {
  name_prefix        = "aws-config-role-${local.region}"
  assume_role_policy = data.aws_iam_policy_document.config_assume.json
  tags = {
    Name        = "config-iam-role"
    Team        = "infra"
    Product     = "compliance"
    Environment = "prod"
  }
}

resource "aws_iam_role_policy_attachment" "awsconfig_managed_policy" {
  role       = aws_iam_role.config.name
  policy_arn = "arn:${local.partition}:iam::aws:policy/service-role/AWS_ConfigRole"
}

resource "aws_iam_role_policy_attachment" "this" {
  role       = aws_iam_role.config.name
  policy_arn = aws_iam_policy.config.arn
}



################## S3 Bucket for AWS configs S3 #####################

resource "aws_s3_bucket" "config" {
  bucket = local.bucket_name
  tags = {
    Name        = "aws-config-prod"
    Environment = "prod"
    Team        = "infra"
    Product     = "compliance"
  }
}

resource "aws_s3_bucket_versioning" "config" {
  bucket = aws_s3_bucket.config.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "config" {
  bucket = aws_s3_bucket.config.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "config" {
  depends_on = [aws_s3_bucket_ownership_controls.config]

  bucket = aws_s3_bucket.config.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "config" {
  bucket = aws_s3_bucket.config.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_server_side_encryption_configuration" "config" {
  bucket = aws_s3_bucket.config.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.config.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}


########################## AWS Config ########################
resource "aws_config_configuration_recorder" "config" {
  name     = "aws-config-recorder"
  role_arn = aws_iam_role.config.arn

  recording_group {
    all_supported                 = true
    include_global_resource_types = true
  }
}

resource "aws_config_delivery_channel" "config" {
  name           = "aws-config-delivery-channel"
  s3_bucket_name = aws_s3_bucket.config.bucket
  sns_topic_arn  = aws_sns_topic.config.arn

  snapshot_delivery_properties {
    delivery_frequency = "One_Hour"
  }

  depends_on = [aws_config_configuration_recorder.config]
}

resource "aws_config_configuration_recorder_status" "config" {
  name       = aws_config_configuration_recorder.config.name
  is_enabled = true

  depends_on = [aws_config_delivery_channel.config]
}

resource "aws_sns_topic" "config" {
  name = "CONFIG_SNS_TOPIC"
  tags = {
    Name        = "config-sns-topic"
    Environment = "prod"
    Team        = "infra"
    Product     = "compliance"
  }
}


