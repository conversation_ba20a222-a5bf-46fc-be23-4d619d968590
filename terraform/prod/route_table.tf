resource "aws_route_table" "rt_private_1" {
  route {
    # cidr_block = var.vpc_cidr_block
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = module.nat-1.nat_id
  }

  # route {
  #   # cidr_block = var.vpc_cidr_block
  #   cidr_block     = "0.0.0.0/0"
  #   nat_gateway_id = module.nat-2.nat_id
  # }

  tags = var.rt_nat_private_1_tags


  vpc_id = module.vpc_main.vpc_id
}

resource "aws_route_table" "rt_private_2" {
  route {
    # cidr_block = var.vpc_cidr_block
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = module.nat-2.nat_id
  }

  tags = var.rt_nat_private_2_tags

  vpc_id = module.vpc_main.vpc_id
}

resource "aws_route_table" "rt_private_3" {
  route {
    # cidr_block = var.vpc_cidr_block
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = module.nat-3.nat_id
  }

  tags = var.rt_nat_private_3_tags

  vpc_id = module.vpc_main.vpc_id
}

resource "aws_route_table" "rt_public" {
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = module.main_igw.aws_internet_gateway_id
  }

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = module.main_igw.aws_internet_gateway_id
  }

  tags = var.rt_ig_public_tags

  vpc_id = module.vpc_main.vpc_id
}

# route table association with subnet
resource "aws_route_table_association" "rt_private_1_subnet_association" {
  route_table_id = aws_route_table.rt_private_1.id
  subnet_id      = module.main_private_subnet_1.subnet_id
}

resource "aws_route_table_association" "rt_private_2_subnet_association" {
  route_table_id = aws_route_table.rt_private_2.id
  subnet_id      = module.main_private_subnet_2.subnet_id
}

resource "aws_route_table_association" "rt_private_3_subnet_association" {
  route_table_id = aws_route_table.rt_private_3.id
  subnet_id      = module.main_private_subnet_3.subnet_id
}


resource "aws_route_table_association" "rt_public_1_subnet_association" {
  route_table_id = aws_route_table.rt_public.id
  subnet_id      = module.main_public_subnet_1.subnet_id
}

resource "aws_route_table_association" "rt_public_2_subnet_association" {
  route_table_id = aws_route_table.rt_public.id
  subnet_id      = module.main_public_subnet_2.subnet_id
}

resource "aws_route_table_association" "rt_public_3_subnet_association" {
  route_table_id = aws_route_table.rt_public.id
  subnet_id      = module.main_public_subnet_3.subnet_id
}
