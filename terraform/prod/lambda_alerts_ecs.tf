resource "aws_cloudwatch_metric_alarm" "gateway_ecs_cpu_utilization" {
  alarm_name          = "Gateway_ECS_CPU_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Gateway Service - Alarm when ECS CPUUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.gateway_service_name
  }
  tags = {
    Name        = "Gateway_ECS_CPU_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "gateway_ecs_memory_utilization" {
  alarm_name          = "Gateway_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Gateway Service - Alarm when ECS MemoryUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.gateway_service_name
  }

  tags = {
    Name        = "Gateway_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "bff_ecs_cpu_utilization" {
  alarm_name          = "BFF_ECS_CPU_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "BFF Service - Alarm when ECS CPUUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.bff_service_name
  }

  tags = {
    Name        = "BFF_ECS_CPU_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "bff_ecs_memory_utilization" {
  alarm_name          = "BFF_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "BFF Service - Alarm when ECS MemoryUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.bff_service_name
  }

  tags = {
    Name        = "BFF_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "remediator_ecs_cpu_utilization" {
  alarm_name          = "remediator_ECS_CPU_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Remediator Service - Alarm when ECS CPUUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.remediator_service_name
  }

  tags = {
    Name        = "remediator_ECS_CPU_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "remediator_ecs_memory_utilization" {
  alarm_name          = "Remediator_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Remediator Service - Alarm when ECS MemoryUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.remediator_service_name
  }

  tags = {
    Name        = "Remediator_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_ecs_cpu_utilization" {
  alarm_name          = "KEYCLOAK_ECS_CPU_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Maximum"
  threshold           = 80
  alarm_description   = "KEYCLOAK Service - Alarm when ECS CPUUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.keycloak_service_name
  }

  tags = {
    Name        = "KEYCLOAK_ECS_CPU_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "keycloak_ecs_memory_utilization" {
  alarm_name          = "Keycloak_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Maximum"
  threshold           = 80
  alarm_description   = "Keycloak Service - Alarm when ECS MemoryUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.keycloak_service_name
  }

  tags = {
    Name        = "Keycloak_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "ti_go_service_ecs_cpu_utilization" {
  alarm_name          = "TI_Go_Service_ECS_CPU_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "TI Go Service Service - Alarm when ECS CPUUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.ti_go_service_service_name
  }

  tags = {
    Name        = "TI_Go_Service_ECS_CPU_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "ti_go_service_ecs_memory_utilization" {
  alarm_name          = "TI_Go_Service_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "TI Go Service - Alarm when ECS MemoryUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.ti_go_service_service_name
  }

  tags = {
    Name        = "TI_Go_Service_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "ecs_tasks_failing_frequently_alert" {
  alarm_name          = "ECS_TASK_FAILING_FREQUENTLY_ALARM"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "TriggeredRules"
  namespace           = "AWS/Events"
  period              = "600"
  statistic           = "Average"
  threshold           = 3
  alarm_description   = "One of the ECS services continuously failing due to OOM or other issue. Please check on urgent basis!!!"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    RuleName = "ecs-task-OOM-or-CPU-stop"
  }

  tags = {
    Name        = "ECS_TASK_FAILING_FREQUENTLY_ALARM"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

# resource "aws_cloudwatch_metric_alarm" "ml_inference_service_ecs_cpu_utilization" {
#   alarm_name          = "ML_Inference_Service_ECS_CPU_Utilization_Alarm"
#   comparison_operator = "GreaterThanThreshold"
#   evaluation_periods  = "20"
#   datapoints_to_alarm = "19"
#   metric_name         = "CPUUtilization"
#   namespace           = "AWS/ECS"
#   period              = "180"
#   statistic           = "Average"
#   threshold           = 98
#   alarm_description   = "ML Inference Service Service - Alarm when ECS CPUUtilization exceeds the threshold"
#   actions_enabled     = true
#   alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
#   dimensions = {
#     ClusterName = module.ecs_cluster.cluster_name
#     ServiceName = var.ml_inference_service_name
#   }

#   tags = {
#     Name        = "ML_Inference_Service_ECS_CPU_Utilization_Alarm"
#     Environment = "prod"
#     Team        = "infra"
#     Product     = "ravenclaw"
#   }
# }

resource "aws_cloudwatch_metric_alarm" "ml_inference_ecs_memory_utilization" {
  alarm_name          = "ML_Inference_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "ML Inference Service - Alarm when ECS MemoryUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = var.ml_inference_service_name
  }

  tags = {
    Name        = "ML_Inference_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "inline_ecs_cpu_utilization" {
  alarm_name          = "inline_ECS_CPU_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 65
  alarm_description   = "Inline Service - Alarm when ECS CPUUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = "inline"
  }

  tags = {
    Name        = "inline_ECS_CPU_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudwatch_metric_alarm" "inline_ecs_memory_utilization" {
  alarm_name          = "Inline_ECS_Memory_Utilization_Alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "180"
  statistic           = "Average"
  threshold           = 65
  alarm_description   = "Inline Service - Alarm when ECS MemoryUtilization exceeds the threshold"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.ecs_alerts.arn]
  dimensions = {
    ClusterName = module.ecs_cluster.cluster_name
    ServiceName = "inline"
  }

  tags = {
    Name        = "Inline_ECS_Memory_Utilization_Alarm"
    Environment = "prod"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}
