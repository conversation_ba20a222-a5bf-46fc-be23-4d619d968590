############### ALB Logs ##################
resource "aws_s3_bucket" "logs" {
  bucket = "alb-cloudfront-logs-bucket"

  tags = {
    Name        = "alb-cloudfront-logs-s3-bucket"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_s3_bucket_ownership_controls" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "logs" {
  depends_on = [aws_s3_bucket_ownership_controls.logs]

  bucket = aws_s3_bucket.logs.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "logs" {
  bucket = aws_s3_bucket.logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_server_side_encryption_configuration" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    id     = "ExpireObjectsAfter30Days"
    status = "Enabled" # Enables the rule

    expiration {
      days = 7 # Objects older than 30 days will be deleted
    }

    filter {
      prefix = "" # Applies the rule to all objects in the bucket
    }
  }
}

# # Get the ELB service account ARN
data "aws_elb_service_account" "main" {}

resource "aws_s3_bucket_policy" "logs_policy" {
  bucket = aws_s3_bucket.logs.id
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          "AWS" : "arn:aws:iam::************:root"
        },
        Action = [
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObject",
          "s3:GetObjectAttributes"
        ],
        Resource = [
          aws_s3_bucket.logs.arn,
          "${aws_s3_bucket.logs.arn}/*"
        ]
      },
      {
        Effect = "Allow",
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        },
        Action = [
          "s3:PutObject"
        ],
        Resource = "${aws_s3_bucket.logs.arn}/vpc/*", # Restrict to the vpc/ prefix
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Effect = "Allow",
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        },
        Action = [
          "s3:GetBucketAcl"
        ],
        Resource = aws_s3_bucket.logs.arn
      },
      {
        Action = "s3:PutObject"
        Condition = {
          StringEquals = {
            "aws:SourceAccount" = "************"
          }
        }
        Effect = "Allow"
        Principal = {
          Service = "logging.s3.amazonaws.com"
        }
        Resource = "arn:aws:s3:::alb-cloudfront-logs-bucket/*"
        Sid      = "S3PolicyStmt-DO-NOT-MODIFY-*************"
        }, {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "delivery.logs.amazonaws.com"
        },
        "Action" : "s3:PutObject",
        "Resource" : "arn:aws:s3:::alb-cloudfront-logs-bucket/*",
        "Condition" : {
          "StringEquals" : {
            "s3:x-amz-acl" : "bucket-owner-full-control"
          }
        }
      },
    ]
  })
}

############ S3 event notification 
resource "aws_iam_role" "iam_for_lambda_alb_logs" {
  name               = "iam_for_lambda_alb_logs"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": [
          "lambda.amazonaws.com"
        ]
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "alb_cloudwatch_logs_policy" {
  name = "ALBCloudWatchLogsPermissions"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogStream",
          "logs:CreateLogGroup",
          "logs:DeleteLogStream",
          "logs:PutLogEvents",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObject",
          "s3:GetObjectAttributes"
        ]
        Resource = ["*"]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "attach_alb_cloudwatch_logs_policy" {
  role       = aws_iam_role.iam_for_lambda_alb_logs.name
  policy_arn = aws_iam_policy.alb_cloudwatch_logs_policy.arn
}

resource "aws_s3_bucket_notification" "ravenclaw_alb_access_logs_bucket_notification" {
  bucket = aws_s3_bucket.logs.id

  lambda_function {
    lambda_function_arn = aws_lambda_function.ravenclaw_alb_access_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "alb/ravenclaw/access-logs/"
    filter_suffix       = ".log.gz"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.keycloak_alb_access_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "alb/keycloak/access-logs/"
    filter_suffix       = ".log.gz"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.ravenclaw_alb_connection_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "alb/ravenclaw/connection-logs/"
    filter_suffix       = ".log.gz"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.keycloak_alb_connection_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "alb/keycloak/connection-logs/"
    filter_suffix       = ".log.gz"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.webui_std_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "cloudfront/webui/stdlogs/"
    filter_suffix       = ".gz"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.assets_std_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "cloudfront/assets/stdlogs/"
    filter_suffix       = ".gz"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.internal_alb_connection_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "alb/internal/connection-logs/"
    filter_suffix       = ".log.gz"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.internal_alb_access_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "alb/internal/access-logs/"
    filter_suffix       = ".log.gz"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.webui_realtime_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "cloudfront/webui/realtime-logs"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.assets_realtime_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "cloudfront/assets/realtime-logs"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.vpc_flow_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "vpc"
  }

  lambda_function {
    lambda_function_arn = aws_lambda_function.gosmtp_nlb_access_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "nlb/gosmtp/access-logs/"
    filter_suffix       = ".gz"
  }

  depends_on = [
    aws_lambda_permission.allow_alb_logs_bucket,
    aws_lambda_permission.allow_keycloak_alb_logs_bucket,
    aws_lambda_permission.allow_internal_alb_logs_bucket,
    aws_lambda_permission.allow_ravenclaw_alb_connection_logs_bucket,
    aws_lambda_permission.allow_keycloak_alb_connection_logs_bucket,
    aws_lambda_permission.allow_internal_alb_connection_logs_bucket,
    aws_lambda_permission.allow_webui_realtime_logs,
    aws_lambda_permission.allow_assets_realtime_logs
  ]
}

##################### Ravenclaw ALB Access Logs #######################
resource "aws_lambda_permission" "allow_alb_logs_bucket" {
  statement_id  = "AllowExecutionFromS3Bucket"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.ravenclaw_alb_access_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "ravenclaw_alb_access_logs" {
  filename         = "lambda_function_elb_payload.zip"
  function_name    = "alb-access-logs-to-cloudwatch"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_elb_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/alb/ravenclaw/access-logs"
    }
  }

  tags = {
    Name        = "ravenclaw-alb-access-logs-lambda-func"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}




##################### keycloak alb access logs #######################
resource "aws_lambda_permission" "allow_keycloak_alb_logs_bucket" {
  statement_id  = "AllowExecutionFromS3BucketKeycloak"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.keycloak_alb_access_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "keycloak_alb_access_logs" {
  filename         = "lambda_function_elb_payload.zip"
  function_name    = "keycloak-alb-access-logs-to-cloudwatch"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_elb_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/alb/keycloak/access-logs"
    }
  }

  tags = {
    Name        = "keycloak-alb-access-logs-lambda-func"
    Team        = "infra"
    Product     = "keycloak"
    Environment = "prod"
  }
}

##################### internal alb access logs #######################
resource "aws_lambda_permission" "allow_internal_alb_logs_bucket" {
  statement_id  = "allowexecutionfroms3bucketinternal"
  action        = "lambda:invokefunction"
  function_name = aws_lambda_function.internal_alb_access_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "internal_alb_access_logs" {
  filename         = "lambda_function_elb_payload.zip"
  function_name    = "internal-alb-access-logs-to-cloudwatch"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_elb_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/alb/internal/access-logs"
    }
  }

  tags = {
    name        = "internal-alb-access-logs-lambda-func"
    team        = "infra"
    product     = "internal-alb"
    environment = "prod"
  }
}

##################### gosmtp nlb access logs #######################
resource "aws_lambda_permission" "allow_gosmtp_nlb_logs_bucket" {
  statement_id  = "allowexecutionfroms3bucketgosmtp"
  action        = "lambda:invokefunction"
  function_name = aws_lambda_function.gosmtp_nlb_access_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "gosmtp_nlb_access_logs" {
  filename         = "lambda_function_nlb_payload.zip"
  function_name    = "gosmtp-nlb-access-logs-to-cloudwatch"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_nlb_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/nlb/gosmtp/access-logs"
    }
  }

  tags = {
    name        = "gosmtp-nlb-access-logs-lambda-func"
    team        = "infra"
    product     = "gosmtp-nlb"
    environment = "prod"
  }
}



###################### Ravenclaw ALB Connection Logs #####################
resource "aws_lambda_permission" "allow_ravenclaw_alb_connection_logs_bucket" {
  statement_id  = "AllowExecutionFromS3BucketRavenclawALBConnectionLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.ravenclaw_alb_connection_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "ravenclaw_alb_connection_logs" {
  filename         = "lambda_function_elb_connection_logs_payload.zip"
  function_name    = "ravenclaw-alb-connection-logs-to-cloudwatch"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_elb_connection_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/alb/ravenclaw/connection-logs"
    }
  }

  tags = {
    Name        = "ravenclaw-alb-connection-logs-lambda-func"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

################## Keycloak ALB Connection Logs ########################3
resource "aws_lambda_permission" "allow_keycloak_alb_connection_logs_bucket" {
  statement_id  = "AllowExecutionFromS3BucketKeycloakALBConnectionLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.keycloak_alb_connection_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "keycloak_alb_connection_logs" {
  filename         = "lambda_function_elb_connection_logs_payload.zip"
  function_name    = "keycloak-alb-connection-logs-to-cloudwatch"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_elb_connection_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/alb/keycloak/connection-logs"
    }
  }

  tags = {
    Name        = "keycloak-alb-connection-logs-lambda-func"
    Team        = "infra"
    Product     = "keycloak"
    Environment = "prod"
  }
}

###################### Internal ALB Connection Logs #####################
resource "aws_lambda_permission" "allow_internal_alb_connection_logs_bucket" {
  statement_id  = "AllowExecutionFromS3BucketInternalALBConnectionLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.internal_alb_connection_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "internal_alb_connection_logs" {
  filename         = "lambda_function_elb_connection_logs_payload.zip"
  function_name    = "internal-alb-connection-logs-to-cloudwatch"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_elb_connection_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/alb/internal/connection-logs"
    }
  }

  tags = {
    Name        = "internal-alb-connection-logs-lambda-func"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "prod"
  }
}

##################### Webui Cloudfront Std Logs #########################
resource "aws_lambda_permission" "allow_cloudfront_webui_std_logs_bucket" {
  statement_id  = "AllowExecutionFromS3BucketWebuiStdLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.webui_std_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "webui_std_logs" {
  filename         = "lambda_function_cloudfront_std_logs_payload.zip"
  function_name    = "webui-cloudfront-stdlogs-function"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_cloudfront_std_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/cloudfront/webui/stdlogs"
    }
  }

  tags = {
    Name        = "cloudfront-webui-stdlogs-lambda-func"
    Team        = "infra"
    Product     = "webui"
    Environment = "prod"
  }
}


####################### Assets Cloudfront std logs ######################### 
resource "aws_lambda_permission" "allow_cloudfront_assets_std_logs_bucket" {
  statement_id  = "AllowExecutionFromS3BucketAssetsStdLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.assets_std_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "assets_std_logs" {
  filename         = "lambda_function_cloudfront_std_logs_payload.zip"
  function_name    = "assets-cloudfront-stdlogs-function"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_cloudfront_std_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/cloudfront/assets/stdlogs"
    }
  }

  tags = {
    Name        = "cloudfront-assets-stdlogs-lambda-func"
    Team        = "infra"
    Product     = "assets"
    Environment = "prod"
  }
}

########################## Cloudfront real time logs #####################3
resource "aws_iam_role" "real_time_logs" {
  name = "CloudfrontWebUIRealtimeLogConfig"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "cloudfront.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "kinesis_policy" {
  name = "KinesisLogPolicy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "kinesis:DescribeStreamSummary",
          "kinesis:DescribeStream",
          "kinesis:PutRecord",
          "kinesis:PutRecords",
        ]
        Resource = aws_kinesis_stream.webui_real_time_logs.arn
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "attach_kinesis_policy" {
  role       = aws_iam_role.real_time_logs.name
  policy_arn = aws_iam_policy.kinesis_policy.arn
}

############### Amazon Data Firehose #####################
resource "aws_iam_role" "firehose_cloudfront_real_time_logs" {
  name = "CloudfrontFirehoseRealtimeLogConfig"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "firehose.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "kinesis_firehose_cloudfront_realtime_policy" {
  name = "KinesisFirehouseCloudfrontRTLogsToS3Policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "glue:GetTable",
          "glue:GetTableVersion",
          "glue:GetTableVersions"
        ]
        Resource = "*"
        }, {
        Effect = "Allow",
        Action = [
          "kafka:GetBootstrapBrokers",
          "kafka:DescribeCluster",
          "kafka:DescribeClusterV2",
          "kafka-cluster:Connect",
          "kafka-cluster:DescribeGroup",
          "kafka-cluster:DescribeTopic",
          "kafka-cluster:DescribeTopicDynamicConfiguration",
          "kafka-cluster:ReadData"
        ],
        Resource = "*"
        }, {
        Effect = "Allow",
        Action = [
          "s3:AbortMultipartUpload",
          "s3:GetBucketLocation",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:ListBucketMultipartUploads",
          "s3:PutObject"
        ],
        Resource = ["*"]
        }, {
        Effect = "Allow",
        Action = [
          "lambda:InvokeFunction",
          "lambda:GetFunctionConfiguration"
        ],
        Resource = ["*"]
        }, {
        Effect = "Allow",
        Action = [
          "kms:GenerateDataKey",
          "kms:Decrypt"
        ],
        Resource = ["*"],
        }, {
        Effect = "Allow",
        Action = [
          "logs:PutLogEvents"
        ],
        Resource = ["*"],
        }, {
        Effect = "Allow",
        Action = [
          "kinesis:DescribeStream",
          "kinesis:GetShardIterator",
          "kinesis:GetRecords",
          "kinesis:ListShards"
        ],
        Resource = ["*"]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "attach_kinesis_realtime_logs_to_s3_policy" {
  role       = aws_iam_role.firehose_cloudfront_real_time_logs.name
  policy_arn = aws_iam_policy.kinesis_firehose_cloudfront_realtime_policy.arn
}

#### Webui Real time log configuration ######
resource "aws_kinesis_stream" "webui_real_time_logs" {
  name             = "webui-real-time-logs-stream"
  shard_count      = 1
  retention_period = 24

  stream_mode_details {
    stream_mode = "PROVISIONED"
  }

  tags = {
    Name        = "webui-real-time-logs-stream"
    Environment = "prod"
    Team        = "infra"
    Application = "webui"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudfront_realtime_log_config" "webui_real_time_logs" {
  name          = "webui-cloudfront-real-time-logs"
  sampling_rate = 100
  fields = [
    "timestamp",
    "c-ip",
    "s-ip",
    "time-to-first-byte",
    "sc-status",
    "sc-bytes",
    "cs-method",
    "cs-protocol",
    "cs-host",
    "cs-uri-stem",
    "cs-bytes",
    "x-edge-location",
    "x-edge-request-id",
    "x-host-header",
    "time-taken",
    "cs-protocol-version",
    "c-ip-version",
    "cs-user-agent",
    "cs-referer",
    "cs-cookie",
    "cs-uri-query",
    "x-edge-response-result-type",
    "x-forwarded-for",
    "ssl-protocol",
    "ssl-cipher",
    "x-edge-result-type",
    "fle-encrypted-fields",
    "fle-status",
    "sc-content-type",
    "sc-content-len",
    "sc-range-start",
    "sc-range-end",
    "c-port",
    "x-edge-detailed-result-type",
    "c-country",
    "cs-accept-encoding",
    "cs-accept",
    "cache-behavior-path-pattern",
    "cs-headers",
    "cs-header-names",
    "cs-headers-count",
    "origin-fbl",
    "origin-lbl",
    "asn",
    "primary-distribution-id",
    "primary-distribution-dns-name",
    "cmcd-encoded-bitrate",
    "cmcd-buffer-length",
    "cmcd-buffer-starvation",
    "cmcd-content-id",
    "cmcd-object-duration",
    "cmcd-deadline",
    "cmcd-measured-throughput",
    "cmcd-next-object-request",
    "cmcd-next-range-request",
    "cmcd-object-type",
    "cmcd-playback-rate",
    "cmcd-requested-maximum-throughput",
    "cmcd-streaming-format",
    "cmcd-session-id",
    "cmcd-stream-type",
    "cmcd-startup",
    "cmcd-top-bitrate",
    "cmcd-version",
    "r-host",
    "sr-reason",
    "x-edge-mqcs"
  ]

  endpoint {
    stream_type = "Kinesis"

    kinesis_stream_config {
      role_arn   = aws_iam_role.real_time_logs.arn
      stream_arn = aws_kinesis_stream.webui_real_time_logs.arn
    }
  }
}

resource "aws_kinesis_firehose_delivery_stream" "webui_realtime_logs_firehose" {
  name        = "webui-cloudfront-logs-firehose"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn         = aws_iam_role.firehose_cloudfront_real_time_logs.arn
    bucket_arn       = aws_s3_bucket.logs.arn
    prefix           = "cloudfront/webui/realtime-logs"
    custom_time_zone = "Asia/Calcutta"
  }

  kinesis_source_configuration {
    kinesis_stream_arn = aws_kinesis_stream.webui_real_time_logs.arn
    role_arn           = aws_iam_role.firehose_cloudfront_real_time_logs.arn
  }

  tags = {
    Name        = "webui-realtime-logs-to-s3-firehose"
    Environment = "prod"
    Team        = "infra"
    Product     = "webui"
  }
}

#### Assets Real time log configuration ######
resource "aws_kinesis_stream" "assets_real_time_logs" {
  name             = "assets-real-time-logs-stream"
  shard_count      = 1
  retention_period = 24

  stream_mode_details {
    stream_mode = "PROVISIONED"
  }

  tags = {
    Name        = "assets-real-time-logs-stream"
    Environment = "prod"
    Team        = "infra"
    Application = "assets"
    Product     = "ravenclaw"
  }
}

resource "aws_cloudfront_realtime_log_config" "assets_real_time_logs" {
  name          = "assets-cloudfront-real-time-logs"
  sampling_rate = 100
  fields = [
    "timestamp",
    "c-ip",
    "s-ip",
    "time-to-first-byte",
    "sc-status",
    "sc-bytes",
    "cs-method",
    "cs-protocol",
    "cs-host",
    "cs-uri-stem",
    "cs-bytes",
    "x-edge-location",
    "x-edge-request-id",
    "x-host-header",
    "time-taken",
    "cs-protocol-version",
    "c-ip-version",
    "cs-user-agent",
    "cs-referer",
    "cs-cookie",
    "cs-uri-query",
    "x-edge-response-result-type",
    "x-forwarded-for",
    "ssl-protocol",
    "ssl-cipher",
    "x-edge-result-type",
    "fle-encrypted-fields",
    "fle-status",
    "sc-content-type",
    "sc-content-len",
    "sc-range-start",
    "sc-range-end",
    "c-port",
    "x-edge-detailed-result-type",
    "c-country",
    "cs-accept-encoding",
    "cs-accept",
    "cache-behavior-path-pattern",
    "cs-headers",
    "cs-header-names",
    "cs-headers-count",
    "origin-fbl",
    "origin-lbl",
    "asn",
    "primary-distribution-id",
    "primary-distribution-dns-name",
    "cmcd-encoded-bitrate",
    "cmcd-buffer-length",
    "cmcd-buffer-starvation",
    "cmcd-content-id",
    "cmcd-object-duration",
    "cmcd-deadline",
    "cmcd-measured-throughput",
    "cmcd-next-object-request",
    "cmcd-next-range-request",
    "cmcd-object-type",
    "cmcd-playback-rate",
    "cmcd-requested-maximum-throughput",
    "cmcd-streaming-format",
    "cmcd-session-id",
    "cmcd-stream-type",
    "cmcd-startup",
    "cmcd-top-bitrate",
    "cmcd-version",
    "r-host",
    "sr-reason",
    "x-edge-mqcs"
  ]

  endpoint {
    stream_type = "Kinesis"

    kinesis_stream_config {
      role_arn   = aws_iam_role.real_time_logs.arn
      stream_arn = aws_kinesis_stream.assets_real_time_logs.arn
    }
  }
}

resource "aws_kinesis_firehose_delivery_stream" "assets_realtime_logs_firehose" {
  name        = "assets-cloudfront-logs-firehose"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn         = aws_iam_role.firehose_cloudfront_real_time_logs.arn
    bucket_arn       = aws_s3_bucket.logs.arn
    prefix           = "cloudfront/assets/realtime-logs"
    custom_time_zone = "Asia/Calcutta"
  }

  kinesis_source_configuration {
    kinesis_stream_arn = aws_kinesis_stream.assets_real_time_logs.arn
    role_arn           = aws_iam_role.firehose_cloudfront_real_time_logs.arn
  }

  tags = {
    Name        = "assets-realtime-logs-to-s3-firehose"
    Environment = "prod"
    Team        = "infra"
    Product     = "assets"
  }
}

############ Cloudfront realtime logs from s3 to lambda to Cloudwatch ###############
resource "aws_lambda_permission" "allow_webui_realtime_logs" {
  statement_id  = "AllowExecutionFromS3BucketWebuiRealtime"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.webui_realtime_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "webui_realtime_logs" {
  filename         = "lambda_function_cloudfront_realtime_logs_payload.zip"
  function_name    = "webui-cloudfront-realtime-logs"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_cloudfront_realtime_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/cloudfront/webui/realtimelogs"
    }
  }

  tags = {
    Name        = "webui-cloudfront-realtime-logs-func"
    Team        = "infra"
    Product     = "webui"
    Environment = "prod"
  }
}

resource "aws_lambda_permission" "allow_assets_realtime_logs" {
  statement_id  = "AllowExecutionFromS3BucketAssetsRealtime"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.assets_realtime_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "assets_realtime_logs" {
  filename         = "lambda_function_cloudfront_realtime_logs_payload.zip"
  function_name    = "assets-cloudfront-realtime-logs"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  source_code_hash = filebase64sha256("lambda_function_cloudfront_realtime_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/cloudfront/assets/realtimelogs"
    }
  }

  tags = {
    Name        = "webui-cloudfront-realtime-logs-func"
    Team        = "infra"
    Product     = "webui"
    Environment = "prod"
  }
}

########### VPC flow logs ################3
resource "aws_lambda_permission" "allow_vpc_flow_logs" {
  statement_id  = "AllowExecutionFromS3BucketVPCFlowLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.vpc_flow_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "vpc_flow_logs" {
  filename         = "lambda_function_vpc_flow_logs_payload.zip"
  function_name    = "vpc-flow-logs-func"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  timeout          = 900
  source_code_hash = filebase64sha256("lambda_function_vpc_flow_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/vpc/flow-logs"
    }
  }

  tags = {
    Name        = "vpc-flow-logs-func"
    Team        = "infra"
    Product     = "compliance"
    Environment = "prod"
  }
}
