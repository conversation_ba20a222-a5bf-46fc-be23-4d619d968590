resource "aws_s3_bucket" "user_portal_build" {
  bucket = "user-portal-build-prod"

  tags = {
    Name        = "user-portal-build-prod-s3-bucket"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_s3_bucket_versioning" "user_portal_build" {
  bucket = aws_s3_bucket.user_portal_build.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "user_portal_build" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.user_portal_build]

  bucket = aws_s3_bucket.user_portal_build.id

  rule {
    id = "lifecycle_configuration"

    noncurrent_version_expiration {
      noncurrent_days = 90
    }

    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "user_portal_build" {
  bucket = aws_s3_bucket.user_portal_build.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "user_portal_build" {
  depends_on = [aws_s3_bucket_ownership_controls.user_portal_build]

  bucket = aws_s3_bucket.user_portal_build.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "user_portal_build" {
  bucket = aws_s3_bucket.user_portal_build.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_website_configuration" "user_portal_build" {
  bucket = aws_s3_bucket.user_portal_build.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "index.html"
  }
}

resource "aws_s3_bucket_policy" "user_portal_build_oai" {
  bucket = aws_s3_bucket.user_portal_build.id


  policy = jsonencode({
    "Version" : "2008-10-17",
    "Id" : "PolicyForCloudFrontPrivateContentProd",
    "Statement" : [
      {
        "Sid" : "AllowCloudFrontServicePrincipalProd",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudfront.amazonaws.com"
        },
        "Action" : "s3:GetObject",
        "Resource" : "arn:aws:s3:::user-portal-build-prod/*",
        "Condition" : {
          "StringEquals" : {
            "AWS:SourceArn" : "arn:aws:cloudfront::771151923073:distribution/E3JYQ9FX876PSW"
          }
        }
      }
    ]
  })
}

resource "aws_s3_bucket_server_side_encryption_configuration" "user_portal_build" {
  bucket = aws_s3_bucket.user_portal_build.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.prod-webui-kms.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

################################

######## Cloudfront 

resource "aws_cloudfront_origin_access_identity" "user_portal_build" {
  comment = "User Portal Build - Cloudfront Origin Access Identity"
}

resource "aws_cloudfront_cache_policy" "user_portal_build" {
  name        = "user_portal_build-cache-policy-prod"
  comment     = "User Portal Build Cache policy for cloud distribution"
  default_ttl = 50
  max_ttl     = 100
  min_ttl     = 1
  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "none"
    }
    headers_config {
      header_behavior = "whitelist"
      headers {
        items = ["Origin", "Content-Type"] # Specify the headers to whitelist
      }
    }
    query_strings_config {
      query_string_behavior = "none"
    }

    enable_accept_encoding_gzip   = true
    enable_accept_encoding_brotli = true
  }
}

resource "aws_cloudfront_distribution" "user_portal_s3_distribution" {
  origin {
    domain_name = aws_s3_bucket.user_portal_build.bucket_regional_domain_name

    origin_access_control_id = aws_cloudfront_origin_access_control.user_portal_build.id
    origin_id                = aws_s3_bucket.user_portal_build.id
  }


  enabled             = true
  is_ipv6_enabled     = false
  comment             = "User Portal Static Cloudfront Distribution"
  default_root_object = "index.html"
  aliases             = ["portal.ravenmail.io"]

  default_cache_behavior {
    allowed_methods            = ["GET", "HEAD"]
    cached_methods             = ["GET", "HEAD"]
    target_origin_id           = aws_s3_bucket.user_portal_build.id
    cache_policy_id            = aws_cloudfront_cache_policy.user_portal_build.id
    response_headers_policy_id = aws_cloudfront_response_headers_policy.no_cache.id

    compress = true

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
  }

  ordered_cache_behavior {
    path_pattern     = "/index.html"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = aws_s3_bucket.user_portal_build.id
    cache_policy_id  = aws_cloudfront_cache_policy.user_portal_build.id

    response_headers_policy_id = aws_cloudfront_response_headers_policy.user_portal_no_cache.id

    viewer_protocol_policy = "redirect-to-https"
    compress               = true
  }


  custom_error_response {
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 0
  }

  custom_error_response {
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 0
  }

  custom_error_response {
    error_code            = 400
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 0
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
      locations        = []
    }
  }

  tags = {
    Name        = "ravenclaw-user-portal-build-cloudfront"
    Environment = "prod"
    Product     = "ravenclaw"
    Team        = "infra"
  }

  price_class = "PriceClass_100"

  viewer_certificate {
    # cloudfront_default_certificate = true
    acm_certificate_arn      = "arn:aws:acm:us-east-1:771151923073:certificate/632bbc67-b0fc-45ef-8046-c8a5d21e2db9"
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2018"
  }


  lifecycle {
    ignore_changes = [
      default_cache_behavior[0].default_ttl,
      default_cache_behavior[0].max_ttl,
    ]
  }
}

resource "aws_cloudfront_response_headers_policy" "user_portal_no_cache" {
  name    = "UserPortalNoCachePolicyForIndexProd"
  comment = "Set Cache-Control: user portal no-cache for index.html"
  security_headers_config {
    # Configure the HSTS header (Strict-Transport-Security)
    strict_transport_security {
      override                   = true
      access_control_max_age_sec = 63072000 # 2 years in seconds
      include_subdomains         = true
      preload                    = true
    }
    # Optionally, add additional security headers like X-Content-Type-Options, etc.
    content_type_options {
      override = true
    }
    xss_protection {
      override   = true
      protection = true
      mode_block = true
    }
  }

  custom_headers_config {
    items {
      header   = "Cache-Control"
      value    = "no-cache"
      override = true
    }
  }

  remove_headers_config {
    items {
      header = "x-amz-server-side-encryption-aws-kms-key-id"
    }

    items {
      header = "X-Amz-Server-Side-Encryption"
    }

    items {
      header = "X-Amz-Server-Side-Encryption-Bucket-Key-Enabled"
    }

    items {
      header = "Etag"
    }

    items {
      header = "Server"
    }

    items {
      header = "X-Amz-Version-id"
    }
  }
}

resource "aws_cloudfront_origin_access_control" "user_portal_build" {
  name                              = "user-portal-kms-oac-prod"
  description                       = "User Portal kms encrypted oac access"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}
