resource "aws_cloudwatch_log_group" "bff" {
  name              = "/ecs/bff"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-bff"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "bff"
  }
}

resource "aws_cloudwatch_log_group" "remediator" {
  name              = "/ecs/remediator"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-remediator"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "remediator"
  }
}

resource "aws_cloudwatch_log_group" "ml_inference" {
  name              = "/ecs/ml-inference"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-ml-inference"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "ml-inference"
  }
}

resource "aws_cloudwatch_log_group" "ingestion" {
  name              = "/ecs/ingestion"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-ingestion"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "ingestion"
  }
}

resource "aws_cloudwatch_log_group" "gotenberg" {
  name              = "/ecs/gotenberg"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-gotenberg"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "bff"
  }
}

resource "aws_cloudwatch_log_group" "gateway" {
  name              = "/ecs/gateway"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-gateway"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "gateway"
  }
}

resource "aws_cloudwatch_log_group" "setup" {
  name              = "/ecs/setup"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-setup"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "setup"
  }
}

resource "aws_cloudwatch_log_group" "ti_go_service" {
  name              = "/ecs/ti-go-service"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-ti-go-service"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "ti-go-service"
  }
}

resource "aws_cloudwatch_log_group" "keycloak" {
  name              = "keycloak-ecs-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-keycloak-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "keycloak"
  }
}

resource "aws_cloudwatch_log_group" "nats_server" {
  name              = "/ec2/nats"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "nats-server"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

resource "aws_cloudwatch_log_group" "bff_ecs_exporter" {
  name              = "ecs/bff-ecs-exporter"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "bff-ecs-exporter"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "prometheus"
  }
}

resource "aws_cloudwatch_log_group" "system_metrics_lambda_alerts" {
  name              = "/aws/lambda/SnsToGoogleChat"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/SnsToGoogleChat"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "lambda-function"
  }
}

resource "aws_cloudwatch_log_group" "log_lambda_alerts" {
  name              = "/aws/lambda/sendAlert"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/sendAlert"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "lambda-function"
  }
}

resource "aws_cloudwatch_log_group" "ravenclaw_rds" {
  name              = "/aws/rds/instance/ravenclaw/postgresql"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/rds/instance/ravenclaw/postgresql"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "rds"
  }
}

resource "aws_cloudwatch_log_group" "keycloak_rds" {
  name              = "/aws/rds/instance/keycloak/postgresql"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/rds/instance/keycloak/postgresql"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "rds"
  }
}

resource "aws_cloudwatch_log_group" "nats_node_1" {
  name              = "/ec2/natsNode1"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "nats-node-1"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

resource "aws_cloudwatch_log_group" "nats_node_2" {
  name              = "/ec2/natsNode2"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "nats-node-2"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

resource "aws_cloudwatch_log_group" "nats_node_3" {
  name              = "/ec2/natsNode3"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "nats-node-3"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

resource "aws_cloudwatch_log_group" "nats_node_4" {
  name              = "/ec2/natsNode4"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "nats-node-4"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "prod"
    Application = "nats"
  }
}

resource "aws_cloudwatch_log_group" "cloudtrail" {
  name              = "/aws/cloudtrail/logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/cloudtrail/logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "cloudtrail"
  }
}

# resource "aws_cloudwatch_log_group" "cloudfront_realtime_logs" {
#   name              = "/aws/cloudfront/webui"
#   skip_destroy      = true
#   log_group_class   = "STANDARD"
#   retention_in_days = 90

#   tags = {
#     Name        = "/aws/cloudfront/webui"
#     Product     = "AWS"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "webui"
#   }
# }

######## ALB Access logs #############

resource "aws_cloudwatch_log_group" "ravenclaw_alb_access_logs" {
  name              = "/aws/alb/ravenclaw/access-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/alb/ravenclaw/access-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "ravenclaw-alb"
  }
}

resource "aws_cloudwatch_log_group" "ravenclaw_alb_lambda_access_logs" {
  name              = "/aws/lambda/alb-access-logs-to-cloudwatch"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/alb-access-logs-to-cloudwatch"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "ravenclaw-alb-logs-lambda"
  }
}

resource "aws_cloudwatch_log_group" "keycloak_alb_access_logs" {
  name              = "/aws/alb/keycloak/access-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/alb/keycloak/access-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "keycloak-alb"
  }
}

resource "aws_cloudwatch_log_group" "keycloak_alb_lambda_access_logs" {
  name              = "/aws/lambda/keycloak-alb-access-logs-to-cloudwatch"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/keycloak-alb-access-logs-to-cloudwatch"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "keycloak-alb-logs-lambda"
  }
}

resource "aws_cloudwatch_log_group" "internal_alb_access_logs" {
  name              = "/aws/alb/internal/access-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/alb/internal/access-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "internal-alb"
  }
}

resource "aws_cloudwatch_log_group" "internal_alb_lambda_access_logs" {
  name              = "/aws/lambda/internal-alb-access-logs-to-cloudwatch"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/internal-alb-access-logs-to-cloudwatch"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "internal-alb-logs-lambda"
  }
}



############## ALB connection logs ########################
resource "aws_cloudwatch_log_group" "ravenclaw_alb_connection_logs" {
  name              = "/aws/alb/ravenclaw/connection-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/alb/ravenclaw/connection-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "ravenclaw-alb"
  }
}

resource "aws_cloudwatch_log_group" "ravenclaw_alb_lambda_connection_logs" {
  name              = "/aws/lambda/ravenclaw-alb-connection-logs-to-cloudwatch"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/ravenclaw-alb-connection-logs-to-cloudwatch"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "ravenclaw-alb-logs-lambda"
  }
}

resource "aws_cloudwatch_log_group" "keycloak_alb_connection_logs" {
  name              = "/aws/alb/keycloak/connection-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/alb/keycloak/connection-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "keycloak-alb"
  }
}

resource "aws_cloudwatch_log_group" "keycloak_alb_lambda_connection_logs" {
  name              = "/aws/lambda/keycloak-alb-connection-logs-to-cloudwatch"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/keycloak-alb-connection-logs-to-cloudwatch"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "keycloak-alb-logs-lambda"
  }
}

resource "aws_cloudwatch_log_group" "internal_alb_connection_logs" {
  name              = "/aws/alb/internal/connection-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/alb/internal/connection-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "internal-alb"
  }
}

resource "aws_cloudwatch_log_group" "internal_alb_lambda_connection_logs" {
  name              = "/aws/lambda/internal-alb-connection-logs-to-cloudwatch"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/internal-alb-connection-logs-to-cloudwatch"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "internal-alb-logs-lambda"
  }
}



############################ Cloudfront Standard Logs #####################
resource "aws_cloudwatch_log_group" "cloudfront_webui_std_logs" {
  name              = "/aws/cloudfront/webui/stdlogs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/cloudfront/webui/stdlogs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "webui"
  }
}

resource "aws_cloudwatch_log_group" "cloudfront_webui_std_logs_lambda" {
  name              = "/aws/lambda/webui-cloudfront-stdlogs-function"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/webui/stdlogs-function"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "webui"
  }
}

resource "aws_cloudwatch_log_group" "cloudfront_assets_std_logs" {
  name              = "/aws/cloudfront/assets/stdlogs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/cloudfront/assets/stdlogs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "assets"
  }
}

resource "aws_cloudwatch_log_group" "cloudfront_assets_std_logs_lambda" {
  name              = "/aws/lambda/assets-cloudfront-stdlogs-function"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/assets/stdlogs-function"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "assets"
  }
}

resource "aws_cloudwatch_log_group" "cloudfront_webui_realtime_logs_lambda" {
  name              = "/aws/lambda/webui-cloudfront-realtime-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/webui/webui-cloudfront-realtime-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "webui"
  }
}

resource "aws_cloudwatch_log_group" "cloudfront_assets_realtime_logs_lambda" {
  name              = "/aws/lambda/assets-cloudfront-realtime-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/assets/assets-cloudfront-realtime-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "webui"
  }
}

resource "aws_cloudwatch_log_group" "cloudfront_webui_realtime_logs" {
  name              = "/aws/cloudfront/webui/realtimelogs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/cloudfront/webui/realtimelogs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "webui"
  }
}

resource "aws_cloudwatch_log_group" "cloudfront_assets_realtime_logs" {
  name              = "/aws/cloudfront/assets/realtimelogs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/cloudfront/assets/realtimelogs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "assets"
  }
}




resource "aws_cloudwatch_log_group" "annotations_cron_logs" {
  name              = "annotations-cron-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "annotations-cron-logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "prod"
    Application = "annotations"
  }
}


resource "aws_cloudwatch_log_group" "alb_waf_acl_logs" {
  name              = "aws-waf-logs-alb-acl"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/waf/alb-acl-logs"
    Product     = "compliance"
    Team        = "infra"
    Environment = "prod"
    Application = "waf"
  }
}

resource "aws_cloudwatch_log_group" "ravenclaw_alb_waf_acl_logs" {
  name              = "aws-waf-logs-ravenclaw-alb-acl"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/waf/ravenclaw-alb-acl-logs"
    Product     = "compliance"
    Team        = "infra"
    Environment = "prod"
    Application = "waf"
  }
}

resource "aws_cloudwatch_log_group" "vpc_flow_logs" {
  name              = "/aws/vpc/flow-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/waf/flow-logs"
    Product     = "compliance"
    Team        = "infra"
    Environment = "prod"
    Application = "vpc"
  }
}

resource "aws_cloudwatch_log_group" "vpc_flow_logs_func" {
  name              = "/aws/lambda/vpc-flow-logs-func"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/vpc-flow-logs-func"
    Product     = "compliance"
    Team        = "infra"
    Environment = "prod"
    Application = "lambda"
  }
}

resource "aws_cloudwatch_log_group" "gosmtp_nlb_s3_logs_group" {
  name              = "/aws/nlb/gosmtp/access-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/nlb/gosmtp/access-logs"
    Product     = "compliance"
    Team        = "infra"
    Environment = "prod"
    Application = "gosmtp"
  }
}

resource "aws_cloudwatch_log_group" "gosmtp_nlb_s3_logs_func" {
  name              = "/aws/lambda/gosmtp-nlb-access-logs-to-cloudwatch"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/gosmtp-nlb-access-logs-to-cloudwatch"
    Product     = "compliance"
    Team        = "infra"
    Environment = "prod"
    Application = "lambda"
  }
}
