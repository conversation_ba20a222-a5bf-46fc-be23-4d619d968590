resource "aws_cloudwatch_log_group" "bff" {
  name              = "/ecs/bff-dev"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-bff-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "bff"
  }
}

resource "aws_cloudwatch_log_group" "remediator" {
  name              = "/ecs/remediator-dev"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-remediator-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "remediator"
  }
}

resource "aws_cloudwatch_log_group" "ml_inference" {
  name              = "/ecs/ml-inference-dev"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-ml-inference-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "ml-inference"
  }
}

resource "aws_cloudwatch_log_group" "ingestion" {
  name              = "/ecs/ingestion"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-ingestion-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "ingestion"
  }
}

resource "aws_cloudwatch_log_group" "gotenberg" {
  name              = "/ecs/gotenberg-dev"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-gotenberg-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "bff"
  }
}

resource "aws_cloudwatch_log_group" "gateway" {
  name              = "/ecs/gateway-dev"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-gateway-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "gateway"
  }
}

resource "aws_cloudwatch_log_group" "setup" {
  name              = "/ecs/setup-dev"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-setup-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "setup"
  }
}

resource "aws_cloudwatch_log_group" "ti_go_service" {
  name              = "/ecs/ti-go-service"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-ti-go-service-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "ti-go-service"
  }
}

resource "aws_cloudwatch_log_group" "keycloak" {
  name              = "keycloak-ecs-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "ecs-keycloak-dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "keycloak"
  }
}

resource "aws_cloudwatch_log_group" "prometheus" {
  name              = "nats-prometheus"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "nats-prometheus-exporters"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "prometheus"
  }
}

# resource "aws_cloudwatch_log_group" "nats_server" {
#   name              = "nats-server.log"
#   skip_destroy      = true
#   log_group_class   = "STANDARD"
#   retention_in_days = 90

#   tags = {
#     Name        = "nats-server.log"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Environment = "dev"
#     Application = "nats"
#   }
# }

resource "aws_cloudwatch_log_group" "ravenclaw_rds" {
  name              = "/aws/rds/instance/ravenclaw/postgresql"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/rds/instance/ravenclaw/postgresql"
    Product     = "ravenclaw"
    Team        = "infra"
    Environment = "dev"
    Application = "rds"
  }
}

resource "aws_cloudwatch_log_group" "bedrock_invocation" {
  name              = "/aws/bedrock/invocation"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/bedrock/invocation"
    Product     = "test"
    Team        = "infra"
    Environment = "dev"
    Application = "bedrock"
  }
}

resource "aws_cloudwatch_log_group" "cloudtrail" {
  name              = "/aws/cloudtrail/logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/cloudtrail/logs"
    Product     = "AWS"
    Team        = "infra"
    Environment = "dev"
    Application = "cloudtrail"
  }
}

# resource "aws_cloudwatch_log_group" "cloudfront_waf_acl_logs" {
#   name              = "aws-waf-logs-cloudfront-acl"
#   skip_destroy      = true
#   log_group_class   = "STANDARD"
#   retention_in_days = 90

#   tags = {
#     Name        = "/aws/waf/cloudfront-acl-logs"
#     Product     = "compliance"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "waf"
#   }
# }
