resource "aws_s3_bucket" "sagemaker_model" {
  bucket = "sagemaker-ravenmail-model-dev"

  tags = {
    Name        = "sagemaker-model-dev"
    Environment = "dev"
    Product     = "ml"
    Team        = "infra"
  }
}

resource "aws_s3_bucket_versioning" "sagemaker_model" {
  bucket = aws_s3_bucket.sagemaker_model.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_ownership_controls" "sagemaker_model" {
  bucket = aws_s3_bucket.sagemaker_model.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "sagemaker_model" {
  depends_on = [aws_s3_bucket_ownership_controls.sagemaker_model]

  bucket = aws_s3_bucket.sagemaker_model.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "sagemaker_model" {
  bucket = aws_s3_bucket.sagemaker_model.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}


resource "aws_s3_bucket_server_side_encryption_configuration" "sagemaker_model" {
  bucket = aws_s3_bucket.sagemaker_model.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.dev-misc-s3.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

########################## IAM role for sagemaker ####################
data "aws_caller_identity" "current" {}

data "aws_iam_policy_document" "sagemaker_assume_role" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["sagemaker.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "sagemaker_policy" {
  statement {
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:ListBucket"
    ]
    resources = [
      "*"
    ]
  }

  statement {
    actions = [
      "ecr:GetAuthorizationToken",
      "ecr:BatchCheckLayerAvailability",
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage"
    ]
    resources = ["*"]
  }

  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    resources = ["*"]
  }

  statement {
    actions = [
      "kms:*",
    ]
    resources = ["*"]
  }
}

resource "aws_iam_role" "sagemaker_model" {
  name               = "email-classification-dev-sagemaker-execution-role"
  assume_role_policy = data.aws_iam_policy_document.sagemaker_assume_role.json
}

resource "aws_iam_role_policy" "sagemaker_model" {
  name   = "email-classification-dev-sagemaker-policy"
  role   = aws_iam_role.sagemaker_model.id
  policy = data.aws_iam_policy_document.sagemaker_policy.json
}

resource "aws_iam_role_policy_attachment" "sagemaker_model" {
  role       = aws_iam_role.sagemaker_model.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSageMakerFullAccess"
}

# SageMaker Model Package Group (Registry)
resource "aws_sagemaker_model_package_group" "model_registry" {
  model_package_group_name        = "email-type-classification-model-registry-dev"
  model_package_group_description = "Model registry for email type classification in dev environment"


  tags = {
    Name        = "email-type-classification-model-registry-dev"
    Environment = "prod"
    Product     = "ml"
    Team        = "infra"
  }
}

# resource "aws_sagemaker_model" "email_type_classification_model" {
#   name               = "email-type-classification-model-dev"
#   execution_role_arn = aws_iam_role.sagemaker_model.arn
#
#   primary_container {
#     mode           = "SingleModel"
#     model_data_url = "s3://sagemaker-ravenmail-model-dev/email_communication_type/email_type_classification_3_classes.tar.gz"
#   }
#
#   # vpc_config = module.vpc_main.vpc_id
#
#   tags = {
#     Name        = "email-type-classification-model-dev"
#     Environment = "dev"
#     Team        = "infra"
#     Product     = "ml"
#   }
# }
