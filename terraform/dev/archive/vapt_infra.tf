# resource "aws_security_group" "vapt" {
#   name        = "windows-rdp-access"
#   description = "Allow RDP access"
#   vpc_id      = module.vpc_main.vpc_id

#   ingress {
#     from_port   = 3389
#     to_port     = 3389
#     protocol    = "tcp"
#     cidr_blocks = ["0.0.0.0/0"]
#   }

#   ingress {
#     from_port   = 22
#     to_port     = 22
#     protocol    = "tcp"
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#   }

#   ingress {
#     from_port   = 139
#     to_port     = 139
#     protocol    = "tcp"
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#   }

#   ingress {
#     from_port   = 445
#     to_port     = 445
#     protocol    = "tcp"
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#   }

#   egress {
#     from_port   = 0
#     to_port     = 0
#     protocol    = "-1"
#     cidr_blocks = ["0.0.0.0/0"]
#   }

#   tags = {
#     Name        = "vapt-windows-rdp-access"
#     Environment = "dev"
#     Team        = "infra"
#     Product     = "VAPT"
#   }
# }

# resource "tls_private_key" "vapt" {
#   algorithm = "RSA"
#   rsa_bits  = 4096
# }

# resource "aws_key_pair" "vapt" {
#   key_name   = "vapt-ec2-dev"
#   public_key = tls_private_key.vapt.public_key_openssh
# }

# resource "local_file" "vapt" {
#   content  = tls_private_key.vapt.private_key_pem
#   filename = "vapt-dev.pem"
# }

# module "vapt_ec2" {
#   source                        = "../modules/ec2_instance"
#   region                        = var.region
#   availability_zone             = "us-east-1c"
#   instance_type                 = "t3a.large"
#   root_block_device_volume_size = 100
#   root_block_device_iops        = 3000
#   root_block_device_throughput  = 125
#   root_block_device_volume_type = "gp3"
#   subnet_id                     = module.main_public_subnet_3.subnet_id
#   vpc_security_group_ids        = ["${aws_security_group.vapt.id}"]
#   key_name                      = aws_key_pair.vapt.key_name
#   associate_public_ip_address   = true
#   ami                           = "ami-09ec59ede75ed2db7"
#   root_block_device_encrypted   = true

#   tags = {
#     Name        = "vapt-instance"
#     Team        = "infra"
#     Product     = "compliance"
#     Environment = "dev"
#   }
# }
