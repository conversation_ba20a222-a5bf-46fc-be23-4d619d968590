module "ecs_alb_loadbalancer_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "ecs_alb_loadbalancer_sg_new"
  description = "For ECS ALB"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.alb_sg_tags

  ingress_ipv4_cidrs                    = ["0.0.0.0/0"]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 443
  ingress_to_port                       = 443
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  ingress_custom_rules = [
    {
      cidr_ipv4   = ["0.0.0.0/0"]
      cidr_ipv6   = []
      from_port   = 80
      to_port     = 80
      ip_protocol = "tcp"
      description = ""
    }
  ]

  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
}

module "internal_alb_loadbalancer_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "internal_alb_loadbalancer_sg_new"
  description = "For Internal ALB"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "internal-alb-sg"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "dev"
  }

  # ───────────────────────────────────────────────────
  # Ingress: HTTPS (443) from VPC IPv4 only
  # ───────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 443
  ingress_to_port                       = 443
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  # ───────────────────────────────────────────────────
  # Custom ingress: HTTP (80) from VPC IPv4 only
  # ───────────────────────────────────────────────────
  ingress_custom_rules = [
    {
      cidr_ipv4   = [var.vpc_cidr_block]
      cidr_ipv6   = []
      from_port   = 80
      to_port     = 80
      ip_protocol = "tcp"
      description = ""
    }
  ]

  # ───────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}
