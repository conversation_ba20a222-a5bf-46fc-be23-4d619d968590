# EFS File System
resource "aws_efs_file_system" "logs" {
  creation_token = "ecs-log-test"
  
  encrypted = true
  
  tags = {
    Name        = "ecs-log-test"
    Environment = "dev"
		Team = "infra"
		Product = "monitoring"
  }
}

resource "aws_efs_mount_target" "logs" {
  file_system_id  = aws_efs_file_system.logs.id
  subnet_id       = module.main_private_subnet_1.subnet_id
  security_groups = [aws_security_group.efs_logs.id]
}


resource "aws_efs_mount_target" "log-2" {
  file_system_id  = aws_efs_file_system.logs.id
  subnet_id       = module.main_private_subnet_2.subnet_id
  security_groups = [aws_security_group.efs_logs.id]
}


resource "aws_efs_mount_target" "logs-3" {
  file_system_id  = aws_efs_file_system.logs.id
  subnet_id       = module.main_private_subnet_3.subnet_id
  security_groups = [aws_security_group.efs_logs.id]
}

# Security Group for EFS
resource "aws_security_group" "efs_logs" {
  name_prefix = "efs-logs-sg"
  vpc_id      = module.vpc_main.vpc_id
  
  ingress {
    from_port   = 2049
    to_port     = 2049
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr_block]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = {
    Name = "efs-logs-sg"
  }
}

resource "aws_efs_access_point" "logs" {
  file_system_id = aws_efs_file_system.logs.id
  
  posix_user {
    gid = 1000
    uid = 1000
  }
  
  root_directory {
    path = "/app"
    creation_info {
      owner_gid   = 1000
      owner_uid   = 1000
      permissions = "755"
    }
  }
  
  tags = {
    Name = "ecs-log-test"
		Environment = "dev"
		Team = "infra"
		Product = "monitoring"
  }
}