provider "aws" {
  region = var.region
}

# provider "cloudflare" {
#   api_token = var.cloudflare_api_token
# }

terraform {

  backend "s3" {
    bucket                   = "terraform-dev-infra-2024"
    key                      = "dev-tf"
    region                   = "us-east-1"
    shared_credentials_files = ["~/.aws/credentials"]
  }

  required_providers {
    aws = {
      version = ">= 5.85.0"
    }
  }
}
