module "bff_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "bff-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id
  container_definitions_json = jsonencode([
    {
      "cpu" : 0,
      "command" : [
        "bff"
      ],
      "secrets" : [
        {
          "name" : "NATS_SERVER_URL",
          "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
          }, {
          "name" : "KEYCLOAK_ADMIN_CLIENT_ID",
          "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_ADMIN_CLIENT_ID.arn
          }, {
          "name" : "KEYCLOAK_ADMIN_CLIENT_SECRET",
          "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_ADMIN_CLIENT_SECRET.arn
          }, {
          "name" : "KEYCLOAK_SERVICE_ACCOUNT_ID",
          "valueFrom" : aws_secretsmanager_secret.KEYCLOAK_SERVICE_ACCOUNT_ID.arn
          }, {
          "name" : "MS_AUTH_CLIENT_ID",
          "valueFrom" : aws_secretsmanager_secret.MS_AUTH_CLIENT_ID.arn
          }, {
          "name" : "MS_AUTH_CLIENT_SECRET",
          "valueFrom" : aws_secretsmanager_secret.MS_AUTH_CLIENT_SECRET.arn
          }, {
          "name" : "MS_AUTH_TENANT_ID",
          "valueFrom" : aws_secretsmanager_secret.MS_AUTH_TENANT_ID.arn
          }, {
          "name" : "GW_AUTH_CLIENT_ID",
          "valueFrom" : aws_secretsmanager_secret.GW_AUTH_CLIENT_ID.arn
          }, {
          "name" : "GW_AUTH_CLIENT_SECRET",
          "valueFrom" : aws_secretsmanager_secret.GW_AUTH_CLIENT_SECRET.arn
          }, {
          "name" : "GW_SERVICE_ACCOUNT",
          "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
          }, {
          "name" : "JWT_SECRET",
          "valueFrom" : aws_secretsmanager_secret.JWT_SECRET.arn
      }],
      "environment" : [{
        "name" : "LOG_ROTATION_FILE",
        "value" : "bff.log"
      },{
        "name" : "BFF_HOST",
        "value" : var.BFF_HOST
        }, {
        "name" : "BFF_PORT",
        "value" : var.BFF_PORT
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_PASSWORD",
        "value" : module.rds.db_password
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "PG_SSL",
        "value" : var.PG_SSL
        }, {
        "name" : "AWS_REGION",
        "value" : var.region
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "PG_MAX_OPEN_CONNECTION",
        "value" : var.PG_MAX_OPEN_CONNECTION
        }, {
        "name" : "PG_MAX_IDLE_CONNECTION",
        "value" : var.PG_MAX_IDLE_CONNECTION
        }, {
        "name" : "BFF_GOTENBERG_HOST",
        "value" : "http://localhost:3000"
        }, {
        "name" : "KEYCLOAK_BASE_URL",
        "value" : "https://auth.dev.ravenmail.io"
        }, {
        "name" : "UI_BASE_URL",
        "value" : "https://dev.dashboard.ravenmail.io"
        }, {
        "name" : "MS_GRAPH_BASE_URL",
        "value" : "https://graph.microsoft.com/v1.0"
        }, {
        "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
        "value" : "false",
        }, {
        "name" : "BFF_PUBLIC_HOST",
        "value" : "https://alpha.ravenclaw.ravenmail.io"
        }, {
        "name" : "BFF_PERM_CHECK_RETRY",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "10"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "60"
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.dev-orgs.arn
        }, {
        "name" : "BFF_USE_PRODUCT_TABLES",
        "value" : "true"
        }, {
        "name" : "BFF_SWAGGER_BASE_URL",
        "value" : "http://swagger.ravenclaw-dev-ns:8082"
        }, {
        "name" : "MAIL_ALERT_SENDER_ADDRESS",
        "value" : "Raven AI alerts<<EMAIL>>"
        }, {
        "name" : "USERPORTAL_BASE_URL",
        "value" : "https://dev.portal.ravenmail.io"
      }],

      "environmentFiles" : [],
      "essential" : true,
      "image" : var.bff_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : var.bff_logs_group,
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "secretOptions" : [],
      "mountPoints" : [{
      "sourceVolume" : "bff-logs",
      "containerPath" : "/root/ravenclaw",
      "readOnly" : false
    }],
      "name" : var.bff_docker_image_name,
      "portMappings" : [{
        "appProtocol" : "http",
        "containerPort" : 8080,
        "hostPort" : 8080,
        "name" : "http",
        "protocol" : "tcp"
      }],
      "systemControls" : [],
      "volumesFrom" : [],
      "ulimits" : []
      }, {
      "cpu" : 0,
      "secrets" : [{
        "name" : "NATS_SERVER_URL",
        "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      }, ],
      "environment" : [{
        "name" : "BFF_HOST",
        "value" : var.BFF_HOST
        }, {
        "name" : "BFF_PORT",
        "value" : var.BFF_PORT
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_PASSWORD",
        "value" : module.rds.db_password
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "PG_SSL",
        "value" : var.PG_SSL
        }, {
        "name" : "AWS_REGION",
        "value" : var.region
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "PG_MAX_OPEN_CONNECTION",
        "value" : var.PG_MAX_OPEN_CONNECTION
        }, {
        "name" : "PG_MAX_IDLE_CONNECTION",
        "value" : var.PG_MAX_IDLE_CONNECTION
      }],
      "environmentFiles" : [],
      "essential" : true,
      "image" : var.gotenberg_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : var.gotenberg_logs_group,
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "secretOptions" : [],
      "mountPoints" : [],
      "name" : var.gotenberg_docker_image_name,
      "portMappings" : [{
        "appProtocol" : "http",
        "containerPort" : 3000,
        "hostPort" : 3000,
        "name" : "http-gotenberg",
        "protocol" : "tcp"
      }],
      "systemControls" : [],
      "volumesFrom" : [],
      "ulimits" : []
  }])

  cpu                = var.bff_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "bff"
  memory             = var.bff_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.bff_task_def_tags
}

module "gateway_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "gateway-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id

  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "gateway",
      "start"
    ],
    "secrets" : [{
      "name" : "GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET",
      "valueFrom" : aws_secretsmanager_secret.GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET.arn
      }, {
      "name" : "NATS_SERVER_URL",
      "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      }, {
      "name" : "GW_SERVICE_ACCOUNT",
      "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
      }, {
      "name" : "GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC",
      "valueFrom" : aws_secretsmanager_secret.GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC.arn
    }],
    "environment" : [
      {
        name = "LOG_ROTATION_FILE"
        value = "gateway.log"
      },
      {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_HOST",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_HOST
      }, {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_PATH",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_PATH
      }, {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_PORT",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_PORT
      }, {
      "name" : "GATEWAY_PUBLIC_HOST",
      "value" : var.GATEWAY_PUBLIC_HOST
      }, {
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      }, {
      "name" : "PG_PASSWORD",
      "value" : module.rds.db_password
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "PG_SSL",
      "value" : var.PG_SSL
      }, {
      "name" : "AWS_REGION",
      "value" : var.region
      }, {
      "name" : "DEPLOYMENT_ENV",
      "value" : var.DEPLOYMENT_ENV
      }, {
      "name" : "GATEWAY_MICROSOFT_LIFECYCLE_PATH",
      "value" : var.GATEWAY_MICROSOFT_LIFECYCLE_PATH
      }, {
      "name" : "PG_MAX_OPEN_CONNECTION",
      "value" : var.PG_MAX_OPEN_CONNECTION
      }, {
      "name" : "PG_MAX_IDLE_CONNECTION",
      "value" : var.PG_MAX_IDLE_CONNECTION
      }, {
      "name" : "MS_GRAPH_BASE_URL",
      "value" : "https://graph.microsoft.com/v1.0"
      }, {
      "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
      "value" : "false"
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "10"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "10"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "60"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
      }, {
      "name" : "SEED_ORGANIZATION_NAME",
      "value" : "ravenmail"
      }, {
      "name" : "GATEWAY_GOOGLE_WEBHOOK_PATH",
      "value" : "/v0/hooks/google"
      }, {
      "name" : "S3_KMS_KEY_ARN",
      "value" : aws_kms_key.dev-orgs.arn
      }, {
      "name" : "GATEWAY_MAIL_INGESTED_SCHEMA_VERSION",
      "value" : "new"
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.gateway_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.gateway_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [{
      "sourceVolume" : "gateway-logs",
      "containerPath" : "/root/ravenclaw",
      "readOnly" : false
    }],
    "name" : var.gateway_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8081,
      "hostPort" : 8081,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }, 
  # {
  #   name      = "otel-collector"
  #   image     = var.otel_collector_image_uri
  #   essential = true
  #   "secrets" : [
  #     {
  #       "name" : "CLICKSTACK_PASSWORD",
  #       "valueFrom" : aws_secretsmanager_secret.CLICKSTACK_PASSWORD.arn
  #       }, {
  #       "name" : "CLICKSTACK_USER",
  #       "valueFrom" : aws_secretsmanager_secret.CLICKSTACK_USER.arn
  #     }
  #   ],
  #   "environment" : [
  #     { name = "SERVICE_NAME", value = "gateway" },
  #     { name = "LOG_FILE_PATH", value = "/root/ravenclaw/gateway.gateway.log" },
  #     { name = "CLICKSTACK_ENDPOINT", value = "clickstack.ravenclaw-dev-ns" },
  #     {name = "HYPERDX_OTEL_EXPORTER_CLICKHOUSE_DATABASE", value = "observability"}
  #   ],
  #   # user = "0",
  #   "mountPoints" : [
  #     {
  #       "sourceVolume" : "gateway-logs",
  #       "containerPath" : "/root/ravenclaw",
  #       "readOnly" : false
  #     }
  #   ],
  #   "volumesFrom": [],
  #   logConfiguration = {
  #     logDriver = "awslogs"
  #     options   = {
  #       awslogs-create-group  = "true"
  #       awslogs-group         = "${var.gateway_logs_group}-otel"
  #       awslogs-region        = var.region
  #       awslogs-stream-prefix = "ecs"
  #     }
  #   }
  # }
  ])

  cpu                = var.gateway_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "gateway"
  memory             = var.gateway_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.gateway_task_def_tags
}

module "remediator_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "remediator-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id

  container_definitions_json = jsonencode([{
    "cpu" : var.remediator_cpu,
    "command" : [
      "remediator"
    ],
    "secrets" : [{
      "name" : "INTENT_EXTRACTION_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.INTENT_EXTRACTION_MODEL_API_KEY.arn
      }, {
      "name" : "NATS_SERVER_URL",
      "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      }, {
      "name" : "GW_SERVICE_ACCOUNT",
      "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
      }, {
      "name" : "SPLUNK_ORGANIZATIONS",
      "valueFrom" : aws_secretsmanager_secret.SPLUNK_ORGANIZATIONS_KEY.arn
    }],
    "environment" : [{
      "name" : "LOG_ROTATION_FILE",
      "value" : "${var.remediator_LOG_ROTATION_FILE}"
      }, {
      "name" : "LOG_ROTATION_MAX_AGE",
      "value" : "${var.remediator_LOG_ROTATION_MAX_AGE}"
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_ENDPOINT",
      "value" : var.INTENT_EXTRACTION_MODEL_ENDPOINT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_API_VERSION",
      "value" : var.INTENT_EXTRACTION_MODEL_API_VERSION
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_DEPLOYMENT",
      "value" : var.INTENT_EXTRACTION_MODEL_DEPLOYMENT
      }, {
      "name" : "INTENT_EXTRACTION_RETRIES",
      "value" : var.INTENT_EXTRACTION_RETRIES
      }, {
      "name" : "INTENT_EXTRACTION_BACKOFF_IN_SEC",
      "value" : var.INTENT_EXTRACTION_BACKOFF_IN_SEC
      }, {
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      }, {
      "name" : "PG_PASSWORD",
      "value" : module.rds.db_password
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "PG_SSL",
      "value" : var.PG_SSL
      }, {
      "name" : "AWS_REGION",
      "value" : var.region
      }, {
      "name" : "DEPLOYMENT_ENV",
      "value" : var.DEPLOYMENT_ENV
      }, {
      "name" : "PG_MAX_OPEN_CONNECTION",
      "value" : var.PG_MAX_OPEN_CONNECTION
      }, {
      "name" : "PG_MAX_IDLE_CONNECTION",
      "value" : var.PG_MAX_IDLE_CONNECTION
      }, {
      "name" : "MS_GRAPH_BASE_URL",
      "value" : "https://graph.microsoft.com/v1.0"
      }, {
      "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
      "value" : "false"
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "10"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "10"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "30"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
      }, {
      "name" : "UI_BASE_URL",
      "value" : "https://dev.dashboard.ravenmail.io"
      }, {
      "name" : "S3_KMS_KEY_ARN",
      "value" : aws_kms_key.dev-orgs.arn
      }, {
      "name" : "BFF_PUBLIC_HOST",
      "value" : "https://alpha.ravenclaw.ravenmail.io"
      }, {
      "name" : "MAIL_ALERT_SENDER_ADDRESS",
      "value" : "Raven AI alerts<<EMAIL>>"
      }, {
      "name" : "SPLUNK_LOG_ROTATION_FILE",
      "value" : "splunk.log",
      }, {
      "name" : "SPLUNK_LOG_ROTATION_MAX_SIZE",
      "value" : "512"
      }, {
      "name" : "SPLUNK_LOG_ROTATION_MAX_BACKUPS",
      "value" : "90"
      }, {
      "name" : "SPLUNK_LOG_ROTATION_MAX_AGE",
      "value" : "120"
      }, {
      "name" : "USERPORTAL_BASE_URL",
      "value" : "https://dev.portal.ravenmail.io"
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.remediator_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.remediator_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [{
      "sourceVolume" : "remediator-logs",
      "containerPath" : "/root/ravenclaw",
      "readOnly" : false
    }],
    "name" : var.remediator_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8081,
      "hostPort" : 8081,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.remediator_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "remediator"
  memory             = var.remediator_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.remediator_task_def_tags
}

module "setup_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "setup"
    ],
    secrets = [
      {
        name      = "NATS_SERVER_URL",
        valueFrom = aws_secretsmanager_secret.NATS_SERVER_URL.arn
        }, {
        name      = "GW_AUTH_CLIENT_ID",
        valueFrom = aws_secretsmanager_secret.GW_AUTH_CLIENT_ID.arn
        }, {
        name      = "GW_AUTH_CLIENT_SECRET",
        valueFrom = aws_secretsmanager_secret.GW_AUTH_CLIENT_SECRET.arn
      }
    ],
    "environment" : [
      {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_PASSWORD",
        "value" : module.rds.db_password
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "PG_SSL",
        "value" : var.PG_SSL
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "10"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "30"
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.dev-orgs.arn
      }
    ],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.setup_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.setup_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.setup_docker_image_name,
    "portMappings" : [],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.setup_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "setup"
  memory             = var.setup_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.setup_task_def_tags
}

module "ml_inference_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "secrets" : [{
      "name" : "INTENT_EXTRACTION_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.INTENT_EXTRACTION_MODEL_API_KEY.arn
      }, {
      "name" : "DETECTION_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.DETECTION_MODEL_API_KEY.arn
      }, {
      "name" : "DETECTION_MODEL_API_VERSION",
      "valueFrom" : aws_secretsmanager_secret.DETECTION_MODEL_API_VERSION.arn
      }, {
      "name" : "ATTACHMENT_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_MODEL_API_KEY.arn
    }, ],
    "environment" : [{
      "name" : "EXTRACTION_MODEL_PATH",
      "value" : var.EXTRACTION_MODEL_PATH
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_DEPLOYMENT",
      "value" : var.INTENT_EXTRACTION_MODEL_DEPLOYMENT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_ENDPOINT",
      "value" : var.INTENT_EXTRACTION_MODEL_ENDPOINT
      }, {
      "name" : "INTENT_EXTRACTION_MODEL_API_VERSION",
      "value" : var.INTENT_EXTRACTION_MODEL_API_VERSION
      }, {
      "name" : "SPACY_ENTITY_MODEL_PATH",
      "value" : var.SPACY_ENTITY_MODEL_PATH
      }, {
      "name" : "AZURE_LLM_API_VERSION",
      "value" : "2024-02-15-preview"
      }, {
      "name" : "DETECTION_MODEL_ENDPOINT",
      "value" : "https://detectio-model-ptu.openai.azure.com"
      }, {
      "name" : "DETECTION_MODEL_DEPLOYMENT",
      "value" : "detectionmodel"
      }, {
      "name" : "DETECTION_MODEL_DEPLOYMENT_WITH_TOKEN_DEPLOYMENT",
      "value" : "detection-model-token-deployment"
      }, {
      "name" : "LLM_API_CALL_RETRY_ATTEMPT_COUNT",
      "value" : "5"
      }, {
      "name" : "ATTACHMENT_MODEL_ENDPOINT",
      "value" : "https://attachment-analysis.openai.azure.com/"
      }, {
      "name" : "ATTACHMENT_MODEL_DEPLOYMENT",
      "value" : "gpt-4o-mini"
      }
    ],
    "command" : [
      "poetry",
      "run",
      "fastapi",
      "run",
      "--workers",
      "2",
      "/app/server/fastapi-server.py"
    ],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.ml_inference_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.ml_inference_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.ml_inference_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8000,
      "hostPort" : 8000,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.ml_inference_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "ml-inference"
  memory             = var.ml_inference_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.ml_inference_task_def_tags
}

module "ingestion_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "historical-ingestion"
    ],
    "environment" : [
      {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_PASSWORD",
        "value" : module.rds.db_password
        }, {
        "name" : "PG_PORT",
        "value" : var.PG_PORT
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "PG_SSL",
        "value" : var.PG_SSL
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "MS_GRAPH_BASE_URL",
        "value" : "https://graph.microsoft.com/v1.0"
        }, {
        "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
        "value" : "false"
        }, {
        "name" : "ML_INFERENCE_SERVICE_URL",
        "value" : "http://ml_inference.ravenclaw-dev-ns:8000"
        }, {
        "name" : "TI_URL_SCAN_LIMIT",
        "value" : "5"
        }, {
        "name" : "ENABLE_INGESTION_DETECT_VENDOR",
        "value" : "true"
        }, {
        "name" : "SENDER_ANALYSIS_IP_ANOMALY_THRESHOLD",
        "value" : "2"
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.dev-orgs.arn
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "10"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "60"
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "ATTACHMENT_MODEL_ENDPOINT",
        "value" : "https://attachment-analysis.openai.azure.com/"
      },
      {
        "name" : "ATTACHMENT_MODEL_DEPLOYMENT",
        "value" : "gpt-4o-mini"
        }, {
        "name" : "AZURE_LLM_API_VERSION",
        "value" : "2024-02-15-preview"
      },
    ],
    "secrets" : [
      {
        "name" : "GW_SERVICE_ACCOUNT",
        "valueFrom" : aws_secretsmanager_secret.GW_SERVICE_ACCOUNT_INGESTION.arn
      },
      {
        "name" : "ATTACHMENT_MODEL_API_KEY",
        "valueFrom" : "arn:aws:secretsmanager:us-east-1:************:secret:ATTACHMENT_MODEL_API_KEY-7x86iV"
        }, {
        "name" : "NATS_SERVER_URL",
        "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      },

    ]
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.ingestion_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.ingestion_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.ingestion_docker_image_name,
    "portMappings" : [],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.ingestion_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "ingestion"
  memory             = var.ingestion_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.ingestion_task_def_tags
}

module "ti_go_service_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "ti-go-service-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : ["ti"],
    "secrets" : [{
      "name" : "NATS_SERVER_URL",
      "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
      }, {
      "name" : "INTENT_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.INTENT_EXTRACTION_MODEL_API_KEY.arn
      }, {
      "name" : "VIRUSTOTAL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.VIRUSTOTAL_API_KEY.arn
      }, {
      "name" : "ATTACHMENT_MODEL_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_MODEL_API_KEY.arn
      }, {
      "name" : "DLP_MODEL_CREDENTIALS",
      "valueFrom" : aws_secretsmanager_secret.DLP_MODEL_CREDENTIALS.arn
      }, {
      "name" : "SPLUNK_ORGANIZATIONS",
      "valueFrom" : aws_secretsmanager_secret.SPLUNK_ORGANIZATIONS_KEY.arn
      }, {
      "name" : "CLASSIFICATION_OAI_API_KEY",
      "valueFrom" : aws_secretsmanager_secret.CLASSIFICATION_OAI_API_KEY.arn
      }, {
      "name" : "ATTACHMENT_SCAN_ENABLED_ORGS",
      "valueFrom" : aws_secretsmanager_secret.ATTACHMENT_SCAN_ENABLED_ORGS.arn
    }],
    "environment" : [ 
      {
        name = "LOG_ROTATION_FILE"
        value = "ti.log"
      },
      {
      "name" : "ASYNC_MAX_AT_ONCE_VT"
      "value" : var.ASYNC_MAX_AT_ONCE_VT
      }, {
      "name" : "ASYNC_MAX_PER_SECOND_VT"
      "value" : var.ASYNC_MAX_PER_SECOND_VT
      }, {
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      }, {
      "name" : "PG_PASSWORD",
      "value" : module.rds.db_password
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "ML_INFERENCE_SERVICE_URL",
      "value" : "http://internal-dev-alb-161840175.us-east-1.elb.amazonaws.com"
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "10"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "10"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "60"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
      }, {
      "name" : "TI_URL_SCAN_LIMIT",
      "value" : "5"
      },
      {
        "name" : "SENDER_ANALYSIS_RECURRING_INVOICE_THRESHOLD",
        "value" : "2"
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : "dev"
      },
      {
        "name" : "ENABLE_INGESTION_DETECT_VENDOR",
        "value" : "false"
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.dev-orgs.arn
        }, {
        "name" : "ATTACHMENT_SCAN",
        "value" : "false"
        }, { "name" : "ATTACHMENT_MODEL_ENDPOINT",
        "value" : "https://attachment-analysis.openai.azure.com/"
        }, {
        "name" : "ATTACHMENT_MODEL_API_VERSION",
        "value" : "2024-02-15-preview"
        }, {
        "name" : "ATTACHMENT_MODEL_DEPLOYMENT",
        "value" : "gpt-4o-mini"
        }, {
        "name" : "NEW_VENDOR_SCAN",
        "value" : "false"
        }, {
        "name" : "THREAT_RULES",
        "value" : "true"
        }, {
        "name" : "SPLUNK_LOG_ROTATION_FILE",
        "value" : "splunk.log",
        }, {
        "name" : "SPLUNK_LOG_ROTATION_MAX_SIZE",
        "value" : "512"
        }, {
        "name" : "SPLUNK_LOG_ROTATION_MAX_BACKUPS",
        "value" : "90"
        }, {
        "name" : "SPLUNK_LOG_ROTATION_MAX_AGE",
        "value" : "120"
        }, {
        "name" : "AZURE_LLM_API_VERSION",
        "value" : "2024-02-15-preview"
        }, {
        "name" : "CLASSIFICATION_OAI_DEPLOYMENT_ID",
        "value" : "gpt-4o-with-structured-outputs"
        }, {
        "name" : "CLASSIFICATION_OAI_ENDPOINT",
        "value" : "https://email-data-task.openai.azure.com"
        }, {
        "name" : "CLASSIFICATION_OAI_API_VERSION",
        "value" : "2024-10-21"
        }, {
        "name" : "INTENT_EXTRACTION_MODEL_ENDPOINT",
        "value" : var.INTENT_EXTRACTION_MODEL_ENDPOINT
        }, {
        "name" : "INTENT_EXTRACTION_MODEL_API_VERSION",
        "value" : var.INTENT_EXTRACTION_MODEL_API_VERSION
        }, {
        "name" : "INTENT_EXTRACTION_MODEL_DEPLOYMENT",
        "value" : var.INTENT_EXTRACTION_MODEL_DEPLOYMENT
        }, {
        "name" : "INTENT_EXTRACTION_RETRIES",
        "value" : var.INTENT_EXTRACTION_RETRIES
        }, {
        "name" : "INTENT_EXTRACTION_BACKOFF_IN_SEC",
        "value" : var.INTENT_EXTRACTION_BACKOFF_IN_SEC
      }
    ],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.ti_go_service_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.ti_go_service_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [
      {
        "sourceVolume" : "ti-go-service-logs",
        "containerPath" : "/root/ravenclaw",
        "readOnly" : false
      }
    ],
    "name" : var.ti_go_service_docker_image_name,
    "portMappings" : [],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.ti_go_service_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "ti-go-service"
  memory             = var.ti_go_service_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.ti_go_service_task_def_tags
}

module "gateway_subcommand_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "gateway"
    ],
    "secrets" : [
      {
        "name" : "GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET",
        "valueFrom" : aws_secretsmanager_secret.GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET.arn
        }, {
        "name" : "NATS_SERVER_URL",
        "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
        }, {
        "name" : "GW_SERVICE_ACCOUNT",
        "valueFrom" : aws_secretsmanager_secret.GATEWAY_SUBCOMMAND_GW_SERVICE_ACCOUNT.arn
      }
    ],
    "environment" : [{
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_HOST",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_HOST
      }, {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_PATH",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_PATH
      }, {
      "name" : "GATEWAY_MICROSOFT_WEBHOOK_PORT",
      "value" : var.GATEWAY_MICROSOFT_WEBHOOK_PORT
      }, {
      "name" : "GATEWAY_PUBLIC_HOST",
      "value" : var.GATEWAY_PUBLIC_HOST
      }, {
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      }, {
      "name" : "PG_PASSWORD",
      "value" : module.rds.db_password
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "PG_SSL",
      "value" : var.PG_SSL
      }, {
      "name" : "AWS_REGION",
      "value" : var.region
      }, {
      "name" : "DEPLOYMENT_ENV",
      "value" : var.DEPLOYMENT_ENV
      }, {
      "name" : "GATEWAY_MICROSOFT_LIFECYCLE_PATH",
      "value" : var.GATEWAY_MICROSOFT_LIFECYCLE_PATH
      }, {
      "name" : "PG_MAX_OPEN_CONNECTION",
      "value" : var.PG_MAX_OPEN_CONNECTION
      }, {
      "name" : "PG_MAX_IDLE_CONNECTION",
      "value" : var.PG_MAX_IDLE_CONNECTION
      }, {
      "name" : "MS_GRAPH_BASE_URL",
      "value" : "https://graph.microsoft.com/v1.0"
      }, {
      "name" : "MS_GRAPH_SKIP_TLS_VERIFICATION",
      "value" : "false"
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "10"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "10"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "60"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
      }, {
      "name" : "SEED_ORGANIZATION_NAME",
      "value" : "ravenmail"
      }, {
      "name" : "GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC",
      "value" : "projects/ravenmail-439412/topics/staging-gmail-notification"
      }, {
      "name" : "GATEWAY_GOOGLE_WEBHOOK_PATH",
      "value" : "/v0/hooks/google"
      }, {
      "name" : "S3_KMS_KEY_ARN",
      "value" : aws_kms_key.dev-orgs.arn
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.gateway_subcommand_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : var.gateway_subcommand_logs_group,
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : var.gateway_subcommand_docker_image_name,
    "portMappings" : [{
      "appProtocol" : "http",
      "containerPort" : 8081,
      "hostPort" : 8081,
      "name" : "http",
      "protocol" : "tcp"
    }],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.gateway_subcommand_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "gateway-subcommand"
  memory             = var.gateway_subcommand_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = var.gateway_subcommand_task_def_tags
}

module "inline_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  volume_name = "inline-logs"
  file_system_id = aws_efs_file_system.logs.id
  access_point_id = aws_efs_access_point.logs.id
  container_definitions_json = jsonencode([
    {
      "command" : [
        "gateway",
        "inline"
      ],
      "cpu" : 0,
      "environment" : [
        {
        "name" : "LOG_ROTATION_FILE",
        "value" : "inline.log"
        },{
        "name" : "AWS_REGION",
        "value" : var.region
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "60"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_MAX_IDLE_CONNECTION",
        "value" : "5"
        }, {
        "name" : "PG_MAX_OPEN_CONNECTION",
        "value" : "20"
        }, {
        "name" : "PG_PASSWORD",
        "value" : module.rds.db_password
        }, {
        "name" : "PG_PORT",
        "value" : "5432"
        }, {
        "name" : "PG_SSL",
        "value" : "disable"
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.dev-orgs.arn
        }, {
        "name" : "SMARTHOST_DOMAIN",
        "value" : "mail.ravenmail.cc"
        }, {
        "name" : "SMARTHOST_PORT",
        "value" : "2525"
        }, {
        "name" : "DKIM_ENABLED",
        "value" : "true"
        }, {
        "name" : "DKIM_SELECTOR",
        "value" : "devravenmail"
      }],

      "essential" : true,
      "image" : var.inline_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : var.inline_log_group,
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "mountPoints" : [{
      "sourceVolume" : "inline-logs",
      "containerPath" : "/root/ravenclaw",
      "readOnly" : false
    }],
      "name" : "gateway-inline-dev",
      "portMappings" : [
        {
          "appProtocol" : "http",
          "containerPort" : 2525,
          "hostPort" : 2525,
          "name" : "http",
          "protocol" : "tcp"
        }
      ],
      "secrets" : [
        {
          "name" : "NATS_SERVER_URL",
          "valueFrom" : aws_secretsmanager_secret.NATS_SERVER_URL.arn
        },
        {
          "name" : "SMARTHOST_TLS_CERT",
          "valueFrom" : aws_secretsmanager_secret.SMARTHOST_TLS_CERT.arn
        },
        {
          "name" : "SMARTHOST_TLS_KEY",
          "valueFrom" : aws_secretsmanager_secret.SMARTHOST_TLS_KEY.arn
        },
        {
          "name" : "DKIM_PRIVATE_KEY",
          "valueFrom" : aws_secretsmanager_secret.DKIM_PRIVATE_KEY.arn
        }
      ],
      "systemControls" : [],
      "volumesFrom" : []
    }
  ])

  cpu                      = "512"
  execution_role_arn       = aws_iam_role.task_execution_role.arn
  family                   = "gateway-inline-dev"
  memory                   = "1024"
  task_role_arn            = aws_iam_role.task_role.arn
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  track_latest             = "false"

  tags = {
    Environment = "dev"
    Name        = "ravenclaw-gateway-inline-task-definition"
    Product     = "inline"
    Team        = "infra"
  }
}
