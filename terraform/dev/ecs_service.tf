module "bff_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.bff_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.ecs_ravenclaw_bff_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.bff_service_name
  desired_count                  = var.bff_service_desired_count
  task_definition_arn            = module.bff_task_definition.arn
  target_group_arn               = aws_lb_target_group.main.arn
  container_name                 = var.bff_docker_image_name
  container_port                 = 8080

  capacity_provider_strategies = [
    {
      capacity_provider = "FARGATE"
      base              = 0
      weight            = 3
    },
    {
      capacity_provider = "FARGATE_SPOT"
      base              = 0
      weight            = 2
    }
  ]

  service_discovery_svc_tags = var.bff_service_discovery_tags

  ecs_service_tags = var.bff_service_tags
}

module "gateway_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.gateway_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.gateway_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.gateway_service_name
  desired_count                  = var.gateway_service_desired_count
  task_definition_arn            = module.gateway_task_definition.arn
  target_group_arn               = aws_lb_target_group.gateway.arn
  container_name                 = var.gateway_docker_image_name
  container_port                 = 8081

  capacity_provider_strategies = [
    {
      capacity_provider = "FARGATE"
      base              = 0
      weight            = 3
    },
    {
      capacity_provider = "FARGATE_SPOT"
      base              = 0
      weight            = 2
    }
  ]

  service_discovery_svc_tags = var.gateway_service_discovery_tags

  ecs_service_tags = var.gateway_service_tags
}

module "remediator_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.remediator_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.remediator_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.remediator_service_name
  desired_count                  = var.remediator_service_desired_count
  task_definition_arn            = module.remediator_task_definition.arn

  capacity_provider_strategies = [
    {
      capacity_provider = "FARGATE"
      base              = 0
      weight            = 3
    },
    {
      capacity_provider = "FARGATE_SPOT"
      base              = 0
      weight            = 2
    }
  ]

  service_discovery_svc_tags = var.remediator_service_discovery_tags

  ecs_service_tags = var.remediator_service_tags
}

module "ml_inference_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.ml_inference_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.ml_inference_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.ml_inference_service_name
  desired_count                  = var.ml_inference_service_desired_count
  task_definition_arn            = module.ml_inference_task_definition.arn
  target_group_arn               = aws_lb_target_group.internal_ml_inference.arn
  container_name                 = "ml-inference-dev"
  container_port                 = 8000

  capacity_provider_strategies = [
    {
      capacity_provider = "FARGATE"
      base              = 0
      weight            = 3
    },
    {
      capacity_provider = "FARGATE_SPOT"
      base              = 0
      weight            = 2
    }
  ]

  service_discovery_svc_tags = var.ml_inference_service_discovery_tags

  ecs_service_tags = var.ml_inference_service_tags
}

module "ti_go_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = var.ti_go_service_service_discovery_name
  cluster                        = module.ecs_cluster.cluster_name
  security_groups_id             = [aws_security_group.ravenclaw_ti_go_service.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = var.ti_go_service_service_name
  desired_count                  = var.ti_go_service_service_desired_count
  task_definition_arn            = module.ti_go_service_task_definition.arn
  capacity_provider_strategies = [
    {
      capacity_provider = "FARGATE"
      base              = 0
      weight            = 3
    },
    {
      capacity_provider = "FARGATE_SPOT"
      base              = 0
      weight            = 2
    }
  ]

  service_discovery_svc_tags = var.ti_go_service_discovery_tags

  ecs_service_tags = var.ti_go_service_tags
}

module "inline_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = "inline"
  cluster                        = module.ecs_cluster.ecs_cluster_arn
  security_groups_id             = [aws_security_group.gosmtp_service_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id]
  name                           = "inline-dev-service"
  desired_count                  = 1
  task_definition_arn            = module.inline_task_definition.arn
  availability_zone_rebalancing  = "ENABLED"
  capacity_provider_strategies = [
    {
      capacity_provider = "FARGATE"
      base              = 0
      weight            = 3
    },
    {
      capacity_provider = "FARGATE_SPOT"
      base              = 0
      weight            = 2
    }
  ]

  target_group_arn = aws_lb_target_group.gosmtp.arn
  container_name   = "gateway-inline-dev"
  container_port   = 2525

  service_discovery_svc_tags = {
    Name        = "inline-dev-service-sd"
    Team        = "infra"
    Product     = "inline"
    Environment = "dev"
  }

  ecs_service_tags = {
    Name        = "inline-dev-service"
    Team        = "infra"
    Product     = "inline"
    Environment = "dev"
  }
}
