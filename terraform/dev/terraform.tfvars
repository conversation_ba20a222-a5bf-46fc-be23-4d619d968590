# global
region = "us-east-1"

# Docker images

## BFF
bff_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-ef84e62-20250701114629"

## Gateway
gateway_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-a54917b-20250703112952"

## Gateway subcommand
gateway_subcommand_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-a54917b-20250703112952"

## Remediator
remediator_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-a54917b-20250703112952"

## Setup
setup_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-2c47642-20250626051332"

## ML Inference
ml_inference_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ml-inference:ml-inference-dev-81004b0-20250701083347"

## Keycloak
keycloak_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/keycloak:1.1.9"

## Ingestion
ingestion_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-a54917b-20250703112952"

## TI Go service
ti_go_service_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-a54917b-20250703112952"

## Inline Service
inline_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-a54917b-20250703112952"

## Analytics Engine
analytics_engine_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-a54917b-20250703112952"

## Benthos image
benthos_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/benthos:1.0.9"

## Clickstack
otel_collector_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/otel:custom-0.0.19"

## Analytics Exporter
analytics_exporter_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-3e84435-20250627100239"

## Swagger Service
# swagger_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-dcb4e2f-20250509051326"

####################################################################################################################

## Tags
### Format - <Name>_<Resource_Name>
### Only pass name and resource name is defaulted
NAME        = "ravenclaw-dev"
ENVIRONMENT = "dev"

# vpc
vpc_cidr_block = "10.0.0.0/20"

# subnets - [TODO] three AZs / linear tickets on backlogs
private_subnet_1_cidr_block = "10.0.2.0/24"
private_subnet_2_cidr_block = "10.0.6.0/24"
private_subnet_3_cidr_block = "10.0.12.0/24"
public_subnet_1_cidr_block  = "10.0.8.0/24"
public_subnet_2_cidr_block  = "10.0.10.0/24"
public_subnet_3_cidr_block  = "10.0.14.0/24"

private_subnet_1_cidr_block_az = "us-east-1a"
private_subnet_2_cidr_block_az = "us-east-1b"
private_subnet_3_cidr_block_az = "us-east-1c"
public_subnet_1_cidr_block_az  = "us-east-1a"
public_subnet_2_cidr_block_az  = "us-east-1b"
public_subnet_3_cidr_block_az  = "us-east-1c"

# ec2
## nats
nats_availability_zone             = "us-east-1b"
nats_instance_type                 = "t2.micro"
nats_root_block_device_volume_size = 10

## bastion
bastion_availability_zone             = "us-east-1a"
bastion_instance_type                 = "t2.large"
bastion_root_block_device_volume_size = 20

# RDS
vpc_name                = "ravenclaw_dev_vpc"
db_name                 = "ravenclaw"
rds_allocated_storage   = 20
rds_availability_zone   = "us-east-1a"
identifier              = "ravenclaw"
rds_instance_class      = "db.t4g.micro"
rds_create_read_replica = false
rds_read_replica_azs    = ["us-east-1b", "us-east-1c"]

# ECS
## cluster
service_discovery_private_dns_namespace = "ravenclaw-dev-ns"
ecs_cluster_name                        = "ecs-ravenclaw-dev-cluster"

## IAM task roles
ecs_task_role_name           = "ecs-dev-task-role"           # change this
ecs_task_execution_role_name = "ecs-dev-task-execution-role" #change this

## Task definitions
### Environment variables

PG_DB                               = "ravenclaw"
PG_PORT                             = 5432
SUBJECT                             = "ravenclaw.ingestion.email.parsed"
SUBJECT_DETECT                      = "ravenclaw.detector.email.analysed"
SUBJECT_ENRICHED                    = "ravenclaw.ti.email.enriched"
GATEWAY_MICROSOFT_WEBHOOK_HOST      = "0.0.0.0"
GATEWAY_MICROSOFT_WEBHOOK_PATH      = "/v0/hooks/microsoft"
GATEWAY_MICROSOFT_WEBHOOK_PORT      = "8081"
GATEWAY_PUBLIC_HOST                 = "https://alpha.ravenclaw.ravenmail.io"
ASYNC_MAX_AT_ONCE_VT                = 10
ASYNC_MAX_PER_SECOND_VT             = 10
INTENT_EXTRACTION_MODEL_DEPLOYMENT  = "gpt-4o-mini"
INTENT_EXTRACTION_MODEL_ENDPOINT    = "https://ti-service-india.openai.azure.com"
INTENT_EXTRACTION_MODEL_API_VERSION = "2024-08-01-preview"
MODEL_PATH                          = "/app/data/spacy_model"
DEPLOYMENT_ENV                      = "dev"
GATEWAY_MICROSOFT_LIFECYCLE_PATH    = "/v0/hooks/microsoft/subs"
PG_MAX_IDLE_CONNECTION              = 5
PG_MAX_OPEN_CONNECTION              = 20
INTENT_EXTRACTION_RETRIES           = 2
INTENT_EXTRACTION_BACKOFF_IN_SEC    = 3


### BFF
bff_cpu    = 512
bff_memory = 1024

### Gotenberg
gotenberg_cpu       = 512
gotenberg_memory    = 1024
gotenberg_image_uri = "************.dkr.ecr.us-east-1.amazonaws.com/gotenberg:8"

### Gateway
gateway_cpu    = 512
gateway_memory = 1024

### Remediator
remediator_cpu    = 512
remediator_memory = 1024

### Setup
setup_cpu    = 512
setup_memory = 1024

### ML Inference
ml_inference_cpu    = 8192
ml_inference_memory = 16384
# ml_inference_cpu    = 4096
# ml_inference_memory = 8192


### Ingestion
# ingestion_cpu    = 512
# ingestion_memory = 1024
ingestion_cpu    = 2048
ingestion_memory = 4096

### TI Go Service
ti_go_service_cpu    = 512
ti_go_service_memory = 1024

### Gateway Subscribe
gateway_subcommand_memory = 1024
gateway_subcommand_cpu    = 512

### Analytics Engine
analytics_engine_cpu    = 512
analytics_engine_memory = 1024

### Benthos
benthos_cpu    = 512
benthos_memory = 1024

### Keycloak
keycloak_cpu                    = 1024
keycloak_memory                 = 2048
keycloak_db                     = "postgres"
keycloak_db_port                = "5432"
keycloak_hostname               = "auth.dev.ravenmail.io"
keycloak_logs_group             = "keycloak-ecs-logs"
keycloak_docker_image_name      = "keycloak"
keycloak_service_discovery_name = "keycloak"
keycloak_service_name           = "keycloak"
keycloak_desired_count          = 2

# Secrets [SENSTITIVE] - remove the values after the infra is up and running
NATS_TI_USERNAME                                 = ""
NATS_TI_PASSWORD                                 = ""
GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET            = ""
GROQ_API_KEY                                     = ""
INTENT_EXTRACTION_MODEL_API_KEY                  = ""
URL_SCAN_API_KEY                                 = ""
VIRUSTOTAL_API_KEY                               = ""
PG_PASSWORD                                      = ""
KEYCLOAK_ADMIN_USERNAME                          = ""
KEYCLOAK_ADMIN_PASSWORD                          = ""
KEYCLOAK_ADMIN_CLIENT_ID                         = ""
KEYCLOAK_ADMIN_CLIENT_SECRET                     = ""
KEYCLOAK_SERVICE_ACCOUNT_ID                      = ""
MS_AUTH_CLIENT_ID                                = ""
MS_AUTH_CLIENT_SECRET                            = ""
MS_AUTH_TENANT_ID                                = ""
DETECTION_MODEL_API_KEY                          = ""
DETECTION_MODEL_ENDPOINT                         = ""
DETECTION_MODEL_DEPLOYMENT                       = ""
DETECTION_MODEL_API_VERSION                      = ""
DETECTION_MODEL_DEPLOYMENT_WITH_TOKEN_DEPLOYMENT = ""
GW_AUTH_CLIENT_ID                                = ""
GW_AUTH_CLIENT_SECRET                            = ""
GW_SERVICE_ACCOUNT                               = ""
ATTACHMENT_MODEL_API_KEY                         = ""
GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC                = ""
GW_SERVICE_ACCOUNT_INGESTION                     = ""
GATEWAY_SUBCOMMAND_GW_SERVICE_ACCOUNT            = ""
SMARTHOST_TLS_CERT                               = ""
SMARTHOST_TLS_KEY                                = ""
DKIM_PRIVATE_KEY                                 = ""
NATS_LOAD_TESTING_SERVER_URL                     = ""
SMARTHOST_LOAD_TESTING_TLS_CERT                  = ""
SMARTHOST_LOAD_TESTING_TLS_KEY                   = ""
SPLUNK_ORGANIZATIONS_KEY                         = ""
CLASSIFICATION_OAI_API_KEY                       = ""
BENTHOS_NATS_URLS                                = ""
JWT_SECRET                                       = ""
ATTACHMENT_SCAN_ENABLED_ORGS                     = ""
DLP_MODEL_CREDENTIALS                            = ""

# Services

## BFF
bff_service_discovery_name = "bff"
bff_service_desired_count  = 1
bff_service_name           = "bff-dev-service"

## gotenberg
gotenberg_service_discovery_name = "gotenberg"
gotenberg_service_desired_count  = 1
gotenberg_service_name           = "gotenberg-dev-service"

## Gateway
gateway_service_discovery_name = "gateway"
gateway_service_desired_count  = 1
gateway_service_name           = "gateway-dev-service"

## Remediator
remediator_service_discovery_name = "remediator"
remediator_service_desired_count  = 1
remediator_service_name           = "remediator-dev-service"

## TI Service
ti_service_service_discovery_name = "ti_service"
ti_service_service_desired_count  = 1
ti_service_service_name           = "ti_service-dev-service"

# ML Inference Service
ml_inference_service_discovery_name = "ml_inference"
ml_inference_service_desired_count  = 1
ml_inference_service_name           = "ml_inference-dev-service"

# Ingestion
ingestion_docker_image_name = "ingestion"

# TI Go Service
ti_go_service_docker_image_name      = "ti-go-service"
ti_go_service_service_discovery_name = "ti-go-service"
ti_go_service_service_name           = "ti-go-service"
ti_go_service_service_desired_count  = 1

### ALB
lb_name                      = "ecs-dev-ravenclaw-alb"
certificate_arn              = "arn:aws:acm:us-east-1:************:certificate/5871b9b2-d16a-4181-8bf9-3c5ce3d873a5"
bff_lb_target_group_name     = "bff-dev-target-group"
gateway_lb_target_group_name = "gateway-dev-target-group"

### Keycloak ALB
keycloak_lb_name              = "ecs-dev-keycloak-alb"
keycloak_certificate_arn      = "arn:aws:acm:us-east-1:************:certificate/a335094b-3784-49fc-9195-598628d635da"
keycloak_lb_target_group_name = "keycloak-dev-target-group"

# Llama
llama_availability_zone             = "us-east-1c"
llama_instance_type                 = "g6.xlarge"
llama_root_block_device_volume_size = 100

## Clickstack
CLICKSTACK_PASSWORD = ""
CLICKSTACK_USER     = ""

## ANALYTICS EXPORTER
ANALYTICS_EXPORTER_RDS_USER            = ""
ANALYTICS_EXPORTER_RDS_PASSWORD        = ""
ANALYTICS_EXPORTER_CLICKHOUSE_USER     = ""
ANALYTICS_EXPORTER_CLICKHOUSE_PASSWORD = ""
