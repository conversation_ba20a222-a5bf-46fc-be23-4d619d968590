# resource "aws_cloudtrail" "main" {
#   depends_on = [aws_s3_bucket_policy.cloudtrail_s3_policy]

#   name                          = "cloudtrail-dev"
#   s3_bucket_name                = aws_s3_bucket.cloudtrail_s3.id
#   s3_key_prefix                 = "dev"
#   include_global_service_events = true

#   advanced_event_selector {
#     name = "S3"

#     field_selector {

#       equals = [
#         "AWS::S3::Object",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "lambda"

#     field_selector {

#       equals = [
#         "AWS::Lambda::Function",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "cloudmap-ns"

#     field_selector {

#       equals = [
#         "AWS::ServiceDiscovery::Namespace",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "cloudmap-svc"

#     field_selector {

#       equals = [
#         "AWS::ServiceDiscovery::Service",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "cloudfront-kv"

#     field_selector {

#       equals = [
#         "AWS::CloudFront::KeyValueStore",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "cloudwatch-metric"

#     field_selector {

#       equals = [
#         "AWS::CloudWatch::Metric",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "s3-access-pt"

#     field_selector {

#       equals = [
#         "AWS::S3::AccessPoint",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "sns-topic"

#     field_selector {

#       equals = [
#         "AWS::SNS::Topic",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "sys-manager"

#     field_selector {

#       equals = [
#         "AWS::SSMMessages::ControlChannel",
#       ]
#       field = "resources.type"

#     }
#     field_selector {

#       equals = [
#         "Data",
#       ]
#       field = "eventCategory"

#     }
#   }
#   advanced_event_selector {
#     name = "ec2"

#     field_selector {

#       equals = [
#         "NetworkActivity",
#       ]
#       field = "eventCategory"

#     }
#     field_selector {

#       equals = [
#         "ec2.amazonaws.com",
#       ]
#       field = "eventSource"

#     }
#   }
#   advanced_event_selector {
#     name = "kms"

#     field_selector {

#       equals = [
#         "NetworkActivity",
#       ]
#       field = "eventCategory"

#     }
#     field_selector {

#       equals = [
#         "kms.amazonaws.com",
#       ]
#       field = "eventSource"

#     }
#   }
#   advanced_event_selector {
#     name = "secretsmanager"

#     field_selector {

#       equals = [
#         "NetworkActivity",
#       ]
#       field = "eventCategory"

#     }
#     field_selector {

#       equals = [
#         "secretsmanager.amazonaws.com",
#       ]
#       field = "eventSource"

#     }
#   }
#   advanced_event_selector {
#     name = "Management events selector"

#     field_selector {

#       equals = [
#         "Management",
#       ]
#       field = "eventCategory"

#     }
#   }



#   # event_selector {
#   #   read_write_type           = "All"
#   #   include_management_events = true
#   # }

#   cloud_watch_logs_group_arn = "${aws_cloudwatch_log_group.cloudtrail.arn}:*" # CloudTrail requires the Log Stream wildcard
#   cloud_watch_logs_role_arn  = aws_iam_role.cloudtrail_role.arn
#   enable_logging             = true
#   enable_log_file_validation = true

#   insight_selector {
#     insight_type = "ApiCallRateInsight"
#   }

#   insight_selector {
#     insight_type = "ApiErrorRateInsight"
#   }

#   is_organization_trail = true
#   kms_key_id            = aws_kms_key.dev-misc-s3.arn

#   tags = {
#     Name        = "cloudtrail-dev"
#     Environment = "dev"
#     Team        = "infra"
#     Product     = "AWS"
#   }
# }

# data "aws_caller_identity" "current" {}

# data "aws_partition" "current" {}

# data "aws_region" "current" {}
