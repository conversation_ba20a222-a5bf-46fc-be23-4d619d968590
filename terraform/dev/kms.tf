resource "aws_kms_key" "dev-tfstate-s3" {
  description             = "KMS key for dev tfstate s3 bucket"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "tfstate-s3"
    Environment = "dev"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "tfstate_kms_alias" {
  name          = "alias/dev-tfstate-bucket-key"
  target_key_id = aws_kms_key.dev-tfstate-s3.key_id
}

resource "aws_kms_key" "dev-orgs" {
  description             = "KMS key for dev tfstate onboarded orgs"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "onboarded-orgs-kms-s3"
    Environment = "dev"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "dev-orgs" {
  name          = "alias/dev-orgs-bucket-key"
  target_key_id = aws_kms_key.dev-orgs.key_id
}

resource "aws_kms_key" "dev-misc-s3" {
  description             = "KMS key for all other dev buckets except tfstate and onboarded orgs"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  policy = jsonencode({
    Version = "2012-10-17"
    Id      = "kms-key-webui-access"
    Statement = [
      {
        Sid    = "Enable KMS key access to root user"
        Effect = "Allow"
        Principal = {
          "AWS" : "arn:aws:iam::771151923073:root"
        },
        Action = [
          "kms:*"
        ]
        Resource = "*",
      },
      {
        Sid    = "Enable KMS key access to cloudfront"
        Effect = "Allow"
        Principal = {
          "Service" : "cloudfront.amazonaws.com"
        },
        Action = [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ]
        Resource = "*",
        # Condition = {
        #   "StringEquals" : {
        #     "aws:SourceArn" : "arn:aws:cloudfront::771151923073:distribution/E3U8OPRT8II9HZ"
        #   },
        # },
      },
      {
        "Sid" : "Allow CloudTrail to use the key",
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "cloudtrail.amazonaws.com"
        },
        "Action" : [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        "Resource" : "*",
      },
    ]
  })
  tags = {
    Name        = "miscs-s3"
    Environment = "dev"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "dev-misc-s3-orgs" {
  name          = "alias/dev-misc-s3-bucket-key"
  target_key_id = aws_kms_key.dev-misc-s3.key_id
}

resource "aws_kms_key" "secretsmanager" {
  description             = "KMS key for dev secretsmanager"
  multi_region            = true
  enable_key_rotation     = true
  deletion_window_in_days = 10
  key_usage               = "ENCRYPT_DECRYPT"
  is_enabled              = true
  rotation_period_in_days = 90
  tags = {
    Name        = "secretsmanager-kms"
    Environment = "dev"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_kms_alias" "secretsmanager" {
  name          = "alias/secretsmanager-key"
  target_key_id = aws_kms_key.secretsmanager.key_id
}
