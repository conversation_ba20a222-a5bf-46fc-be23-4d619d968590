# global
variable "region" {
  type = string
}

variable "NAME" {
  type = string
}

variable "ENVIRONMENT" {
  type = string
}

# vpc
variable "vpc_cidr_block" {
  type = string
}


# subnets
variable "private_subnet_1_cidr_block" {
  type = string
}

variable "private_subnet_2_cidr_block" {
  type = string
}

variable "private_subnet_3_cidr_block" {
  type = string
}

variable "public_subnet_1_cidr_block" {
  type = string
}

variable "public_subnet_2_cidr_block" {
  type = string
}

variable "public_subnet_3_cidr_block" {
  type = string
}

variable "private_subnet_1_cidr_block_az" {
  type = string
}

variable "private_subnet_2_cidr_block_az" {
  type = string
}

variable "private_subnet_3_cidr_block_az" {
  type = string
}

variable "public_subnet_1_cidr_block_az" {
  type = string
}

variable "public_subnet_2_cidr_block_az" {
  type = string
}

variable "public_subnet_3_cidr_block_az" {
  type = string
}

# ec2 - nats

variable "nats_availability_zone" {
  type = string
}

variable "nats_instance_type" {
  type = string
}

variable "nats_root_block_device_volume_size" {
  type = number
}

variable "nats_root_block_device_iops" {
  type    = number
  default = 3000
}

variable "nats_root_block_device_throughput" {
  type    = number
  default = 125
}

variable "ssh_key_name" {
  type    = string
  default = "ecs-dev"
}

variable "nats_root_block_device_volume_type" {
  type    = string
  default = "gp3"
}

# ec2 - bastion

variable "bastion_availability_zone" {
  type = string
}

variable "bastion_instance_type" {
  type = string
}

variable "bastion_root_block_device_volume_size" {
  type = number
}

variable "bastion_root_block_device_iops" {
  type    = number
  default = 3000
}

variable "bastion_root_block_device_throughput" {
  type    = number
  default = 125
}

variable "bastion_root_block_device_volume_type" {
  type    = string
  default = "gp3"
}

# rds
variable "vpc_name" {
  type    = string
  default = ""
}

variable "PG_PASSWORD" {
  type = string
}

variable "db_name" {
  type = string
}

variable "rds_allocated_storage" {
  type = number
}

variable "identifier" {
  type = string
}

variable "rds_availability_zone" {
  type = string
}

variable "rds_multi_az" {
  type    = bool
  default = false
}

variable "rds_instance_class" {
  type = string
}

# ecs
## cluster

variable "service_discovery_private_dns_namespace" {
  type = string
}

variable "ecs_cluster_name" {
  type = string
}

# iam roles
variable "ecs_task_role_name" {
  type = string
}

variable "ecs_task_execution_role_name" {
  type = string
}

# Secrets [Senstive]
variable "NATS_TI_USERNAME" {
  type = string
}

variable "NATS_TI_PASSWORD" {
  type = string
}

variable "GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET" {
  type = string
}

variable "GROQ_API_KEY" {
  type = string
}

variable "INTENT_EXTRACTION_MODEL_API_KEY" {
  type = string
}

variable "INTENT_EXTRACTION_RETRIES" {
  type = string
}

variable "INTENT_EXTRACTION_BACKOFF_IN_SEC" {
  type = string
}

variable "URL_SCAN_API_KEY" {
  type = string
}

variable "VIRUSTOTAL_API_KEY" {
  type = string
}

variable "KEYCLOAK_ADMIN_USERNAME" {
  type = string
}

variable "KEYCLOAK_ADMIN_PASSWORD" {
  type = string
}

# ACM - domain name
variable "domain_name" {
  type    = string
  default = "example.com"
}

variable "bff_service_discovery_name" {
  type = string
}

variable "bff_service_name" {
  type = string
}

variable "bff_service_desired_count" {
  type = number
}

variable "gateway_service_discovery_name" {
  type = string
}

variable "gateway_service_name" {
  type = string
}

variable "gateway_service_desired_count" {
  type = number
}

variable "remediator_service_discovery_name" {
  type = string
}

variable "remediator_service_name" {
  type = string
}

variable "remediator_service_desired_count" {
  type = number
}

variable "ti_service_service_discovery_name" {
  type = string
}

variable "ti_service_service_name" {
  type = string
}

variable "ti_service_service_desired_count" {
  type = number
}

# EIP
variable "nat1_eip_domain" {
  type    = string
  default = "vpc"
}

variable "nat2_eip_domain" {
  type    = string
  default = "vpc"
}

variable "nat3_eip_domain" {
  type    = string
  default = "vpc"
}

variable "bastion_eip_domain" {
  type    = string
  default = "vpc"
}

variable "nat1_public_ipv4_pool" {
  type    = string
  default = "amazon"
}

variable "nat2_public_ipv4_pool" {
  type    = string
  default = "amazon"
}

variable "bastion_public_ipv4_pool" {
  type    = string
  default = "amazon"
}

variable "nat3_public_ipv4_pool" {
  type    = string
  default = "amazon"
}

variable "DEPLOYMENT_ENV" {
  type = string
}

variable "PG_MAX_OPEN_CONNECTION" {
  type = string
}

variable "PG_MAX_IDLE_CONNECTION" {
  type = string
}

variable "GATEWAY_MICROSOFT_LIFECYCLE_PATH" {
  type = string
}

variable "certificate_arn" {
  type = string
}

variable "bff_lb_target_group_name" {
  type = string
}

variable "gateway_lb_target_group_name" {
  type = string
}

variable "ssh_key_pair_public_key" {
  type    = string
  default = ""
}

variable "lb_name" {
  type = string
}

variable "lb_type" {
  type    = string
  default = "application"
}

variable "lb_internal" {
  type    = bool
  default = false
}

variable "lb_deletion_protection" {
  type    = bool
  default = true
}

variable "bff_lb_target_group_target_type" {
  type    = string
  default = "ip"
}

variable "bff_lb_target_group_port" {
  type    = number
  default = 80
}

variable "bff_lb_target_group_protocol" {
  type    = string
  default = "HTTP"
}

variable "bff_lb_target_group_health_check_path" {
  type    = string
  default = "/health"
}

variable "gateway_lb_target_group_target_type" {
  type    = string
  default = "ip"
}

variable "gateway_lb_target_group_port" {
  type    = number
  default = 80
}

variable "gateway_lb_target_group_protocol" {
  type    = string
  default = "HTTP"
}

variable "gateway_lb_target_group_health_check_path" {
  type    = string
  default = "/health"
}

variable "lb_listener_port" {
  type    = string
  default = "443"
}

variable "lb_listener_protocol" {
  type    = string
  default = "HTTPS"
}

variable "lb_listener_ssl_policy" {
  type    = string
  default = "ELBSecurityPolicy-TLS13-1-2-2021-06"
}

variable "lb_listener_default_action_type" {
  type    = string
  default = "forward"
}

variable "gotenberg_cpu" {
  type = number
}

variable "gotenberg_memory" {
  type = number
}

variable "gotenberg_image_uri" {
  type = string
}

variable "gotenberg_service_discovery_name" {
  type = string
}

variable "gotenberg_service_desired_count" {
  type = number
}

variable "gotenberg_service_name" {
  type = string
}

variable "gotenberg_logs_group" {
  type    = string
  default = "/ecs/gotenberg-dev"
}

variable "gotenberg_docker_image_name" {
  type    = string
  default = "gotenberg-dev"
}

variable "ml_inference_service_discovery_name" {
  type = string
}

variable "ml_inference_service_name" {
  type = string
}

variable "ml_inference_service_desired_count" {
  type = number
}

variable "rds_create_read_replica" {
  type = bool
}

variable "rds_read_replica_azs" {
  type = list(string)
}

variable "ti_go_service_service_discovery_name" {
  type = string
}

variable "ti_go_service_service_name" {
  type = string
}

variable "ti_go_service_service_desired_count" {
  type = number
}

variable "KEYCLOAK_ADMIN_CLIENT_ID" {
  type = string
}

variable "KEYCLOAK_ADMIN_CLIENT_SECRET" {
  type = string
}

variable "KEYCLOAK_SERVICE_ACCOUNT_ID" {
  type = string
}

variable "MS_AUTH_CLIENT_ID" {
  type = string
}

variable "MS_AUTH_CLIENT_SECRET" {
  type = string
}

variable "MS_AUTH_TENANT_ID" {
  type = string
}

variable "DETECTION_MODEL_API_KEY" {
  type = string
}

variable "DETECTION_MODEL_ENDPOINT" {
  type = string
}

variable "DETECTION_MODEL_DEPLOYMENT" {
  type = string
}

# llama
variable "llama_availability_zone" {
  type = string
}

variable "llama_instance_type" {
  type = string
}

variable "llama_root_block_device_volume_size" {
  type = number
}

variable "llama_root_block_device_iops" {
  type    = number
  default = 3000
}

variable "llama_root_block_device_throughput" {
  type    = number
  default = 125
}

variable "llama_root_block_device_volume_type" {
  type    = string
  default = "gp3"
}

variable "DETECTION_MODEL_API_VERSION" {
  type = string
}

variable "DETECTION_MODEL_DEPLOYMENT_WITH_TOKEN_DEPLOYMENT" {
  type = string
}

variable "GW_AUTH_CLIENT_ID" {
  type = string
}

variable "GW_AUTH_CLIENT_SECRET" {
  type = string
}

variable "GW_SERVICE_ACCOUNT" {
  type = string
}

variable "GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC" {
  type = string
}

variable "GW_SERVICE_ACCOUNT_INGESTION" {
  type = string
}


variable "GATEWAY_SUBCOMMAND_GW_SERVICE_ACCOUNT" {
  type = string
}

variable "SMARTHOST_TLS_CERT" {
  type = string
}

variable "SMARTHOST_TLS_KEY" {
  type = string
}

variable "DKIM_PRIVATE_KEY" {
  type = string
}

variable "analytics_engine_image_uri" {
  type = string
}

variable "analytics_engine_cpu" {
  type = number
}

variable "analytics_engine_memory" {
  type = number
}

variable "DLP_MODEL_CREDENTIALS" {
  type = string
}

variable "SPLUNK_ORGANIZATIONS_KEY" {
  type = string
}

variable "CLASSIFICATION_OAI_API_KEY" {
  type = string
}

variable "JWT_SECRET" {
  type = string
}

variable "ATTACHMENT_SCAN_ENABLED_ORGS" {
  type = string
}

variable "CLICKSTACK_PASSWORD" {
  type = string
}

variable "CLICKSTACK_USER" {
  type = string
}

variable "otel_collector_image_uri" {
  type = string
}

variable "ANALYTICS_EXPORTER_RDS_USER" {
  type = string
}

variable "ANALYTICS_EXPORTER_RDS_PASSWORD" {
  type = string
}

variable "ANALYTICS_EXPORTER_CLICKHOUSE_USER" {
  type = string
}

variable "ANALYTICS_EXPORTER_CLICKHOUSE_PASSWORD" {
  type = string
}
