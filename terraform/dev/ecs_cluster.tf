module "ecs_cluster" {
  source                                  = "../modules/ecs_cluster"
  region                                  = var.region
  service_discovery_private_dns_namespace = var.service_discovery_private_dns_namespace
  vpc_id                                  = module.vpc_main.vpc_id
  name                                    = var.ecs_cluster_name
  setting_name                            = "containerInsights"
  setting_value                           = "disabled"

  namespace_tags = var.ecs_namespace_tags

  ecs_cluster_tags = var.ecs_cluster_tags
}

resource "aws_ecs_cluster_capacity_providers" "main" {
  cluster_name = module.ecs_cluster.cluster_name

  capacity_providers = ["FARGATE", "FARGATE_SPOT"]

  default_capacity_provider_strategy {
    base              = 0
    weight            = 3
    capacity_provider = "FARGATE"
  }

  default_capacity_provider_strategy {
    base              = 0
    weight            = 2
    capacity_provider = "FARGATE_SPOT"
  }
}
