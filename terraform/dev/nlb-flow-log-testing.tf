resource "aws_flow_log" "main" {
  log_destination      = "${aws_s3_bucket.logs.arn}/vpc/flowlogs"
  log_destination_type = "s3"
  traffic_type         = "ALL"
  vpc_id               = module.vpc_main.vpc_id
  log_format           = "$${version} $${account-id} $${interface-id} $${srcaddr} $${dstaddr} $${srcport} $${dstport} $${protocol} $${packets} $${bytes} $${start} $${end} $${action} $${log-status} $${vpc-id} $${subnet-id} $${instance-id} $${tcp-flags} $${type} $${pkt-srcaddr} $${pkt-dstaddr} $${region} $${az-id} $${sublocation-type} $${sublocation-id} $${pkt-src-aws-service} $${pkt-dst-aws-service} $${flow-direction} $${traffic-path} $${ecs-cluster-arn} $${ecs-cluster-name} $${ecs-container-instance-arn} $${ecs-container-instance-id} $${ecs-container-id} $${ecs-second-container-id} $${ecs-service-name} $${ecs-task-definition-arn} $${ecs-task-arn} $${ecs-task-id} $${reject-reason}"

  tags = {
    Name        = "vpc-flow-logs"
    Team        = "infra"
    Product     = "compliance"
    Environment = "dev"
  }
}

############### ALB Logs ##################
resource "aws_s3_bucket" "logs" {
  bucket = "dev-alb-cloudfront-logs-bucket"

  tags = {
    Name        = "dev-alb-cloudfront-logs-s3-bucket"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_s3_bucket_ownership_controls" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "logs" {
  depends_on = [aws_s3_bucket_ownership_controls.logs]

  bucket = aws_s3_bucket.logs.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "logs" {
  bucket = aws_s3_bucket.logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_server_side_encryption_configuration" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    id     = "ExpireObjectsAfter30Days"
    status = "Enabled" # Enables the rule

    expiration {
      days = 7 # Objects older than 30 days will be deleted
    }

    filter {
      prefix = "" # Applies the rule to all objects in the bucket
    }
  }
}

# # Get the ELB service account ARN
data "aws_elb_service_account" "main" {}

resource "aws_s3_bucket_policy" "logs_policy" {
  bucket = aws_s3_bucket.logs.id
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          "AWS" : "arn:aws:iam::************:root"
        },
        Action = [
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObject",
          "s3:GetObjectAttributes"
        ],
        Resource = [
          aws_s3_bucket.logs.arn,
          "${aws_s3_bucket.logs.arn}/*"
        ]
      },
      {
        Effect = "Allow",
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        },
        Action = [
          "s3:PutObject"
        ],
        Resource = "${aws_s3_bucket.logs.arn}/vpc/*", # Restrict to the vpc/ prefix
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Effect = "Allow",
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        },
        Action = [
          "s3:GetBucketAcl"
        ],
        Resource = aws_s3_bucket.logs.arn
      },
      {
        Action = "s3:PutObject"
        Condition = {
          StringEquals = {
            "aws:SourceAccount" = "************"
          }
        }
        Effect = "Allow"
        Principal = {
          Service = "logging.s3.amazonaws.com"
        }
        Resource = "arn:aws:s3:::dev-alb-cloudfront-logs-bucket/*"
        Sid      = "S3PolicyStmt-DO-NOT-MODIFY-*************"
        }, {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "delivery.logs.amazonaws.com"
        },
        "Action" : "s3:PutObject",
        "Resource" : "arn:aws:s3:::dev-alb-cloudfront-logs-bucket/*",
        "Condition" : {
          "StringEquals" : {
            "s3:x-amz-acl" : "bucket-owner-full-control"
          }
        }
      },
    ]
  })
}

############ S3 event notification 
resource "aws_iam_role" "iam_for_lambda_alb_logs" {
  name               = "dev_iam_for_lambda_alb_logs"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": [
          "lambda.amazonaws.com"
        ]
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "alb_cloudwatch_logs_policy" {
  name = "DevALBCloudWatchLogsPermissions"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogStream",
          "logs:CreateLogGroup",
          "logs:DeleteLogStream",
          "logs:PutLogEvents",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetObject",
          "s3:GetObjectAttributes"
        ]
        Resource = ["*"]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "attach_alb_cloudwatch_logs_policy" {
  role       = aws_iam_role.iam_for_lambda_alb_logs.name
  policy_arn = aws_iam_policy.alb_cloudwatch_logs_policy.arn
}

resource "aws_s3_bucket_notification" "ravenclaw_alb_access_logs_bucket_notification" {
  bucket = aws_s3_bucket.logs.id

  lambda_function {
    lambda_function_arn = aws_lambda_function.vpc_flow_logs.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "vpc"
  }
}

resource "aws_lambda_permission" "allow_vpc_flow_logs" {
  statement_id  = "DevAllowExecutionFromS3BucketVPCFlowLogs"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.vpc_flow_logs.arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.logs.arn
}

resource "aws_lambda_function" "vpc_flow_logs" {
  filename         = "lambda_function_vpc_flow_logs_payload.zip"
  function_name    = "dev-vpc-flow-logs-func"
  role             = aws_iam_role.iam_for_lambda_alb_logs.arn
  handler          = "lambda.handler"
  runtime          = "python3.9"
  timeout          = 900
  source_code_hash = filebase64sha256("lambda_function_vpc_flow_logs_payload.zip")

  environment {
    variables = {
      LOG_GROUP_NAME = "/aws/vpc/dev-flow-logs"
    }
  }

  tags = {
    Name        = "dev-vpc-flow-logs-func"
    Team        = "infra"
    Product     = "compliance"
    Environment = "prod"
  }
}

resource "aws_cloudwatch_log_group" "vpc_flow_logs" {
  name              = "/aws/vpc/dev-flow-logs"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/waf/dev-flow-logs"
    Product     = "compliance"
    Team        = "infra"
    Environment = "prod"
    Application = "vpc"
  }
}

resource "aws_cloudwatch_log_group" "vpc_flow_logs_func" {
  name              = "/aws/lambda/dev-vpc-flow-logs-func"
  skip_destroy      = true
  log_group_class   = "STANDARD"
  retention_in_days = 90

  tags = {
    Name        = "/aws/lambda/dev-vpc-flow-logs-func"
    Product     = "compliance"
    Team        = "infra"
    Environment = "prod"
    Application = "lambda"
  }
}
