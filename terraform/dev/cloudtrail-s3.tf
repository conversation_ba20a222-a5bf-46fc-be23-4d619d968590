# resource "aws_s3_bucket" "cloudtrail_s3" {
#   bucket        = "cloudtrail-dev-trail"
#   force_destroy = false

#   tags = {
#     Name        = "cloudtrail-s3-dev-bucket"
#     Environment = "dev"
#     Product     = "AWS"
#     Team        = "infra"
#   }
# }

# resource "aws_s3_bucket_versioning" "cloudtrail_s3" {
#   bucket = aws_s3_bucket.cloudtrail_s3.id
#   versioning_configuration {
#     status = "Enabled"
#   }
# }

# resource "aws_s3_bucket_lifecycle_configuration" "cloudtrail_s3" {
#   # Must have bucket versioning enabled first
#   depends_on = [aws_s3_bucket_versioning.cloudtrail_s3]

#   bucket = aws_s3_bucket.cloudtrail_s3.id

#   rule {
#     id = "lifecycle_configuration"

#     noncurrent_version_expiration {
#       noncurrent_days = 180
#     }

#     status = "Enabled"
#   }
# }

# resource "aws_s3_bucket_ownership_controls" "cloudtrail_s3" {
#   bucket = aws_s3_bucket.cloudtrail_s3.id

#   rule {
#     object_ownership = "BucketOwnerPreferred"
#   }
# }

# resource "aws_s3_bucket_acl" "cloudtrail_s3" {
#   depends_on = [aws_s3_bucket_ownership_controls.cloudtrail_s3]

#   bucket = aws_s3_bucket.cloudtrail_s3.id
#   acl    = "private"
# }

# resource "aws_s3_bucket_public_access_block" "cloudtrail_s3" {
#   bucket = aws_s3_bucket.cloudtrail_s3.id

#   block_public_acls       = true
#   block_public_policy     = true
#   ignore_public_acls      = true
#   restrict_public_buckets = true
# }

# # data "aws_iam_policy_document" "cloudtrail_s3_policy" {
# #   statement {
# #     sid    = "AWSCloudTrailAclCheck"
# #     effect = "Allow"

# #     principals {
# #       type        = "Service"
# #       identifiers = ["cloudtrail.amazonaws.com"]
# #     }

# #     actions   = ["s3:GetBucketAcl", "s3:GetBucketPolicy"]
# #     resources = [aws_s3_bucket.cloudtrail_s3.arn]
# #     # resources = ["*"]
# #     # condition {
# #     #   test     = "StringEquals"
# #     #   variable = "aws:SourceArn"
# #     #   values   = ["arn:${data.aws_partition.current.partition}:cloudtrail:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:trail/cloudtrail-dev"]
# #     # }
# #   }

# #   statement {
# #     sid    = "AWSCloudTrailWrite"
# #     effect = "Allow"

# #     principals {
# #       type        = "Service"
# #       identifiers = ["cloudtrail.amazonaws.com"]
# #     }

# #     actions   = ["s3:PutObject", "s3:PutBucketPolicy"]
# #     resources = ["arn:aws:s3:::${aws_s3_bucket.cloudtrail_s3.arn}/dev/AWSLogs/*/*"]
# #     # resources = ["*"]

# #     # condition {
# #     #   test     = "StringEquals"
# #     #   variable = "s3:x-amz-acl"
# #     #   values   = ["bucket-owner-full-control"]
# #     # }
# #     # condition {
# #     #   test     = "StringEquals"
# #     #   variable = "aws:SourceArn"
# #     #   values   = ["arn:${data.aws_partition.current.partition}:cloudtrail:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:trail/cloudtrail-dev"]
# #     # }
# #   }
# # }

# data "aws_iam_policy_document" "cloudtrail_s3_policy" {
#   statement {
#     sid    = "AWSCloudTrailAclCheck"
#     effect = "Allow"

#     principals {
#       type        = "Service"
#       identifiers = ["cloudtrail.amazonaws.com"]
#     }

#     actions   = ["s3:GetBucketAcl", "s3:GetBucketPolicy"]
#     resources = [aws_s3_bucket.cloudtrail_s3.arn]
#   }

#   statement {
#     sid    = "AWSCloudTrailWrite"
#     effect = "Allow"

#     principals {
#       type        = "Service"
#       identifiers = ["cloudtrail.amazonaws.com"]
#     }

#     actions   = ["s3:PutObject"]
#     resources = ["${aws_s3_bucket.cloudtrail_s3.arn}/dev/AWSLogs/*"]

#     condition {
#       test     = "StringEquals"
#       variable = "s3:x-amz-acl"
#       values   = ["bucket-owner-full-control"]
#     }
#   }
# }

# resource "aws_s3_bucket_server_side_encryption_configuration" "cloudtrail_s3" {
#   bucket = aws_s3_bucket.cloudtrail_s3.id

#   rule {
#     apply_server_side_encryption_by_default {
#       kms_master_key_id = aws_kms_key.dev-misc-s3.arn
#       sse_algorithm     = "aws:kms"
#     }
#     bucket_key_enabled = true
#   }
# }

# resource "aws_s3_bucket_policy" "cloudtrail_s3_policy" {
#   bucket = aws_s3_bucket.cloudtrail_s3.id
#   policy = data.aws_iam_policy_document.cloudtrail_s3_policy.json
# }
