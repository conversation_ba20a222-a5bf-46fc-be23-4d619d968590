########### go smtp
resource "aws_eip" "nlb_eip_1" {
  domain = "vpc"
  tags = {
    Name        = "gosmtp-nlb-eip-1"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# resource "aws_eip" "nlb_eip_2" {
#   domain = "vpc"
#   tags = {
#     Name        = "gosmtp-nlb-eip-2"
#     Environment = "dev"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
# }

# resource "aws_eip" "nlb_eip_3" {
#   domain = "vpc"
#   domain = "vpc"
#   tags = {
#     Name        = "gosmtp-nlb-eip-3"
#     Environment = "dev"
#     Product     = "ravenclaw"
#     Team        = "infra"
#   }
# }

resource "aws_lb" "smtp" {
  name               = "gosmtp-server-nlb"
  internal           = false
  load_balancer_type = "network"
  security_groups    = [aws_security_group.google_smtp_nlb_loadbalancer_sg.id, aws_security_group.microsoft_smtp_nlb_loadbalancer_sg.id]
  # security_groups = [aws_security_group.microsoft_smtp_nlb_loadbalancer_sg.id]
  # subnets            = [module.main_public_subnet_1.subnet_id, module.main_public_subnet_2.subnet_id, module.main_public_subnet_3.subnet_id]

  subnet_mapping {
    subnet_id     = module.main_public_subnet_1.subnet_id
    allocation_id = aws_eip.nlb_eip_1.id
  }

  # subnet_mapping {
  #   subnet_id     = module.main_public_subnet_2.subnet_id
  #   allocation_id = aws_eip.nlb_eip_2.id
  # }

  # subnet_mapping {
  #   subnet_id     = module.main_public_subnet_3.subnet_id
  #   allocation_id = aws_eip.nlb_eip_3.id
  # }

  enable_deletion_protection = true

  tags = {
    Name        = "gosmtp-server-nlb"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_lb_target_group" "gosmtp" {
  name                               = "gosmtp-server-tg-2525"
  target_type                        = "ip"
  port                               = 2525
  protocol                           = "TCP"
  lambda_multi_value_headers_enabled = false
  slow_start                         = 0
  vpc_id                             = module.vpc_main.vpc_id
  tags = {
    Name        = "gosmtp-server-tg-2525"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
  health_check {
    protocol = "TCP"
  }
}

resource "aws_lb_listener" "smtp" {
  load_balancer_arn        = aws_lb.smtp.arn
  port                     = 25
  protocol                 = "TCP"
  tcp_idle_timeout_seconds = 350
  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.gosmtp.arn

    # forward {
    #   target_group {
    #     arn    = aws_lb_target_group.gosmtp.arn
    #     weight = 0
    #   }
    # }
  }

  tags = {
    Name        = "gosmtp-nlb-listener"
    Environment = "dev"
    Product     = "gosmtp"
    Team        = "infra"
  }
}
