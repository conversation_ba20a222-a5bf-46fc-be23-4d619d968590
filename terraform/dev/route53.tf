resource "aws_route53_zone" "dev-dashboard" {
  name = "dev.dashboard.ravenmail.io"

  tags = {
    Name        = "ravenclaw-dev-dashboard-route53"
    Environment = "dev"
    Team        = "infra"
    Product     = "ravenclaw"
  }
}

resource "aws_route53_record" "dev-dashboard" {
  zone_id = aws_route53_zone.dev-dashboard.zone_id
  name    = "dev.dashboard.ravenmail.io"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.s3_distribution.domain_name
    zone_id                = aws_cloudfront_distribution.s3_distribution.hosted_zone_id
    evaluate_target_health = false
  }

}
