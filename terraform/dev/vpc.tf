module "vpc_main" {
  source     = "../modules/vpc"
  region     = var.region
  cidr_block = var.vpc_cidr_block

  tags = var.vpc_tags
}

resource "aws_vpc_endpoint" "s3_vpc_ep_gateway" {
  vpc_id            = module.vpc_main.vpc_id
  vpc_endpoint_type = "Gateway"
  service_name      = "com.amazonaws.${var.region}.s3"
  route_table_ids = [
    aws_route_table.rt_private_1.id
  ]
  tags = {
    Name        = "ravenclaw-s3-vpc-endpoint"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "dev"
  }
}

# resource "aws_security_group" "vpc_inference_endpoints_sg" {
#   description = "SG for VPC Inference Endpoints"

#   egress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "0"
#     protocol    = "-1"
#     self        = "false"
#     to_port     = "0"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "0"
#     protocol    = "-1"
#     self        = "false"
#     to_port     = "0"
#   }

#   name = "vpc_interence_endpoints_sg"
#   tags = {
#     Name        = "vpc_interference_endpoints_sg"
#     Environment = "dev"
#     Team        = "infra"
#     Product     = "ravenclaw"
#   }
#   vpc_id = module.vpc_main.vpc_id
# }

# resource "aws_vpc_endpoint" "ecr-dkr" {
#   vpc_id              = module.vpc_main.vpc_id
#   service_name        = "com.amazonaws.${var.region}.ecr.dkr"
#   vpc_endpoint_type   = "Interface"
#   subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   private_dns_enabled = true
#   security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

#   tags = {
#     Name        = "ravenclaw-ecr-dkr-vpc-endpoint"
#     Team        = "infra"
#     Product     = "ravenclaw"
#     Environment = "dev"
#   }
# }

# resource "aws_vpc_endpoint" "ecr" {
#   vpc_id              = module.vpc_main.vpc_id
#   service_name        = "com.amazonaws.${var.region}.ecr.api"
#   vpc_endpoint_type   = "Interface"
#   subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   private_dns_enabled = true
#   security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

#   tags = {
#     Name        = "ravenclaw-ecr-vpc-endpoint"
#     Team        = "infra"
#     Product     = "ravenclaw"
#     Environment = "dev"
#   }
# }

# resource "aws_vpc_endpoint" "ecr-dkr" {
#   vpc_id              = module.vpc_main.vpc_id
#   service_name        = "com.amazonaws.${var.region}.ecr.dkr"
#   vpc_endpoint_type   = "Interface"
#   subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   private_dns_enabled = true
#   security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

#   tags = {
#     Name        = "ravenclaw-ecr-dkr-vpc-endpoint"
#     Team        = "infra"
#     Product     = "ravenclaw"
#     Environment = "dev"
#   }
# }

# resource "aws_vpc_endpoint" "cloudwatch" {
#   vpc_id              = module.vpc_main.vpc_id
#   service_name        = "com.amazonaws.${var.region}.logs"
#   vpc_endpoint_type   = "Interface"
#   subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   private_dns_enabled = true
#   security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

#   tags = {
#     Name        = "ravenclaw-cloudwatch-logs-vpc-endpoint"
#     Team        = "infra"
#     Product     = "ravenclaw"
#     Environment = "dev"
#   }
# }

# resource "aws_vpc_endpoint" "kms" {
#   vpc_id              = module.vpc_main.vpc_id
#   service_name        = "com.amazonaws.${var.region}.kms"
#   vpc_endpoint_type   = "Interface"
#   subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   private_dns_enabled = true
#   security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

#   tags = {
#     Name        = "ravenclaw-kms-vpc-endpoint"
#     Team        = "infra"
#     Product     = "ravenclaw"
#     Environment = "dev"
#   }
# }

# resource "aws_vpc_endpoint" "secretsmanager" {
#   vpc_id              = module.vpc_main.vpc_id
#   service_name        = "com.amazonaws.${var.region}.secretsmanager"
#   vpc_endpoint_type   = "Interface"
#   subnet_ids          = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
#   private_dns_enabled = true
#   security_group_ids  = [aws_security_group.vpc_inference_endpoints_sg.id]

#   tags = {
#     Name        = "ravenclaw-secretsmanager-vpc-endpoint"
#     Team        = "infra"
#     Product     = "ravenclaw"
#     Environment = "dev"
#   }
# }
