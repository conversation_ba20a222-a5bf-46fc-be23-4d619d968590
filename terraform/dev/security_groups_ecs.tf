module "ecs_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "ecs_sg_new"
  description = "Created in ECS Console"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.ecs_sg_tags

  # ─────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = ["::/0"]
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 8081
  ingress_to_port                       = 8081
  ingress_protocol                      = "tcp"
  ingress_self                          = false
  ingress_custom_rules                  = []

  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
}

module "ecs_ravenclaw_bff_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "ecs_ravenclaw_bff_sg_new"
  description = "Created in ECS Console - BFF"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.bff_sg_tags

  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = ["::/0"]
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 8080
  ingress_to_port                       = 8080
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  ingress_custom_rules = [
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = ["::/0"]
      from_port   = 3000
      to_port     = 3000
      ip_protocol = "tcp"
      description = ""
    }
  ]

  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
}

module "ecs_ravenclaw_setup_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "ecs_ravenclaw_setup_sg_new"
  description = "Created in ECS Console"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.setup_sg_tags

  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = ["::/0"]
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 8080
  ingress_to_port                       = 8080
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  ingress_custom_rules = [
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = ["::/0"]
      from_port   = 8081
      to_port     = 8081
      ip_protocol = "tcp"
      description = ""
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = ["::/0"]
      from_port   = 3000
      to_port     = 3000
      ip_protocol = "tcp"
      description = ""
    }
  ]

  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
  egress_self                          = false
}

module "gateway_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "gateway_sg_new"
  description = "Created in ECS Console - gateway"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.gateway_sg_tags

  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 8081
  ingress_to_port                       = 8081
  ingress_protocol                      = "tcp"
  ingress_self                          = false
  ingress_custom_rules                  = []

  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
  egress_self                          = false
}

module "remediator_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "remediator_sg_new"
  description = "Created in ECS Console"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.remediator_sg_tags

  # ────────────────────────────────────────────────
  # Ingress: HTTP (80) from within the VPC only
  # ────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 80
  ingress_to_port                       = 80
  ingress_protocol                      = "tcp"
  ingress_self                          = false
  ingress_custom_rules                  = []

  # ────────────────────────────────────────────────
  # Egress: allow all outbound
  # ────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
  egress_self                          = false
}

module "ml_inference_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "ml_inference_sg_new"
  description = "Created in ECS Console"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.ml_inference_sg_tags

  # ───────────────────────────────────────────────────────────
  # Ingress: TCP 8000 only from within the VPC
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 8000
  ingress_to_port                       = 8000
  ingress_protocol                      = "tcp"
  ingress_self                          = false
  ingress_custom_rules                  = []

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}

module "keycloak_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "keycloak_sg_new"
  description = "SG for keycloak service"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.keycloak_sg_tags

  # ───────────────────────────────────────────────────────────
  # Ingress: TCP 8443 from within the VPC only
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 8443
  ingress_to_port                       = 8443
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  # ───────────────────────────────────────────────────────────
  # Custom ingress: TCP 9000 & 7800 from within the VPC only
  # ───────────────────────────────────────────────────────────
  ingress_custom_rules = [
    {
      cidr_ipv4   = [var.vpc_cidr_block]
      cidr_ipv6   = []
      from_port   = 9000
      to_port     = 9000
      ip_protocol = "tcp"
      description = ""
    },
    {
      cidr_ipv4   = [var.vpc_cidr_block]
      cidr_ipv6   = []
      from_port   = 7800
      to_port     = 7800
      ip_protocol = "tcp"
      description = ""
    }
  ]

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}


module "ecs_ravenclaw_ingestion_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "ecs_ravenclaw_ingestion_sg_new"
  description = "Created in ECS Console"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.ingestion_sg_tags

  # ───────────────────────────────────────────────────────────
  # Ingress: HTTP (80) from VPC IPv4 + anywhere IPv6
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = ["::/0"]
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 80
  ingress_to_port                       = 80
  ingress_protocol                      = "tcp"
  ingress_self                          = false
  ingress_custom_rules                  = []

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}

module "ravenclaw_ti_go_service_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "ravenclaw_ti_go_service_sg_new"
  description = "Created in ECS Console"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.ti_go_sg_tags

  # ───────────────────────────────────────────────────────────
  # Ingress: HTTP (80) from within the VPC only
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 80
  ingress_to_port                       = 80
  ingress_protocol                      = "tcp"
  ingress_self                          = false
  ingress_custom_rules                  = []

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}

module "gosmtp_service_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "gosmtp-server-ecs-sg-new"
  description = "Security group for SMTP server ECS Fargate tasks"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "gosmtp-server-ecs-sg"
    Environment = "dev"
    Product     = "inline"
    Team        = "infra"
  }

  # ───────────────────────────────────────────────────────────
  # Ingress: HTTP (80) from within the VPC only
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 80
  ingress_to_port                       = 80
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  # ───────────────────────────────────────────────────────────
  # Custom ingress: SMTP submission ports from within the VPC
  # ───────────────────────────────────────────────────────────
  ingress_custom_rules = [
    {
      cidr_ipv4   = [var.vpc_cidr_block]
      cidr_ipv6   = []
      from_port   = 587
      to_port     = 587
      ip_protocol = "tcp"
      description = ""
    },
    {
      cidr_ipv4   = [var.vpc_cidr_block]
      cidr_ipv6   = []
      from_port   = 2525
      to_port     = 2525
      ip_protocol = "tcp"
      description = ""
    }
  ]

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}
