module "nats" {
  source                        = "../modules/ec2_instance"
  region                        = var.region
  availability_zone             = var.nats_availability_zone
  instance_type                 = var.nats_instance_type
  root_block_device_volume_size = var.nats_root_block_device_volume_size
  root_block_device_iops        = var.nats_root_block_device_iops
  root_block_device_throughput  = var.nats_root_block_device_throughput
  root_block_device_volume_type = var.nats_root_block_device_volume_type
  subnet_id                     = module.main_private_subnet_2.subnet_id
  vpc_security_group_ids        = ["${aws_security_group.nats_sg.id}"]
  key_name                      = var.ssh_key_name
  iam_instance_profile          = aws_iam_instance_profile.CloudWatchAgentServerProfile.name
  tags                          = var.nats_instance_tags
}

module "nats-1" {
  source                        = "../modules/ec2_instance"
  region                        = var.region
  availability_zone             = "us-east-1a"
  instance_type                 = "t4g.micro"
  root_block_device_volume_size = 10
  root_block_device_iops        = var.nats_root_block_device_iops
  root_block_device_throughput  = var.nats_root_block_device_throughput
  root_block_device_volume_type = var.nats_root_block_device_volume_type
  subnet_id                     = module.main_private_subnet_1.subnet_id
  vpc_security_group_ids        = ["${aws_security_group.nats_sg.id}"]
  key_name                      = "nats-dev-new"
  ami                           = data.aws_ami.graviton_instance_ami.id

  iam_instance_profile = aws_iam_instance_profile.CloudWatchAgentServerProfile.name

  service_discovery_service_name = "nats-1-dev"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  tags = var.nats_cluster_1_tags
}

module "nats-2" {
  source                        = "../modules/ec2_instance"
  region                        = var.region
  availability_zone             = "us-east-1b"
  instance_type                 = "t4g.micro"
  root_block_device_volume_size = 10
  root_block_device_iops        = var.nats_root_block_device_iops
  root_block_device_throughput  = var.nats_root_block_device_throughput
  root_block_device_volume_type = var.nats_root_block_device_volume_type
  subnet_id                     = module.main_private_subnet_2.subnet_id
  vpc_security_group_ids        = ["${aws_security_group.nats_sg.id}"]
  key_name                      = "nats-dev-new"
  ami                           = data.aws_ami.graviton_instance_ami.id

  service_discovery_service_name = "nats-2-dev"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  tags = var.nats_cluster_2_tags
}

module "nats-3" {
  source                        = "../modules/ec2_instance"
  region                        = var.region
  availability_zone             = "us-east-1c"
  instance_type                 = "t4g.micro"
  root_block_device_volume_size = 10
  root_block_device_iops        = var.nats_root_block_device_iops
  root_block_device_throughput  = var.nats_root_block_device_throughput
  root_block_device_volume_type = var.nats_root_block_device_volume_type
  subnet_id                     = module.main_private_subnet_3.subnet_id
  vpc_security_group_ids        = ["${aws_security_group.nats_sg.id}"]
  key_name                      = "nats-dev-new"
  ami                           = data.aws_ami.graviton_instance_ami.id

  iam_instance_profile           = aws_iam_instance_profile.CloudWatchAgentServerProfile.name
  service_discovery_service_name = "nats-3-dev"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  tags = var.nats_cluster_3_tags
}

module "nats-4" {
  source                                  = "../modules/ec2_instance"
  region                                  = var.region
  availability_zone                       = "us-east-1c"
  instance_type                           = "t4g.micro"
  root_block_device_volume_size           = 10
  root_block_device_iops                  = var.nats_root_block_device_iops
  root_block_device_throughput            = var.nats_root_block_device_throughput
  root_block_device_volume_type           = var.nats_root_block_device_volume_type
  subnet_id                               = module.main_private_subnet_3.subnet_id
  vpc_security_group_ids                  = ["${aws_security_group.nats_sg.id}"]
  key_name                                = "nats-dev-new"
  ami                                     = data.aws_ami.graviton_instance_ami.id
  root_block_device_delete_on_termination = false

  iam_instance_profile           = aws_iam_instance_profile.CloudWatchAgentServerProfile.name
  service_discovery_service_name = "nats-4-dev"
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

  tags = var.nats_cluster_4_tags
}
