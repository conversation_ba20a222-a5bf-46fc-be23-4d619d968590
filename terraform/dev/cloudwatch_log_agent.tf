resource "aws_iam_role" "CloudWatchAgentServerRole" {
  name               = "CloudWatchAgentServerRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

  resource "aws_iam_role_policy_attachment" "CloudWatchAgentServerPolicy_attach" {
  role       = aws_iam_role.CloudWatchAgentServerRole.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

resource "aws_iam_role_policy_attachment" "AmazonSSMManagedInstanceCore_attach" {
  role       = aws_iam_role.CloudWatchAgentServerRole.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role" "CloudWatchAgentAdminRole" {
  name               = "CloudWatchAgentAdminRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "CloudWatchAgentAdminPolicy_attach" {
  role       = aws_iam_role.CloudWatchAgentAdminRole.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentAdminPolicy"
}

resource "aws_iam_role_policy_attachment" "AmazonSSMManagedInstanceCore_attach_admin" {
  role       = aws_iam_role.CloudWatchAgentAdminRole.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_instance_profile" "CloudWatchAgentServerProfile" {
  name = "CloudWatchAgentServerProfile"
  role = aws_iam_role.CloudWatchAgentServerRole.name
}

resource "aws_iam_instance_profile" "CloudWatchAgentAdminProfile" {
  name = "CloudWatchAgentAdminProfile"
  role = aws_iam_role.CloudWatchAgentAdminRole.name
}
