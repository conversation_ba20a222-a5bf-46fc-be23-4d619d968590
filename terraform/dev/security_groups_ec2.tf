module "bastion_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1" # or wherever your VPC lives
  name        = "bastion_sg_new"
  description = "SG for dev bastion instance"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "ravenclaw-bastion-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }

  ingress_ipv4_cidrs      = ["${var.vpc_cidr_block}"] # no public‐CIDR-based allow
  ingress_ipv6_cidrs      = []
  ingress_prefix_list_ids = []
  ingress_from_port       = 22
  ingress_to_port         = 22
  ingress_protocol        = "tcp"
  ingress_custom_rules    = [] # no extra custom rules
  ingress_self            = true

  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
  egress_self                          = false
}

module "nats_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "nats_dev_sg_new"
  description = "SG for dev NATS setup"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.nats_sg_tags

  # ───────────────────────────────────────────────────────────
  # Ingress: NATS & SSH & Node Exporter from within VPC
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 4222
  ingress_to_port                       = 4222
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  # Any additional ports beyond 4222
  ingress_custom_rules = [
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 6222
      to_port     = 6222
      ip_protocol = "tcp"
      description = ""
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 8222
      to_port     = 8222
      ip_protocol = "tcp"
      description = ""
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 7777
      to_port     = 7777
      ip_protocol = "tcp"
      description = ""
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 7778
      to_port     = 7778
      ip_protocol = "tcp"
      description = ""
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 22
      to_port     = 22
      ip_protocol = "tcp"
      description = ""
    },
    {
      cidr_ipv4   = ["${var.vpc_cidr_block}"]
      cidr_ipv6   = []
      from_port   = 9100
      to_port     = 9100
      ip_protocol = "tcp"
      description = "Allow HTTP - Node Exporter"
    }
  ]

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
  egress_self                          = false
}

module "llama_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "llama_sg_new"
  description = "SG for dev llama instance"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.llama_sg_tags

  # ───────────────────────────────────────────────────────────
  # Ingress: SSH (22) from within the VPC only
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 22
  ingress_to_port                       = 22
  ingress_protocol                      = "tcp"
  ingress_self                          = false

  # ───────────────────────────────────────────────────────────
  # Custom ingress: TCP 8000 from within the VPC only
  # ───────────────────────────────────────────────────────────
  ingress_custom_rules = [
    {
      cidr_ipv4   = [var.vpc_cidr_block]
      cidr_ipv6   = []
      from_port   = 8000
      to_port     = 8000
      ip_protocol = "tcp"
      description = ""
    }
  ]

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_self                          = false
  egress_custom_rules                  = []
}
