# module "nats-load-test" {
#   source                                  = "../modules/ec2_instance"
#   region                                  = var.region
#   availability_zone                       = "us-east-1a"
#   instance_type                           = "t4g.medium"
#   root_block_device_volume_size           = 100
#   root_block_device_iops                  = var.nats_root_block_device_iops
#   root_block_device_throughput            = var.nats_root_block_device_throughput
#   root_block_device_volume_type           = var.nats_root_block_device_volume_type
#   subnet_id                               = module.main_private_subnet_1.subnet_id
#   vpc_security_group_ids                  = ["${aws_security_group.nats_sg.id}"]
#   key_name                                = "nats-load-test-ssh-key"
#   root_block_device_encrypted             = true
#   root_block_device_delete_on_termination = false
#   ami                                     = data.aws_ami.graviton_instance_ami.id

#   iam_instance_profile           = aws_iam_instance_profile.CloudWatchAgentServerProfile.name
#   service_discovery_service_name = "nats-load-test"
#   namespace_id                   = module.ecs_cluster.service_discovery_namespace_id

#   tags = {
#     Name        = "nats-load-test"
#     Environment = "dev"
#     Product     = "ravenclaw"
#     Team        = "infra"
#     Application = "nats"
#   }
# }


variable "inline_load_testing_image_uri" {
  type    = string
  default = "771151923073.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-3a18f75-20250401184505"
}

variable "inline_load_testing_log_group" {
  type    = string
  default = "/ecs/inline-load-testing"
}

variable "NATS_LOAD_TESTING_SERVER_URL" {
  type = string
}

variable "setup_load_testing_image_uri" {
  type    = string
  default = "771151923073.dkr.ecr.us-east-1.amazonaws.com/ravenclaw:ravenclaw-dev-3a18f75-20250401184505"
}

variable "SMARTHOST_LOAD_TESTING_TLS_CERT" {
  type = string
}

variable "SMARTHOST_LOAD_TESTING_TLS_KEY" {
  type = string
}

resource "aws_secretsmanager_secret_version" "NATS_LOAD_TESTING_SERVER_URL" {
  secret_id     = aws_secretsmanager_secret.NATS_LOAD_TESTING_SERVER_URL.arn
  secret_string = var.NATS_LOAD_TESTING_SERVER_URL
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret" "NATS_LOAD_TESTING_SERVER_URL" {
  name                    = "NATS_LOAD_TESTING_SERVER_URL"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Environment = "dev"
    Name        = "nats-load-testing-server-url"
    Product     = "nats"
    Team        = "infra"
  }
}

resource "aws_secretsmanager_secret_version" "SMARTHOST_LOAD_TESTING_TLS_CERT" {
  secret_id     = aws_secretsmanager_secret.SMARTHOST_LOAD_TESTING_TLS_CERT.arn
  secret_string = var.SMARTHOST_LOAD_TESTING_TLS_CERT
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret" "SMARTHOST_LOAD_TESTING_TLS_CERT" {
  name                    = "SMARTHOST_LOAD_TESTING_TLS_CERT"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Environment = "dev"
    Name        = "smarthost-load-testing-tls-cert"
    Product     = "gosmtp"
    Team        = "infra"
  }
}

resource "aws_secretsmanager_secret_version" "SMARTHOST_LOAD_TESTING_TLS_KEY" {
  secret_id     = aws_secretsmanager_secret.SMARTHOST_LOAD_TESTING_TLS_KEY.arn
  secret_string = var.SMARTHOST_LOAD_TESTING_TLS_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret" "SMARTHOST_LOAD_TESTING_TLS_KEY" {
  name                    = "SMARTHOST_LOAD_TESTING_TLS_KEY"
  kms_key_id              = aws_kms_key.secretsmanager.key_id
  recovery_window_in_days = 7

  tags = {
    Environment = "dev"
    Name        = "smarthost-load-testing-tls-key"
    Product     = "gosmtp"
    Team        = "infra"
  }
}


module "inline_load_testing_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([
    {
      "command" : [
        "gateway",
        "inline"
      ],
      "cpu" : 0,
      "environment" : [{
        "name" : "AWS_REGION",
        "value" : var.region
        }, {
        "name" : "DEPLOYMENT_ENV",
        "value" : var.DEPLOYMENT_ENV
        }, {
        "name" : "NATS_FETCH_SIZE",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_BACKOFF_IN_SEC",
        "value" : "5"
        }, {
        "name" : "NATS_MAX_PENDING",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_RECONNECT",
        "value" : "10"
        }, {
        "name" : "NATS_MAX_RETRIES",
        "value" : "0"
        }, {
        "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
        "value" : "60"
        }, {
        "name" : "NATS_RECONNECT_WAIT_IN_SEC",
        "value" : "5"
        }, {
        "name" : "PG_DB",
        "value" : var.PG_DB
        }, {
        "name" : "PG_HOST",
        "value" : module.rds.db_host
        }, {
        "name" : "PG_MAX_IDLE_CONNECTION",
        "value" : "5"
        }, {
        "name" : "PG_MAX_OPEN_CONNECTION",
        "value" : "20"
        }, {
        "name" : "PG_PASSWORD",
        "value" : module.rds.db_password
        }, {
        "name" : "PG_PORT",
        "value" : "5432"
        }, {
        "name" : "PG_SSL",
        "value" : "disable"
        }, {
        "name" : "PG_USERNAME",
        "value" : module.rds.db_username
        }, {
        "name" : "S3_KMS_KEY_ARN",
        "value" : aws_kms_key.dev-misc-s3.arn
        }, {
        "name" : "SMARTHOST_DOMAIN",
        "value" : "mail.ravenmail.dev"
        }, {
        "name" : "SMARTHOST_PORT",
        "value" : "2525"
        }, {
        "name" : "DKIM_ENABLED",
        "value" : "true"
        }, {
        "name" : "DKIM_SELECTOR",
        "value" : "devravenmail"
      }],

      "essential" : true,
      "image" : var.inline_load_testing_image_uri,
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-create-group" : "true",
          "awslogs-group" : var.inline_load_testing_log_group,
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "mountPoints" : [],
      "name" : "inline-load-testing",
      "portMappings" : [
        {
          "appProtocol" : "http",
          "containerPort" : 2525,
          "hostPort" : 2525,
          "name" : "http",
          "protocol" : "tcp"
        }
      ],
      "secrets" : [
        {
          "name" : "NATS_SERVER_URL",
          "valueFrom" : aws_secretsmanager_secret.NATS_LOAD_TESTING_SERVER_URL.arn
        },
        {
          "name" : "SMARTHOST_TLS_CERT",
          "valueFrom" : aws_secretsmanager_secret.SMARTHOST_LOAD_TESTING_TLS_CERT.arn
        },
        {
          "name" : "SMARTHOST_TLS_KEY",
          "valueFrom" : aws_secretsmanager_secret.SMARTHOST_LOAD_TESTING_TLS_KEY.arn
        },
        {
          "name" : "DKIM_PRIVATE_KEY",
          "valueFrom" : aws_secretsmanager_secret.DKIM_PRIVATE_KEY.arn
        },
      ],
      "systemControls" : [],
      "volumesFrom" : []
    }
  ])

  cpu                      = "512"
  execution_role_arn       = aws_iam_role.task_execution_role.arn
  family                   = "inline-load-testing"
  memory                   = "1024"
  task_role_arn            = aws_iam_role.task_role.arn
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  track_latest             = "false"

  tags = {
    Environment = "dev"
    Name        = "inline-load-testing-task-definition"
    Product     = "inline"
    Team        = "infra"
  }
}

module "inline_load_testing_service" {
  source                         = "../modules/ecs_service"
  region                         = var.region
  namespace_id                   = module.ecs_cluster.service_discovery_namespace_id
  service_discovery_service_name = "inline-load-testing"
  cluster                        = module.ecs_cluster.ecs_cluster_arn
  security_groups_id             = [aws_security_group.gosmtp_service_sg.id]
  subnets_id                     = [module.main_private_subnet_1.subnet_id, module.main_private_subnet_2.subnet_id, module.main_private_subnet_3.subnet_id]
  name                           = "inline-load-testing"
  desired_count                  = 1
  task_definition_arn            = module.inline_load_testing_task_definition.arn
  availability_zone_rebalancing  = "ENABLED"

  # target_group_arn = aws_lb_target_group.gosmtp.arn
  # container_name   = "inline-load-testing"
  # container_port   = 2525

  capacity_provider_strategies = [
    {
      capacity_provider = "FARGATE"
      base              = 0
      weight            = 3
    },
    {
      capacity_provider = "FARGATE_SPOT"
      base              = 0
      weight            = 2
    }
  ]

  service_discovery_svc_tags = {
    Name        = "inline-load-testing-service-sd"
    Team        = "infra"
    Product     = "inline"
    Environment = "dev"
  }

  ecs_service_tags = {
    Name        = "inline-load-testing-service"
    Team        = "infra"
    Product     = "inline"
    Environment = "dev"
  }
}

########### go smtp
module "setup_load_testing_task_definition" {
  source = "../modules/ecs_task_definitions"
  region = var.region
  container_definitions_json = jsonencode([{
    "cpu" : 0,
    "command" : [
      "setup"
    ],
    "secrets" : [{
      "name" : "NATS_SERVER_URL",
      "valueFrom" : aws_secretsmanager_secret.NATS_LOAD_TESTING_SERVER_URL.arn
      }, {
      "name" : "PG_PASSWORD",
      "valueFrom" : aws_secretsmanager_secret.PG_PASSWORD.arn
    }],
    "environment" : [{
      "name" : "PG_DB",
      "value" : var.PG_DB
      }, {
      "name" : "PG_HOST",
      "value" : module.rds.db_host
      # "value" : "ravenclaw-read-replica-1.cf0c0yyq0e9f.ap-south-1.rds.amazonaws.com"
      }, {
      "name" : "PG_PORT",
      "value" : var.PG_PORT
      }, {
      "name" : "PG_USERNAME",
      "value" : module.rds.db_username
      }, {
      "name" : "PG_SSL",
      "value" : var.PG_SSL
      }, {
      "name" : "NATS_MAX_RETRIES",
      "value" : "0"
      }, {
      "name" : "NATS_MAX_BACKOFF_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_RECONNECT_WAIT_IN_SEC",
      "value" : "5"
      }, {
      "name" : "NATS_MAX_RECONNECT",
      "value" : "10"
      }, {
      "name" : "NATS_MAX_PENDING",
      "value" : "20"
      }, {
      "name" : "NATS_PROCESS_TIMEOUT_IN_SEC",
      "value" : "60"
      }, {
      "name" : "NATS_FETCH_SIZE",
      "value" : "5"
    }],
    "environmentFiles" : [],
    "essential" : true,
    "image" : var.setup_load_testing_image_uri,
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-create-group" : "true",
        "awslogs-group" : "/ecs/setup-load-testing",
        "awslogs-region" : var.region,
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "secretOptions" : [],
    "mountPoints" : [],
    "name" : "setup-load-testing",
    "portMappings" : [],
    "systemControls" : [],
    "volumesFrom" : [],
    "ulimits" : []
  }])

  cpu                = var.setup_cpu
  execution_role_arn = aws_iam_role.task_execution_role.arn
  family             = "setup-load-testing"
  memory             = var.setup_memory
  task_role_arn      = aws_iam_role.task_role.arn

  tags = {
    Environment = "dev"
    Name        = "setup-load-testing-task-definition"
    Product     = "inline"
    Team        = "infra"
  }
}
