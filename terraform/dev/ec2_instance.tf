module "llama" {
  source                        = "../modules/ec2_instance"
  region                        = var.region
  availability_zone             = var.llama_availability_zone
  instance_type                 = var.llama_instance_type
  root_block_device_volume_size = var.llama_root_block_device_volume_size
  root_block_device_iops        = var.llama_root_block_device_iops
  root_block_device_throughput  = var.llama_root_block_device_throughput
  root_block_device_volume_type = var.llama_root_block_device_volume_type
  subnet_id                     = module.main_private_subnet_3.subnet_id
  vpc_security_group_ids        = ["${aws_security_group.llama_sg.id}"]
  key_name                      = var.ssh_key_name
  iam_instance_profile          = aws_iam_instance_profile.CloudWatchAgentServerProfile.name

  tags = var.llama_instance_tags
}

module "bastion" {
  source                        = "../modules/ec2_instance"
  region                        = var.region
  associate_public_ip_address   = true
  availability_zone             = var.bastion_availability_zone
  instance_type                 = var.bastion_instance_type
  root_block_device_volume_size = var.bastion_root_block_device_volume_size
  root_block_device_iops        = var.bastion_root_block_device_iops
  root_block_device_throughput  = var.bastion_root_block_device_throughput
  root_block_device_volume_type = var.bastion_root_block_device_volume_type
  subnet_id                     = module.main_public_subnet_1.subnet_id
  vpc_security_group_ids        = ["${aws_security_group.bastion_sg.id}"]
  key_name                      = var.ssh_key_name
  iam_instance_profile          = aws_iam_instance_profile.bastion_iam_profile.name

  tags = var.bastion_instance_tags
}


resource "aws_iam_role" "ec2_role" {
  name = "ec2-role"

  assume_role_policy = <<EOF
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Principal": {
          "Service": "ec2.amazonaws.com"
        },
        "Action": "sts:AssumeRole"
      }
    ]
  }
  EOF
}

resource "aws_iam_policy" "cloudwatch_policy" {
  name        = "cloudwatch-policy"
  description = "Policy for CloudWatch access"

  policy = <<EOF
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Action": [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ],
        "Resource": "*"
      }
    ]
  }
  EOF
}

resource "aws_iam_role_policy_attachment" "ec2_role_policy_attachment" {
  role       = aws_iam_role.ec2_role.name
  policy_arn = aws_iam_policy.cloudwatch_policy.arn
}

resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "ec2-instance-profile"
  role = aws_iam_role.ec2_role.name
}

data "aws_ami" "graviton_instance_ami" {
  most_recent = true
  owners      = ["amazon"]
  filter {
    name   = "architecture"
    values = ["arm64"]
  }
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*"]
  }
}

############ Annotation instance
##### EC2 Test Instance #####
# module "annotations" {
#   source                                  = "../modules/ec2_instance"
#   region                                  = var.region
#   availability_zone                       = "us-east-1c"
#   instance_type                           = "t4g.micro"
#   root_block_device_volume_size           = 100
#   root_block_device_iops                  = 3000
#   root_block_device_throughput            = 125
#   root_block_device_volume_type           = "gp3"
#   subnet_id                               = module.main_public_subnet_3.subnet_id
#   vpc_security_group_ids                  = ["${aws_security_group.annotations_sg.id}"]
#   key_name                                = "annotations-ec2-dev"
#   iam_instance_profile                    = aws_iam_instance_profile.annotations_ec2_instance_profile.name
#   associate_public_ip_address             = false
#   root_block_device_encrypted             = true
#   root_block_device_delete_on_termination = false
#   ami                                     = data.aws_ami.graviton_instance_ami.id

#   # service_discovery_service_name = "annotations"
#   # namespace_id                   = aws_service_discovery_private_dns_namespace.dev.id

#   tags = {
#     Name        = "annotations-instance"
#     Team        = "infra"
#     Product     = "ML"
#     Environment = "prod"
#   }
# }

##### IAM Role for EC2 ######
# Define the IAM Role
resource "aws_iam_role" "annotation_ec2_role" {
  name = "AnnotationEC2DevRole"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "ec2.amazonaws.com"
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })
}

# Define the IAM Policy for the EC2 Role
resource "aws_iam_policy" "annotation_ec2_policy" {
  name = "AnnotationEC2DevPolicy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      # Read permission for a specific RDS instance
      {
        "Effect" : "Allow",
        "Action" : [
          "rds:DescribeDBInstances",
          "rds:DescribeDBSnapshots",
          "rds:ListTagsForResource"
        ],
        "Resource" : "*"
      },

      # Read permissions to all S3 buckets
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:List*",
          "s3:Get*"
        ],
        "Resource" : [
          "arn:aws:s3:::*"
        ]
      },

      # Write permission to a specific S3 bucket
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:PutObject",
          "s3:DeleteObject"
        ],
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey"
        ],
        Resource = "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey",
          "secretsmanager:Batch*",
          "secretsmanager:Describe*",
          "secretsmanager:Get*",
          "secretsmanager:List*",
          "secretsmanager:Validate*",
          "ssm:*",
          "s3:Describe*",
          "s3:Get*",
          "s3:Put*",
          "s3:Restore*",
          "s3:Tag*",
          "s3:List*",
          "s3:Untag*",
          "s3:Update*",
          "s3:Create*",
          "ecr:Get*",
          "ecr:Describe*",
          "ecr:Batch*",
          "ecr:List*",
          "ecr:Validate*",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "ses:Create*",
          "ses:Send*",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ],
        Resource = "*"
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "annotations_ec2_role_policy_attachment" {
  role       = aws_iam_role.annotation_ec2_role.name
  policy_arn = aws_iam_policy.annotation_ec2_policy.arn
}

# Define the IAM Instance Profile
resource "aws_iam_instance_profile" "annotations_ec2_instance_profile" {
  name = "AnnotationsEC2InstanceDevProfile"
  role = aws_iam_role.annotation_ec2_role.name
}

resource "aws_security_group" "annotations_sg" {
  description = "SG for annotations agrilla ec2 instance"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "6900"
    protocol    = "tcp"
    self        = "false"
    to_port     = "6900"
  }

  name = "annotations_ec2_sg"
  tags = {
    Name        = "annotations-ec2-sg"
    Environment = "prod"
    Team        = "infra"
    Product     = "ML"
  }
  vpc_id = module.vpc_main.vpc_id
}
