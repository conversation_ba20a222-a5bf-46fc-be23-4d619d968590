module "microsoft_smtp_nlb_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "microsoft-gosmtp-server-nlb-sg-new"
  description = "Security group for GOSMTP server Network Load Balancer - Microsoft CIDRs"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "microsoft-gosmtp-server-nlb-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }

  # ───────────────────────────────────────────────────────────
  # Ingress: SMTP port 25 from Microsoft public CIDRs
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs = [
    "************/31",
    "************/31",
    "************/22",
    "************/20",
    "*********/15",
    "*********/13",
    "**********/15",
    "**********/16",
    "*********/14",
    "**********/14",
    "**********/16",
    "**********/17",
    "************/32",
    "**********/17",
    "**************/32",
    "***********/16",
    "************/22",
    "**************/32",
  ]                                         
# Microsoft SMTP IPv4 ranges  [oai_citation:3‡Microsoft Learn](https://learn.microsoft.com/en-us/microsoft-365/enterprise/microsoft-365-endpoints?view=o365-worldwide)

  ingress_ipv6_cidrs = [
    "2a01:111:f400::/48",
    "2a01:111:f403::/48",
    "2a01:111:f403::/49",
    "2a01:111:f403:8000::/51",
    "2a01:111:f403:c000::/51",
    "2a01:111:f403:f000::/52",
    "2603:1006::/40",
    "2603:1016::/36",
    "2603:1026::/36",
    "2603:1036::/36",
    "2603:1046::/36",
    "2603:1056::/36",
    "2620:1ec:4::152/128",
    "2620:1ec:4::153/128",
    "2620:1ec:c::10/128",
    "2620:1ec:c::11/128",
    "2620:1ec:d::10/128",
    "2620:1ec:d::11/128",
    "2620:1ec:8f0::/46",
    "2620:1ec:900::/46",
    "2620:1ec:a92::152/128",
    "2620:1ec:a92::153/128",
  ]                                         

# Microsoft SMTP IPv6 ranges  [oai_citation:4‡Microsoft Learn](https://learn.microsoft.com/en-us/microsoft-365/enterprise/microsoft-365-endpoints?view=o365-worldwide)
  ingress_from_port    = 25
  ingress_to_port      = 25
  ingress_protocol     = "tcp"
  ingress_self         = false

  # ───────────────────────────────────────────────────────────
  # Ingress: internal ports 587 (submission) & 80 (health) from VPC
  # ───────────────────────────────────────────────────────────
  # We pass these as custom rules so they map correctly alongside the above
  ingress_custom_rules = [
    {
      cidr_ipv4    = [
    "************/31",
    "************/31",
    "************/22",
    "************/20",
    "*********/15",
    "*********/13",
    "**********/15",
    "**********/16",
    "*********/14",
    "**********/14",
    "**********/16",
    "**********/17",
    "************/32",
    "**********/17",
    "**************/32",
    "***********/16",
    "************/22",
    "**************/32",
  ]
      cidr_ipv6    = [
    "2a01:111:f400::/48",
    "2a01:111:f403::/48",
    "2a01:111:f403::/49",
    "2a01:111:f403:8000::/51",
    "2a01:111:f403:c000::/51",
    "2a01:111:f403:f000::/52",
    "2603:1006::/40",
    "2603:1016::/36",
    "2603:1026::/36",
    "2603:1036::/36",
    "2603:1046::/36",
    "2603:1056::/36",
    "2620:1ec:4::152/128",
    "2620:1ec:4::153/128",
    "2620:1ec:c::10/128",
    "2620:1ec:c::11/128",
    "2620:1ec:d::10/128",
    "2620:1ec:d::11/128",
    "2620:1ec:8f0::/46",
    "2620:1ec:900::/46",
    "2620:1ec:a92::152/128",
    "2620:1ec:a92::153/128",
  ]                     
      from_port    = 587
      to_port      = 587
      ip_protocol  = "tcp"
      description  = "Internal SMTP submission"
    }
    # {
    #   cidr_ipv4    = var.vpc_cidr_block
    #   from_port    = 80
    #   to_port      = 80
    #   ip_protocol  = "tcp"
    #   description  = "Health check HTTP"
    # }
  ]

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound (0.0.0.0/0, protocol -1)
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs      = ["0.0.0.0/0"]
  egress_ipv6_cidrs      = []
  egress_from_port       = 0
  egress_to_port         = 0
  egress_protocol        = "-1"
  egress_self            = false
  egress_custom_rules    = []
}


module "google_smtp_nlb_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "google-gosmtp-server-nlb-sg-new"
  description = "Security group for GOSMTP server Network Load Balancer - Google CIDRs"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "google-gosmtp-server-nlb-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }

  # Public Google SMTP port 25 ranges
  ingress_ipv4_cidrs = [
    "************/24",
    "************/19",
    "**********/20",
    "***********/20",
    "***********/18",
    "**********/16",
    "***********/21",
    "***********/16",
    "************/17",
    "************/19",
    "************/19",
    "***********/19",
    "************/19",
    "*************/19",
    "************/24",
    "*************/19",
    "**********/16",
    "*************/24",
    "************/21",
    "*************/20",
    "***********/22",
    "*************/20",
    "************/20",
    "************/24",
    "*************/24",
  ]
  ingress_ipv6_cidrs = [
    "2001:4860:4000::/36",
    "2404:6800:4000::/36",
    "2607:f8b0:4000::/36",
    "2800:3f0:4000::/36",
    "2a00:1450:4000::/36",
    "2c0f:fb50:4000::/36",
    "2600:1901:101::8/126",
    "2600:1901:101::14/126",
    "2600:1901:101::10/126",
    "2600:1901:101::c/126",
    "2600:1901:101::4/126",
    "2600:1901:101::/126",
  ]
  ingress_from_port    = 25
  ingress_to_port      = 25
  ingress_protocol     = "tcp"
  ingress_self         = false
  ingress_custom_rules = [
    {
      cidr_ipv4   = [
    "************/24",
    "************/19",
    "**********/20",
    "***********/20",
    "***********/18",
    "**********/16",
    "***********/21",
    "***********/16",
    "************/17",
    "************/19",
    "************/19",
    "***********/19",
    "************/19",
    "*************/19",
    "************/24",
    "*************/19",
    "**********/16",
    "*************/24",
    "************/21",
    "*************/20",
    "***********/22",
    "*************/20",
    "************/20",
    "************/24",
    "*************/24",
  ]
      cidr_ipv6   = [
    "2001:4860:4000::/36",
    "2404:6800:4000::/36",
    "2607:f8b0:4000::/36",
    "2800:3f0:4000::/36",
    "2a00:1450:4000::/36",
    "2c0f:fb50:4000::/36",
    "2600:1901:101::8/126",
    "2600:1901:101::14/126",
    "2600:1901:101::10/126",
    "2600:1901:101::c/126",
    "2600:1901:101::4/126",
    "2600:1901:101::/126",
  ]

      from_port   = 587
      to_port     = 587
      ip_protocol = "tcp"
      description = "Internal SMTP submission"
    },
    #{
    #  cidr_ipv4   = var.vpc_cidr_block
    #  from_port   = 80
    #  to_port     = 80
    #  ip_protocol = "tcp"
    #  description = "Health check HTTP"
    #}
  ]

  egress_ipv4_cidrs    = ["0.0.0.0/0"]
  egress_ipv6_cidrs    = []
  egress_from_port     = 0
  egress_to_port       = 0
  egress_protocol      = "-1"
  egress_self          = false
  egress_custom_rules  = []
}
