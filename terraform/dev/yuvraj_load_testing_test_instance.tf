data "aws_ami" "yuvraj_load_testing_test_instance" {
  most_recent = true
  owners      = ["amazon"]
  filter {
    name   = "architecture"
    values = ["arm64"]
  }
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*"]
  }
}

variable "yuvraj_load_testing_test_instance_tags" {
  type = map(string)
  default = {
    Name        = "yuvraj-load-testing-instance"
    Environment = "test"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

resource "aws_security_group" "yuvraj_load_testing_instance_sg" {
  description = "SG for test yuvraj load testing instance"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  name   = "yuvraj-load-testing-instance-sg"
  vpc_id = module.vpc_main.vpc_id
  tags = {
    Name        = "yuvraj-load-testing-instance-sg"
    Environment = "test"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}


# resource "aws_instance" "yuvraj_load_testing_test_instance" {
#   ami                         = data.aws_ami.yuvraj_load_testing_test_instance.id
#   associate_public_ip_address = true
#   availability_zone           = "us-east-1c"
#   key_name                    = "yuvraj-load-testing-instance-key"

#   disable_api_stop        = "false"
#   disable_api_termination = "false"
#   ebs_optimized           = "false"
#   subnet_id               = module.main_private_subnet_3.subnet_id


#   get_password_data                    = "false"
#   hibernation                          = "false"
#   instance_initiated_shutdown_behavior = "stop"
#   instance_type                        = "t4g.xlarge"
#   ipv6_address_count                   = 0

#   vpc_security_group_ids = ["${aws_security_group.yuvraj_load_testing_instance_sg.id}"]

#   maintenance_options {
#     auto_recovery = "default"
#   }

#   metadata_options {
#     http_endpoint               = "enabled"
#     http_protocol_ipv6          = "disabled"
#     http_put_response_hop_limit = 2
#     http_tokens                 = "required"
#     instance_metadata_tags      = "disabled"
#   }

#   monitoring                 = false
#   placement_partition_number = 0

#   private_dns_name_options {
#     enable_resource_name_dns_a_record    = false
#     enable_resource_name_dns_aaaa_record = false
#     hostname_type                        = "ip-name"
#   }

#   root_block_device {
#     delete_on_termination = "false"
#     encrypted             = "false"
#     iops                  = 3000
#     throughput            = 125
#     volume_size           = 50
#     volume_type           = "gp3"
#     tags = merge(var.yuvraj_load_testing_test_instance_tags,
#       { Name = "${var.yuvraj_load_testing_test_instance_tags["Name"]}-volume" }
#     )
#   }
#   # iam_instance_profile = aws_iam_instance_profile.RohitTestInstanceProfile.name
#   tags    = var.yuvraj_load_testing_test_instance_tags
#   tenancy = "default"
# }

# resource "aws_iam_role" "YuvrajLoadTestingInstanceRole" {
#   name = "YuvrajLoadTestingInstanceRoleDev"
#   assume_role_policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Effect = "Allow"
#         Principal = {
#           Service = "ec2.amazonaws.com"
#         }
#         Action = "sts:AssumeRole"
#       }
#     ]
#   })
# }

# resource "aws_iam_policy" "YuvrajLoadTestingInstancePolicy" {
#   name        = "YuvrajLoadTestingInstancePolicyDev"
#   description = "Policy for EC2 Test Instace with necessary permissions"
#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Effect = "Allow",
#         Action = [
#           "rds:Describe*",
#           "rds:ListTagsForResource",
#           "ec2:DescribeAccountAttributes",
#           "ec2:DescribeAvailabilityZones",
#           "ec2:DescribeInternetGateways",
#           "ec2:DescribeSecurityGroups",
#           "ec2:DescribeSubnets",
#           "ec2:DescribeVpcAttribute",
#           "ec2:DescribeVpcs",
#           "cloudwatch:GetMetricStatistics",
#           "cloudwatch:ListMetrics",
#           "cloudwatch:GetMetricData",
#           "logs:DescribeLogStreams",
#           "logs:GetLogEvents",
#           "devops-guru:GetResourceCollection",
#           "devops-guru:SearchInsights",
#           "devops-guru:ListAnomaliesForInsight",
#           "s3:Get*",
#           "s3:List*",
#           "s3:Describe*",
#           "s3-object-lambda:Get*",
#           "s3-object-lambda:List*",
#           "kms:*",
#           "bedrock:invokeModel"
#         ],
#         Resource = "*"
#       }
#     ]
#   })
# }

# resource "aws_iam_role_policy_attachment" "YuvrajLoadTestingInstancePolicyAttachment" {
#   role       = aws_iam_role.YuvrajLoadTestingInstanceRole.name
#   policy_arn = aws_iam_policy.YuvrajLoadTestingInstancePolicy.arn
# }

# resource "aws_iam_instance_profile" "RohitTestInstanceProfile" {
#   name = "RohitTestInstanceProfile"
#   role = aws_iam_role.YuvrajLoadTestingInstanceRole.name
# }
