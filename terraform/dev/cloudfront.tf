resource "aws_cloudfront_origin_access_identity" "webui_build" {
  comment = "WebUI Build - Cloudfront Origin Access Identity"
}

resource "aws_cloudfront_cache_policy" "webui_build" {
  name        = "webui_build-cache-policy"
  comment     = "WebUI Build Cache policy for cloud distribution"
  default_ttl = 50
  max_ttl     = 100
  min_ttl     = 1
  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "none"
    }
    headers_config {
      header_behavior = "whitelist"
      headers {
        items = ["Origin", "Content-Type"] # Specify the headers to whitelist
      }
    }
    query_strings_config {
      query_string_behavior = "none"
    }

    enable_accept_encoding_gzip   = true
    enable_accept_encoding_brotli = true
  }
}

resource "aws_cloudfront_distribution" "s3_distribution" {
  origin {
    domain_name = aws_s3_bucket.webui_build.bucket_regional_domain_name
    # s3_origin_config {
    #   origin_access_identity = aws_cloudfront_origin_access_identity.webui_build.cloudfront_access_identity_path
    # }
    origin_access_control_id = aws_cloudfront_origin_access_control.webui.id

    origin_id = aws_s3_bucket.webui_build.id
  }


  enabled             = true
  is_ipv6_enabled     = false
  comment             = "Web UI Static Cloudfront Distribution"
  default_root_object = "index.html"
  aliases             = ["dev.dashboard.ravenmail.io"]

  # logging_config {
  #   include_cookies = false
  #   bucket          = "mylogs.s3.amazonaws.com"
  #   prefix          = "webui"
  # }

  default_cache_behavior {
    allowed_methods            = ["GET", "HEAD"]
    cached_methods             = ["GET", "HEAD"]
    target_origin_id           = aws_s3_bucket.webui_build.id
    cache_policy_id            = aws_cloudfront_cache_policy.webui_build.id
    response_headers_policy_id = aws_cloudfront_response_headers_policy.no_cache.id

    # forwarded_values {
    #   query_string = false

    #   cookies {
    #     forward = "none"
    #   }
    #   headers = ["Origin", "Content-Type", "Accept-Encoding"]
    # }

    compress = true

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
  }

  ordered_cache_behavior {
    path_pattern     = "/index.html"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = aws_s3_bucket.webui_build.id
    cache_policy_id  = aws_cloudfront_cache_policy.webui_build.id

    response_headers_policy_id = aws_cloudfront_response_headers_policy.no_cache.id

    viewer_protocol_policy = "redirect-to-https"
    compress               = true
  }


  custom_error_response {
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 0
  }

  custom_error_response {
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 0
  }

  custom_error_response {
    error_code            = 400
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 0
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
      locations        = []
    }
  }

  tags = var.webui_build_cloudfront_tags

  price_class = "PriceClass_100"

  viewer_certificate {
    # cloudfront_default_certificate = true
    acm_certificate_arn      = "arn:aws:acm:us-east-1:771151923073:certificate/89630d40-4fb1-49a6-8c4e-2bae408ba7ef"
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2018"
  }

  depends_on = [aws_s3_bucket_policy.webui_build_oai]

  lifecycle {
    ignore_changes = [
      default_cache_behavior[0].default_ttl,
      default_cache_behavior[0].max_ttl,
    ]
  }
}

resource "aws_cloudfront_response_headers_policy" "no_cache" {
  name    = "NoCachePolicyForIndex"
  comment = "Set Cache-Control: no-cache for index.html"
  security_headers_config {
    # Configure the HSTS header (Strict-Transport-Security)
    strict_transport_security {
      override                   = true
      access_control_max_age_sec = 63072000 # 2 years in seconds
      include_subdomains         = true
      preload                    = true
    }
    # Optionally, add additional security headers like X-Content-Type-Options, etc.
    content_type_options {
      override = true
    }
    # frame_options {
    #   override     = true
    #   frame_option = "SAMEORIGIN"
    # }
    # referrer_policy {
    #   override        = true
    #   referrer_policy = "same-origin"
    # }
    xss_protection {
      override   = true
      protection = true
      mode_block = true
    }
  }

  custom_headers_config {
    items {
      header   = "Cache-Control"
      value    = "no-cache"
      override = true
    }
  }

  remove_headers_config {
    items {
      header = "x-amz-server-side-encryption-aws-kms-key-id"
    }

    items {
      header = "X-Amz-Server-Side-Encryption"
    }

    items {
      header = "X-Amz-Server-Side-Encryption-Bucket-Key-Enabled"
    }

    items {
      header = "Etag"
    }

    items {
      header = "Server"
    }

    items {
      header = "X-Amz-Version-id"
    }
  }
}

resource "aws_cloudfront_origin_access_control" "webui" {
  name                              = "webui-kms-oac"
  description                       = "Webui kms encrypted oac access"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}
