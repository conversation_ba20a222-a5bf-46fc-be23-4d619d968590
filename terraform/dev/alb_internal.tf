resource "aws_lb" "internal" {
  name               = "dev-alb"
  internal           = "true"
  load_balancer_type = "application"
  security_groups    = [aws_security_group.internal_alb_loadbalancer_sg.id]
  subnets            = [module.main_public_subnet_1.subnet_id, module.main_public_subnet_2.subnet_id, module.main_public_subnet_3.subnet_id]

  enable_deletion_protection = "true"
  idle_timeout               = 180

  tags = {
    Name        = "internal-alb"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "dev"
  }
}

resource "aws_lb_target_group" "internal_ml_inference" {
  name        = "ml-inference-target-group"
  target_type = "ip"
  port        = 8000
  protocol    = "HTTP"
  vpc_id      = module.vpc_main.vpc_id
  tags = {
    Name        = "ml-inference-target-group"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "dev"
  }
  health_check {
    path = "/health"
  }
}

resource "aws_lb_listener" "internal_ml_inference" {
  load_balancer_arn = aws_lb.internal.arn
  port              = "80"
  protocol          = "HTTP"
  # ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06" 

  # certificate_arn = var.certificate_arn

  # default_action {
  #   type             = var.lb_listener_default_action_type
  #   target_group_arn = aws_lb_target_group.main.arn
  # }

  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "Either endpoint or IP may not be valid"
      status_code  = "200"
    }
  }

  tags = var.alb_listener_tags
}

resource "aws_lb_listener_rule" "internal_ml_inference" {
  listener_arn = aws_lb_listener.internal_ml_inference.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.internal_ml_inference.arn
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }
  # condition {
  #   source_ip {
  #     values = ["${module.bastion.ec2_public_ip}/32"]
  #   }
  # }

  tags = {
    Name        = "internal-ml-inference-listener-rule"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "dev"
  }
}
