# resource "aws_acm_certificate" "vpn" {
#   domain_name       = "conn.ravenmail.dev"
#   validation_method = "DNS"

#   tags = {
#     Environment = "dev"
#     Product     = "AWS"
#     Team        = "infra"
#     Name        = "vpn-dev-acm"
#   }

#   lifecycle {
#     create_before_destroy = true
#   }
# }

# ##-----------------------------------------------------------------------------
# ## Labels module callled that will be used for naming and tags.
# ##-----------------------------------------------------------------------------
# module "labels" {
#   source  = "clouddrove/labels/aws"
#   version = "1.3.0"

#   name        = "ravenclaw-dev-vpn"
#   environment = "dev"
#   label_order = ["name", "environment"]
# }

# resource "tls_private_key" "ca" {
#   count     = 1
#   algorithm = "RSA"
#   rsa_bits  = 2048
# }

# ##-----------------------------------------------------------------------------
# ## tls_self_signed_cert (Resource) Creates a self-signed TLS certificate in PEM (RFC 1421) format.
# ##-----------------------------------------------------------------------------
# resource "tls_self_signed_cert" "ca" {
#   count           = 1
#   private_key_pem = join("", tls_private_key.ca[*].private_key_pem)

#   subject {

#     common_name  = format("%s-ca", module.labels.id)
#     organization = "ravenmail.io"
#   }

#   dns_names = ["conn.ravenmail.dev"]

#   validity_period_hours = 87600
#   is_ca_certificate     = true

#   allowed_uses = [
#     "cert_signing",
#     "crl_signing",
#   ]
# }

# ##-----------------------------------------------------------------------------
# ## aws_acm_certificate. The ACM certificate resource allows requesting and management of certificates from the Amazon Certificate Manager..
# ##-----------------------------------------------------------------------------
# resource "aws_acm_certificate" "ca" {
#   count            = 1
#   private_key      = join("", tls_private_key.ca[*].private_key_pem)
#   certificate_body = join("", tls_self_signed_cert.ca[*].cert_pem)

#   lifecycle {
#     create_before_destroy = true
#   }
# }

# resource "tls_private_key" "root" {
#   count     = 1
#   algorithm = "RSA"
# }

# ##-----------------------------------------------------------------------------
# ## Generates a Certificate Signing Request (CSR) in PEM format, which is the typical format used to request a certificate from a certificate authority.
# ##-----------------------------------------------------------------------------
# resource "tls_cert_request" "root" {
#   count           = 1
#   private_key_pem = join("", tls_private_key.server[*].private_key_pem)

#   subject {
#     common_name  = format("%s-client", module.labels.id)
#     organization = "ravenmail.io"
#   }
#   dns_names = ["conn.ravenmail.dev"]
# }

# ##-----------------------------------------------------------------------------
# ## Generates a Certificate Signing Request (CSR) in PEM format, which is the typical format used to request a certificate from a certificate authority.
# ##-----------------------------------------------------------------------------
# resource "tls_locally_signed_cert" "root" {
#   count                 = 1
#   cert_request_pem      = join("", tls_cert_request.root[*].cert_request_pem)
#   ca_private_key_pem    = join("", tls_private_key.ca[*].private_key_pem)
#   ca_cert_pem           = join("", tls_self_signed_cert.ca[*].cert_pem)
#   validity_period_hours = 87600

#   allowed_uses = [
#     "key_encipherment",
#     "digital_signature",
#     "client_auth",
#   ]
# }

# ##-----------------------------------------------------------------------------
# ## aws_acm_certificate. The ACM certificate resource allows requesting and management of certificates from the Amazon Certificate Manager..
# ##-----------------------------------------------------------------------------
# resource "aws_acm_certificate" "root" {
#   count             = 1
#   private_key       = join("", tls_private_key.server[*].private_key_pem)
#   certificate_body  = join("", tls_locally_signed_cert.root[*].cert_pem)
#   certificate_chain = join("", tls_self_signed_cert.ca[*].cert_pem)

#   lifecycle {
#     create_before_destroy = true
#   }
# }

# resource "tls_private_key" "server" {
#   count     = 1
#   algorithm = "RSA"
# }

# ##-----------------------------------------------------------------------------
# ## Generates a Certificate Signing Request (CSR) in PEM format, which is the typical format used to request a certificate from a certificate authority.
# ##-----------------------------------------------------------------------------
# resource "tls_cert_request" "server" {
#   count           = 1
#   private_key_pem = join("", tls_private_key.server[*].private_key_pem)

#   subject {
#     common_name  = format("%s-server", module.labels.id)
#     organization = "ravenmail.io"
#   }

#   dns_names = ["conn.ravenmail.dev"]
# }

# ##-----------------------------------------------------------------------------
# ## Generates a Certificate Signing Request (CSR) in PEM format, which is the typical format used to request a certificate from a certificate authority.
# ##-----------------------------------------------------------------------------
# resource "tls_locally_signed_cert" "server" {
#   count = 1

#   cert_request_pem      = join("", tls_cert_request.server[*].cert_request_pem)
#   ca_private_key_pem    = join("", tls_private_key.ca[*].private_key_pem)
#   ca_cert_pem           = join("", tls_self_signed_cert.ca[*].cert_pem)
#   validity_period_hours = 87600

#   allowed_uses = [
#     "key_encipherment",
#     "digital_signature",
#     "server_auth",
#   ]
# }

# ##-----------------------------------------------------------------------------
# ## aws_acm_certificate. The ACM certificate resource allows requesting and management of certificates from the Amazon Certificate Manager..
# ##-----------------------------------------------------------------------------
# resource "aws_acm_certificate" "server" {
#   count             = 1
#   private_key       = join("", tls_private_key.server[*].private_key_pem)
#   certificate_body  = join("", tls_locally_signed_cert.server[*].cert_pem)
#   certificate_chain = join("", tls_self_signed_cert.ca[*].cert_pem)

#   lifecycle {
#     create_before_destroy = true
#   }
# }

# ##-----------------------------------------------------------------------------
# ## aws_ec2_client_vpn_endpoint. Provides an AWS Client VPN endpoint for OpenVPN clients.
# ##-----------------------------------------------------------------------------
# resource "aws_ec2_client_vpn_endpoint" "default" {
#   count       = 1
#   description = module.labels.id
#   # server_certificate_arn = join("", aws_acm_certificate.server[*].arn)
#   server_certificate_arn = aws_acm_certificate.vpn.arn
#   client_cidr_block      = "**********/12"
#   split_tunnel           = true
#   vpc_id                 = module.vpc_main.vpc_id
#   session_timeout_hours  = 24
#   security_group_ids = [
#     aws_security_group.vpn_sg.id,
#   ]
#   vpn_port            = 443
#   self_service_portal = "enabled"
#   # dns_servers            = var.dns_servers

#   authentication_options {
#     type = "certificate-authentication"
#     # saml_provider_arn              = var.saml_arn
#     # self_service_saml_provider_arn = var.self_saml_arn
#     root_certificate_chain_arn = join("", aws_acm_certificate.root[*].arn)
#   }

#   connection_log_options {
#     enabled               = true
#     cloudwatch_log_group  = join("", aws_cloudwatch_log_group.vpn[*].name)
#     cloudwatch_log_stream = join("", aws_cloudwatch_log_stream.vpn[*].name)
#   }

#   tags = {
#     Name        = "ravenclaw-vpn-dev"
#     Environment = "dev"
#     Product     = "AWS"
#     Team        = "infra"
#   }
#   lifecycle {
#     ignore_changes = [
#       authentication_options
#     ]
#   }
# }

# ##-----------------------------------------------------------------------------
# ## aws_security_group. Provides a security group resource.
# ##-----------------------------------------------------------------------------
# #tfsec:ignore:aws-ec2-no-public-egress-sgr
# #tfsec:ignore:aws-ec2-add-description-to-security-group
# #tfsec:ignore:aws-ec2-add-description-to-security-group-rule
# # resource "aws_security_group" "this" {
# #   count       = 1
# #   name_prefix = "ravenclaw-dev-vpn"
# #   vpc_id      = module.vpn.vpc_id
# #   tags = {
# #     Name        = "ravenclaw-vpn-dev-sg"
# #     Environment = "dev"
# #     Product     = "AWS"
# #     Team        = "infra"
# #   }

# #   dynamic "ingress" {
# #     for_each = [
# #       {
# #         from_port = 0
# #         protocol  = -1
# #         self      = true
# #         to_port   = 0
# #       }
# #     ]
# #     content {
# #       self        = lookup(ingress.value, "self", true)
# #       from_port   = lookup(ingress.value, "from_port", 0)
# #       to_port     = lookup(ingress.value, "to_port", 0)
# #       protocol    = lookup(ingress.value, "protocol", "-1")
# #       description = lookup(ingress.value, "description", "")
# #     }
# #   }

# #   dynamic "egress" {
# #     for_each = [
# #       {
# #         from_port   = 0
# #         to_port     = 0
# #         protocol    = "-1"
# #         cidr_blocks = "0.0.0.0/0"
# #       }
# #     ]
# #     content {
# #       cidr_blocks = compact(split(",", lookup(egress.value, "cidr_blocks", "0.0.0.0/0")))
# #       from_port   = lookup(egress.value, "from_port", 0)
# #       to_port     = lookup(egress.value, "to_port", 0)
# #       protocol    = lookup(egress.value, "protocol", "-1")
# #     }
# #   }
# # }

# ##-----------------------------------------------------------------------------
# ## Provides network associations for AWS Client VPN endpoints.
# ##-----------------------------------------------------------------------------

# # resource "aws_ec2_client_vpn_network_association" "private" {
# #   count = length([
# #     "${module.main_private_subnet_1.subnet_id}",
# #     "${module.main_private_subnet_2.subnet_id}",
# #     "${module.main_private_subnet_3.subnet_id}",
# #   ])
# #   client_vpn_endpoint_id = join("", aws_ec2_client_vpn_endpoint.default[*].id)
# #   subnet_id = element([
# #     "${module.main_private_subnet_1.subnet_id}",
# #     "${module.main_private_subnet_2.subnet_id}",
# #     "${module.main_private_subnet_3.subnet_id}",
# #   ], count.index)
# # }

# # resource "aws_ec2_client_vpn_network_association" "public" {
# #   count = length([
# #     "${module.main_public_subnet_1.subnet_id}",
# #     "${module.main_public_subnet_2.subnet_id}",
# #     "${module.main_public_subnet_3.subnet_id}",
# #   ])
# #   client_vpn_endpoint_id = join("", aws_ec2_client_vpn_endpoint.default[*].id)
# #   subnet_id = element([
# #     "${module.main_public_subnet_1.subnet_id}",
# #     "${module.main_public_subnet_2.subnet_id}",
# #     "${module.main_public_subnet_3.subnet_id}",
# #   ], count.index)
# # }

# ##-----------------------------------------------------------------------------
# ## aws_cloudwatch_log_group Provides a CloudWatch Log Group resource.
# ##-----------------------------------------------------------------------------
# #tfsec:ignore:aws-cloudwatch-log-group-customer-key
# resource "aws_cloudwatch_log_group" "vpn" {
#   count             = 1
#   name              = format("/aws/vpn/%s/logs", module.labels.id)
#   retention_in_days = 90
#   tags = {
#     Name        = "ravenclaw-dev-vpn-logs"
#     Team        = "infra"
#     Product     = "AWS"
#     Environment = "dev"
#   }
# }

# ##-----------------------------------------------------------------------------
# ## A log stream is a sequence of log events that share the same source. Each separate source of logs in CloudWatch Logs makes up a separate log stream.
# ##-----------------------------------------------------------------------------
# resource "aws_cloudwatch_log_stream" "vpn" {
#   count          = 1
#   name           = format("%s-usage", module.labels.id)
#   log_group_name = join("", aws_cloudwatch_log_group.vpn[*].name)
# }

# ##-----------------------------------------------------------------------------
# ## Provides authorization rules for AWS Client VPN endpoints.
# ##-----------------------------------------------------------------------------
# resource "aws_ec2_client_vpn_authorization_rule" "vpn_auth" {
#   count                  = 1
#   client_vpn_endpoint_id = join("", aws_ec2_client_vpn_endpoint.default[*].id)
#   # target_network_cidr    = element("0.0.0.0/0", count.index)
#   target_network_cidr  = "0.0.0.0/0"
#   authorize_all_groups = true
# }

# ##-----------------------------------------------------------------------------
# ## Provides authorization rules for AWS Client VPN endpoints.
# ##-----------------------------------------------------------------------------
# # resource "aws_ec2_client_vpn_authorization_rule" "vpn_group_auth" {
# #   count                  = var.enabled ? length(var.group_ids) : 0
# #   client_vpn_endpoint_id = join("", aws_ec2_client_vpn_endpoint.default[*].id)
# #   target_network_cidr    = element(var.target_network_cidr, count.index)
# #   access_group_id        = element(var.group_ids, count.index)
# # }

# ##-----------------------------------------------------------------------------
# ## Provides additional routes for AWS Client VPN endpoints.
# ##-----------------------------------------------------------------------------
# # resource "aws_ec2_client_vpn_route" "vpn_route" {
# #   count                  = length(["0.0.0.0/0", "0.0.0.0/0"])
# #   client_vpn_endpoint_id = join("", aws_ec2_client_vpn_endpoint.default[*].id)
# #   destination_cidr_block = element(["0.0.0.0/0", "0.0.0.0/0"], count.index)
# #   target_vpc_subnet_id   = element(var.route_subnet_ids, count.index)
# #   depends_on             = [aws_ec2_client_vpn_network_association.default]
# # }
