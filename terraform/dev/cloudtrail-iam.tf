# # IAM Roles
# # Updated IAM Role for CloudTrail to send logs to CloudWatch
# resource "aws_iam_role" "cloudtrail_role" {
#   name = "cloudtrail-to-cloudwatch-role"

#   assume_role_policy = jsonencode({
#     "Version" : "2012-10-17",
#     "Statement" : [
#       {
#         "Effect" : "Allow",
#         "Principal" : {
#           "Service" : "cloudtrail.amazonaws.com"
#         },
#         "Action" : "sts:AssumeRole"
#       }
#     ]
#   })
# }


# # Policy for CloudTrail Role to access CloudWatch Logs and S3 Bucket
# resource "aws_iam_policy" "cloudtrail_policy" {
#   name = "cloudtrail-policy"

#   policy = jsonencode({
#     "Version" : "2012-10-17",
#     "Statement" : [
#       {
#         "Effect" : "Allow",
#         "Action" : [
#           "logs:CreateLogStream",
#           "logs:PutLogEvents"
#         ],
#         "Resource" : "${aws_cloudwatch_log_group.cloudtrail.arn}:*"
#       },
#       {
#         "Effect" : "Allow",
#         # "Principal" : {
#         #   "Service" : "cloudtrail.amazonaws.com"
#         # },
#         "Action" : [
#           "s3:GetBucketAcl",
#           "s3:PutObject",
#           "s3:GetObject",
#         ],
#         "Resource" : "*"
#         # "Resource" : [
#         #   "${aws_s3_bucket.cloudtrail_s3.arn}",
#         #   "${aws_s3_bucket.cloudtrail_s3.arn}/*",
#         #   "${aws_s3_bucket.cloudtrail_s3.arn}/AWSLogs/*",
#         # ]
#       },
#       {
#         "Sid" : "AllowCloudTrailKMSAccess",
#         "Effect" : "Allow",
#         # "Principal" : {
#         #   "Service" : "cloudtrail.amazonaws.com"
#         # },
#         "Action" : [
#           "kms:Encrypt",
#           "kms:Decrypt",
#           "kms:ReEncrypt*",
#           "kms:GenerateDataKey*",
#           "kms:DescribeKey"
#         ],
#         "Resource" : "*"
#         # "Resource" : "${aws_kms_key.dev-misc-s3.arn}"
#       }
#     ]
#   })
# }

# # Attach the policy to the CloudTrail role
# resource "aws_iam_role_policy_attachment" "cloudtrail_policy_attachment" {
#   role       = aws_iam_role.cloudtrail_role.name
#   policy_arn = aws_iam_policy.cloudtrail_policy.arn
# }
