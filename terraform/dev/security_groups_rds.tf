module "ravenclaw_db_sg" {
  source      = "../modules/security_groups"
  region      = "us-east-1"
  name        = "ravenclaw_db_sg_new"
  description = "Created by RDS management console"
  vpc_id      = module.vpc_main.vpc_id
  tags        = var.rds_sg_tags

  # ───────────────────────────────────────────────────────────
  # Ingress: PostgreSQL (5432) from within the VPC only
  # ───────────────────────────────────────────────────────────
  ingress_ipv4_cidrs                    = [var.vpc_cidr_block]
  ingress_ipv6_cidrs                    = []
  ingress_prefix_list_ids               = []
  ingress_referenced_security_group_ids = []
  ingress_from_port                     = 5432
  ingress_to_port                       = 5432
  ingress_protocol                      = "tcp"
  ingress_self                          = false
  ingress_custom_rules                  = []

  # ───────────────────────────────────────────────────────────
  # Egress: allow all outbound
  # ───────────────────────────────────────────────────────────
  egress_ipv4_cidrs                    = ["0.0.0.0/0"]
  egress_ipv6_cidrs                    = []
  egress_prefix_list_ids               = []
  egress_referenced_security_group_ids = []
  egress_from_port                     = 0
  egress_to_port                       = 0
  egress_protocol                      = "-1"
  egress_custom_rules                  = []
  egress_self                          = false
}
