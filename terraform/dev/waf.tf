################################## Cloudfront ####################################
resource "aws_wafv2_web_acl" "cloudfront" {
  name = "web-acl-cloudfront"
  description = "WAF Web ACL for Cloudfront with OWASP Top 10 and DDoS protection"
  scope = "CLOUDFRONT"

  default_action {
    allow {}
  }

  rule {
    name = "AWSManagedRulesCommonRuleSet"
    priority = 1
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        name = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name = "AWSManagedRulesCommonRuleSetMetric"
      sampled_requests_enabled = true
    }
  }
  
  # Rule 2: SQL Injection Protection
  rule {
    name = "AWSManagedRulesSQLiRuleSet"
    priority = 2
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        name = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name = "AWSManagedRulesSQLiRuleSetMetric"
      sampled_requests_enabled = true
    }
  }

  # Rule 3: Rate Limiting for DDoS Protection
  rule {
    name = "RateLimitRule"
    priority = 3 
    action {
      block {}
    }
    statement {
      rate_based_statement {
        limit = 10000
        aggregate_key_type = "IP"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name = "RateLimitRuleMetric"
      sampled_requests_enabled = true
    }
  }

  rule {
    name = "BlockMaliciousIPs"
    priority = 4
    action {
      block {}
    }
    statement {
      ip_set_reference_statement {
        arn = aws_wafv2_ip_set.blocked_ips_cloudfront.arn
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name = "BlockMaliciousIPsMetric"
      sampled_requests_enabled = true
    }
  }

    rule {
      name = "AWSManagedRulesBotControlRuleSet"
      priority = 5
      override_action {
        none {}
      }
      statement {
        managed_rule_group_statement {
          name = "AWSManagedRulesBotControlRuleSet"
          vendor_name = "AWS"
        }
      }
      visibility_config {
        cloudwatch_metrics_enabled = true
        metric_name = "AWSManagedRulesBotControlRuleSetMetric"
        sampled_requests_enabled = true
      }
    }
  

  rule {
    name = "AWSManagedRulesAmazonIpReputationList"
    priority = 6
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        name = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      sampled_requests_enabled = true
      cloudwatch_metrics_enabled = true
      metric_name = "AWSManagedRulesAmazonIpReputationListMetric"
    }
  }

  visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name = "WebACLMetric"
      sampled_requests_enabled = true
  }

  tags = {
    Name = "waf-cloudfront-acl"
    Team = "infra"
    Product = "compliance"
    Environment = "prod"
  }
}

resource "aws_wafv2_ip_set" "blocked_ips_cloudfront" {
  name        = "blocked-ips-set"
  description = "Set of IPs to block"
  scope       = "CLOUDFRONT" # ALB is regional

  ip_address_version = "IPV4"

  addresses = [
    "*************/32"
  ]

  tags = {
    Name = "waf-cloudfront-ip-set"
    Team = "infra"
    Product = "compliance"
    Environment = "prod"
  }
}
