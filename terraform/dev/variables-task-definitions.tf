variable "NATS_SERVER_PORT" {
  type    = string
  default = 4222
}

variable "PG_DB" {
  type = string
}

variable "PG_PORT" {
  type = string
}

variable "SUBJECT" {
  type = string
}

variable "SUBJECT_DETECT" {
  type = string
}

variable "SUBJECT_ENRICHED" {
  type = string
}

# bff
variable "BFF_HOST" {
  type    = string
  default = "0.0.0.0"
}

variable "BFF_PORT" {
  type    = string
  default = "8080"
}

variable "PG_SSL" {
  type    = string
  default = "disable"
}

variable "bff_image_uri" {
  type = string
}

variable "bff_logs_group" {
  type    = string
  default = "/ecs/bff-dev"
}

variable "bff_docker_image_name" {
  type    = string
  default = "bff-dev"
}

variable "bff_cpu" {
  type = number
}

variable "bff_memory" {
  type = number
}


# Gateway
variable "GATEWAY_MICROSOFT_WEBHOOK_HOST" {
  type = string
}

variable "GATEWAY_MICROSOFT_WEBHOOK_PATH" {
  type = string
}

variable "GATEWAY_MICROSOFT_WEBHOOK_PORT" {
  type = string
}

variable "GATEWAY_PUBLIC_HOST" {
  type = string
}

variable "gateway_image_uri" {
  type = string
}

variable "gateway_logs_group" {
  type    = string
  default = "/ecs/gateway-dev"
}

variable "gateway_docker_image_name" {
  type    = string
  default = "gateway-dev"
}

variable "gateway_cpu" {
  type = number
}

variable "gateway_memory" {
  type = number
}

# Remediator
variable "remediator_cpu" {
  type = number
}

variable "remediator_LOG_ROTATION_FILE" {
  type    = string
  default = "ravenclaw.log"
}

variable "remediator_LOG_ROTATION_MAX_AGE" {
  type    = string
  default = "90"
}

variable "remediator_image_uri" {
  type = string
}

variable "remediator_logs_group" {
  type    = string
  default = "/ecs/remediator-dev"
}

variable "remediator_docker_image_name" {
  type    = string
  default = "remediator-dev"
}

variable "remediator_memory" {
  type = number
}

# setup
variable "setup_image_uri" {
  type = string
}

variable "setup_logs_group" {
  type    = string
  default = "/ecs/setup-dev"
}

variable "setup_docker_image_name" {
  type    = string
  default = "setup-dev"
}

variable "setup_cpu" {
  type = number
}

variable "setup_memory" {
  type = number
}

variable "ASYNC_MAX_AT_ONCE_VT" {
  type    = string
  default = "10"
}

variable "ASYNC_MAX_PER_SECOND_VT" {
  type    = string
  default = "10"
}

variable "INTENT_EXTRACTION_MODEL_DEPLOYMENT" {
  type    = string
  default = "tone-and-intent-model"
}

variable "INTENT_EXTRACTION_MODEL_ENDPOINT" {
  type    = string
  default = "https://ti-service-india.openai.azure.com"
}

variable "INTENT_EXTRACTION_MODEL_API_VERSION" {
  type    = string
  default = "2024-05-01-preview"
}

variable "MODEL_PATH" {
  type    = string
  default = "/app/data/spacy_model"
}

variable "ml_inference_image_uri" {
  type = string
}

variable "ml_inference_logs_group" {
  type    = string
  default = "/ecs/ml-inference-dev"
}

variable "ml_inference_docker_image_name" {
  type    = string
  default = "ml-inference-dev"
}

variable "ml_inference_cpu" {
  type = number
}

variable "ml_inference_memory" {
  type = number
}

variable "EXTRACTION_MODEL_PATH" {
  type    = string
  default = "/app/models/gliner_large_v2_5"
}

variable "SPACY_ENTITY_MODEL_PATH" {
  type    = string
  default = "/app/models/spacy_model"
}

variable "ingestion_image_uri" {
  type = string
}

variable "ingestion_logs_group" {
  type    = string
  default = "/ecs/ingestion"
}

variable "ingestion_docker_image_name" {
  type = string
}

variable "ingestion_cpu" {
  type = number
}

variable "ingestion_memory" {
  type = number
}

variable "ti_go_service_image_uri" {
  type = string
}

variable "ti_go_service_logs_group" {
  type    = string
  default = "/ecs/ti-go-service"
}

variable "ti_go_service_docker_image_name" {
  type = string
}

variable "ti_go_service_cpu" {
  type = number
}

variable "ti_go_service_memory" {
  type = number
}

variable "gateway_subcommand_docker_image_name" {
  type    = string
  default = "gateway-subcommand"
}

variable "gateway_subcommand_logs_group" {
  type    = string
  default = "/ecs/gateway-subcommand"
}

variable "gateway_subcommand_cpu" {
  type = number
}

variable "gateway_subcommand_memory" {
  type = number
}

variable "gateway_subcommand_image_uri" {
  type = string
}

variable "ATTACHMENT_MODEL_API_KEY" {
  type = string
}

variable "inline_image_uri" {
  type = string
}

variable "inline_log_group" {
  type    = string
  default = "/ecs/gateway-inline-dev"
}
