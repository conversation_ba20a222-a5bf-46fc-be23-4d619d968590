# ALB
variable "alb_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "target_group_gateway_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-target-group-gateway"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "target_group_bff_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-target-group-bff"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_listener_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-listener"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_listener_rule_bff_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-listener-rule-bff"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_listener_rule_gateway_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-lb-listener-rule-gateway"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# ECS Cluster
variable "ecs_cluster_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ecs-cluster"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_namespace_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ecs-service-discovery-dns-ns"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# ECS Service
variable "bff_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-svc"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "bff_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-sd"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-svc"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "gateway_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-sd"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "remediator_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-svc"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "remediator_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-sd"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ml_inference_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ml-inference-svc"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "python"
  }
}

variable "ml_inference_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ml-inference-sd"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Route table
variable "rt_nat_private_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rt-private-1"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "rt_ig_public_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rt-public"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Secrets Manager
variable "nat_ti_username_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-ti-username"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nat_ti_password_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-ti-password"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_microsoft_suscription_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-microsoft-subscription-secret"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pg_password_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-pg-password"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "groq_api_key_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-groq-api-key"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "intent_extraction_model_api_key_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-intent-extraction-model-api-key"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "url_scan_api_key_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-url-scan-api-key"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "virustotal_api_key_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-virustotal-api-key"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Security groups

variable "bastion_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bastion-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-alb-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ecs-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "bff_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "setup_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-setup-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nats_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "rds_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "remediator_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ml_inference_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ml-inference-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

#VPC
variable "vpc_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-vpc"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Subnets
variable "pvt_subnet_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-1-pvt-subnet"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pvt_subnet_2_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-2-pvt-subnet"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pvt_subnet_3_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-3-pvt-subnet"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pub_subnet_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-1-pub-subnet"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pub_subnet_2_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-2-pub-subnet"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "pub_subnet_3_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-az-3-pub-subnet"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# RDS
variable "ravenclaw_rds_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-instance"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ravenclaw_rds_subnet_group_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-subnet-group"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ravenclaw_rds_kms_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-kms"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "postgresql"
  }
}

# NAT Gateway
variable "nat_gateway_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-gateway-1"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Internet Gateway
variable "internet_gateway_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-internet-gateway"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# IAM roles
variable "ecs_task_role_iam_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-task-role-iam"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ecs_task_execution_role_iam_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-task-execution-role-iam"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Elastic IPs
variable "nat_gateway_1_eip_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-1-gateway-eip"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nat_gateway_2_eip_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-2-gateway-eip"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "nat_gateway_3_eip_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nat-3-gateway-eip"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "bastion_eip_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bastion-eip"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# EC2
variable "nats_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-nats-instance"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "bastion_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bastion-instance"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "terraform-dev"
  }
}

variable "rushikesh_yadwade_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rushikesh-yadwade-dev-instance"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "terraform-dev"
  }
}

## NATS clustering
variable "nats_cluster_1_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-dev-nats-1"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "nats_cluster_2_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-dev-nats-2"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "nats_cluster_3_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-dev-nats-3"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "nats_cluster_4_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-dev-nats-4"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "nats"
  }
}

variable "bff_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-bff-task-definition"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-gateway-task-definition"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "remediator_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-remediator-task-definition"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ml_inference_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ml-inference-task-definition"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "setup_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-setup-task-definition"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

# Keycloak infra
variable "keycloak_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-td"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-sd"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
    Application = "keycloak"
  }
}

variable "keycloak_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-ecs-service"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_admin_username_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-admin-username"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_admin_password_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-admin-password"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "alb_listener_rule_keycloak_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-alb-listener-rule"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_alb_listener_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-alb-listener"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "target_group_keycloak_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-alb-target-group"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_alb_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-keycloak-alb"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "webui_build_s3_bucket_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-webui-build-s3-bucket"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "webui_build_cloudfront_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-webui-build-cloudfront"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "rds_replica_instance_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-rds-read-replica"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ingestion_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ingestion-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ingestion_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ingestion-task-definition"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "ti_go_sg_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ti-go-service-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ti_go_service_task_def_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ti-go-service"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "engineering"
  }
}

variable "ti_go_service_discovery_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ti-go-service-sd"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ti_go_service_tags" {
  type = map(string)
  default = {
    Name        = "ravenclaw-ti-go-service"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "engineering"
    Application = "go"
  }
}

variable "keycloak_admin_client_id_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-admin-client-id-secret"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_admin_client_secret_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-admin-client-secret"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "keycloak_service_account_id_tags" {
  type = map(string)
  default = {
    Name        = "keycloak-service-account-id-secret"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ms_auth_client_id_secret_tags" {
  type = map(string)
  default = {
    Name        = "ms-auth-client-id-secret"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ms_auth_client_secret_tags" {
  type = map(string)
  default = {
    Name        = "ms-auth-client-secret"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "ms_auth_tenant_id_tags" {
  type = map(string)
  default = {
    Name        = "ms-auth-tenant-id"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "detection_model_api_key_tags" {
  type = map(string)
  default = {
    Name        = "detection-model-api-key"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "detection_model_endpoint_tags" {
  type = map(string)
  default = {
    Name        = "detection-model-endpoint"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "detection_model_deployment_tags" {
  type = map(string)
  default = {
    Name        = "detection-model-deployment"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "detection_model_api_version_secret_tags" {
  type = map(string)
  default = {
    Name        = "detection-model-api-version"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "detection_model_deployment_with_token_deployment_secret_tags" {
  type = map(string)
  default = {
    Name        = "detection-model-deployment-with-token-deployment"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "llama_instance_tags" {
  type = map(string)
  default = {
    Name        = "llama-ec2-instance"
    Environment = "dev"
    Product     = "ml-inference"
    Team        = "infra"
    Application = "llama-3.1-8b-model"
  }
}

variable "llama_sg_tags" {
  type = map(string)
  default = {
    Name        = "llama-ec2-instance-sg"
    Environment = "dev"
    Product     = "ml-inference"
    Team        = "infra"
  }
}

variable "gw_auth_client_id_tags" {
  type = map(string)
  default = {
    Name        = "gw-auth-client-id"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gw_auth_client_secret_tags" {
  type = map(string)
  default = {
    Name        = "gw-auth-client-secret"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "gateway_subcommand_task_def_tags" {
  type = map(string)
  default = {
    Name        = "gateway-subscribe-job"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}

variable "attachment_model_api_key_tags" {
  type = map(string)
  default = {
    Name        = "attachment-model-api-key-secret"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
}
