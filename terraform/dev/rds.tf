module "rds" {
  source                 = "../modules/rds"
  region                 = var.region
  subnet_ids             = ["${module.main_private_subnet_1.subnet_id}", "${module.main_private_subnet_2.subnet_id}", "${module.main_private_subnet_3.subnet_id}"]
  vpc_name               = var.vpc_name
  db_name                = var.db_name
  allocated_storage      = var.rds_allocated_storage
  availability_zone      = var.rds_availability_zone
  vpc_security_group_ids = ["${aws_security_group.ravenclaw_db_sg.id}"]
  password               = aws_secretsmanager_secret_version.PG_PASSWORD.secret_string
  identifier             = var.identifier
  multi_az               = var.rds_multi_az
  instance_class         = var.rds_instance_class
  copy_tags_to_snapshot  = true
  max_allocated_storage  = var.rds_allocated_storage
  db_subnet_group_name   = var.vpc_name
  storage_type           = "gp3"
  storage_throughput     = 125
  iops                   = 3000

  instance_tags = var.ravenclaw_rds_instance_tags

  subnet_group_tags = var.ravenclaw_rds_subnet_group_tags

  kms_tags = var.ravenclaw_rds_kms_tags

  # Read replica
  create_read_replica   = var.rds_create_read_replica
  read_replica_azs      = var.rds_read_replica_azs
  replica_instance_tags = var.rds_replica_instance_tags
}
