################# Cloudfront Webui Log Alerts ####################
# resource "aws_cloudwatch_log_group" "webui_std_logs" {
#   name              = "/aws/cloudfront/webui"
#   skip_destroy      = true
#   log_group_class   = "STANDARD"
#   retention_in_days = 90

#   tags = {
#     Name        = "/aws/cloudfront/webui"
#     Product     = "AWS"
#     Team        = "infra"
#     Environment = "prod"
#     Application = "webui"
#   }
# }

# resource "aws_sns_topic" "logs" {
#   name = "LOGS_USEAST1_PROD_SNS_TOPIC"
#   tags = {
#     Name        = "LOGS_USEAST1_PROD_SNS_TOPIC"
#     Environment = "prod"
#     Team        = "infra"
#     Product     = "ravenclaw"
#   }
# }

# resource "aws_iam_role" "lambda_exec" {
#   name = "lambda_exec_role_useast1_prod"

#   assume_role_policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Action = "sts:AssumeRole"
#         Effect = "Allow"
#         Sid    = ""
#         Principal = {
#           Service = "lambda.amazonaws.com"
#         }
#       },
#     ]
#   })
# }

# resource "aws_iam_policy" "lambda_logs_policy" {
#   name        = "lambda_logs_policy_useast1_prod"
#   description = "Policy to allow Lambda to read logs"

#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Effect = "Allow"
#         Action = [
#           "logs:DescribeLogGroups",
#           "logs:DescribeLogStreams",
#           "logs:GetLogEvents",
#           "logs:FilterLogEvents"
#         ]
#         Resource = "*"
#       }
#     ]
#   })
# }

# resource "aws_iam_role_policy_attachment" "lambda_logs_attachment" {
#   role       = aws_iam_role.lambda_exec.name
#   policy_arn = aws_iam_policy.lambda_logs_policy.arn
# }

# resource "aws_lambda_function" "main" {
#   filename         = "lambda_function_handler.zip"
#   function_name    = "sendAlert"
#   handler          = "lambda_function.lambda_handler"
#   runtime          = "python3.10"
#   role             = aws_iam_role.lambda_exec.arn
#   source_code_hash = filebase64sha256("lambda_function_handler.zip")
#   environment {
#     variables = {
#       GOOGLE_CHAT_WEBHOOK_URL = "https://chat.googleapis.com/v1/spaces/AAAAgjtskvw/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=vyxKTpka3KVmb1ebsosf6-oSqLCTaHJLfAzuOFa_fFY"
#     }
#   }

#   tags = {
#     Name        = "sendAlert"
#     Environment = "prod"
#     Team        = "infra"
#     Product     = "ravenclaw"
#   }
# }

# resource "aws_iam_role_policy_attachment" "lambda_policy" {
#   role       = aws_iam_role.lambda_exec.name
#   policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
# }

# resource "aws_lambda_permission" "allow_cloudwatch" {
#   statement_id  = "AllowExecutionFromSNS"
#   action        = "lambda:InvokeFunction"
#   function_name = aws_lambda_function.main.function_name
#   principal     = "sns.amazonaws.com"
#   source_arn    = aws_sns_topic.logs.arn
# }

# resource "aws_sns_topic_subscription" "main" {
#   topic_arn = aws_sns_topic.logs.arn
#   protocol  = "lambda"
#   endpoint  = aws_lambda_function.main.arn
# }

# # module "cloudfront_std_log_4xx_alert" {
#   source                           = "../modules/log_alerting"
#   region                           = var.region
#   log_group_name                   = "/aws/cloudfront/webui"
#   log_filter_name                  = "cloudfront_std_log_4xx_alarm"
#   log_pattern                      = "[col1, col2, col3, col4, col5, col6, col7, col8, col9, col10, col11=4*, col12]"
#   alarm_name                       = "cloudfront_std_log_4xx_alarm"
#   sns_topic_arn                    = aws_sns_topic.logs.arn
#   metric_transformation_name       = "CLOUDFRONT_STD_LOG_4XX_ALARM"
#   alarm_metric_threshold           = 10
#   alarm_metric_statistic           = "Sum"
#   alarm_metric_comparison_operator = "GreaterThanOrEqualToThreshold"
#   alarm_metric_evaluation_periods  = "1"
#   alarm_metric_period              = "600"
# }

# module "cloudfront_std_log_5xx_alert" {
#   source                           = "../modules/log_alerting"
#   region                           = var.region
#   log_group_name                   = "/aws/cloudfront/webui"
#   log_filter_name                  = "cloudfront_std_log_5xx_alarm"
#   alarm_name                       = "cloudfront_std_log_5xx_alarm"
#   log_pattern                      = "[col1, col2, col3, col4, col5, col6, col7, col8, col9, col10, col11=5*, col12]"
#   sns_topic_arn                    = aws_sns_topic.logs.arn
#   metric_transformation_name       = "CLOUDFRONT_STD_LOG_5XX_ALARM"
#   alarm_metric_threshold           = 10
#   alarm_metric_statistic           = "Sum"
#   alarm_metric_comparison_operator = "GreaterThanOrEqualToThreshold"
#   alarm_metric_evaluation_periods  = "1"
#   alarm_metric_period              = "600"
# }
