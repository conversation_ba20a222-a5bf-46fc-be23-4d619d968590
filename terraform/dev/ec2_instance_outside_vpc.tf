# IAM Role
resource "aws_iam_role" "S3EC2AccessDevRole" {
  name = "S3EC2AccessDevRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "s3_read_only_iam_policy" {
  name        = "S3DevEC2AccessPolicy"
  path        = "/"
  description = "IAM policy for dev EC2 instance to access S3"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "S3:List*",
          "S3:Get*",
          "S3:Describe*",
          "kms:Decrypt",
          "kms:Encrypt",
          "kms:GenerateDataKey",
          "kms:ReEncrypt*",
          "kms:DescribeKey",
        ]
        Resource = "*",
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "S3DevReadOnly_attach" {
  role       = aws_iam_role.S3EC2AccessDevRole.name
  policy_arn = aws_iam_policy.s3_read_only_iam_policy.arn
}

resource "aws_iam_instance_profile" "S3DevReadOnlyProfile" {
  name = "S3DevReadOnlyProfile"
  role = aws_iam_role.S3EC2AccessDevRole.name
}

resource "aws_instance" "docker-build-validation-vm" {
  ami                         = "ami-04b70fa74e45c3917"
  associate_public_ip_address = "false"
  availability_zone           = "us-east-1a"

  capacity_reservation_specification {
    capacity_reservation_preference = "open"
  }

  cpu_options {
    core_count       = "1"
    threads_per_core = "1"
  }


  credit_specification {
    cpu_credits = "standard"
  }

  disable_api_stop        = "false"
  disable_api_termination = "false"
  ebs_optimized           = "false"

  enclave_options {
    enabled = "false"
  }

  get_password_data                    = "false"
  hibernation                          = "false"
  instance_initiated_shutdown_behavior = "stop"
  instance_type                        = "t2.micro"
  ipv6_address_count                   = "0"
  key_name                             = "mail-sec-bastion-01"

  maintenance_options {
    auto_recovery = "default"
  }

  metadata_options {
    http_endpoint               = "enabled"
    http_protocol_ipv6          = "disabled"
    http_put_response_hop_limit = "2"
    http_tokens                 = "required"
    instance_metadata_tags      = "disabled"
  }

  monitoring                 = "false"
  placement_partition_number = "0"

  private_dns_name_options {
    enable_resource_name_dns_a_record    = "false"
    enable_resource_name_dns_aaaa_record = "false"
    hostname_type                        = "ip-name"
  }

  private_ip = "**********"

  root_block_device {
    delete_on_termination = "true"
    encrypted             = "false"
    iops                  = "3000"
    throughput            = "125"
    volume_size           = "8"
    volume_type           = "gp3"
    tags = {
      Name        = "docker-build-validation-vm-volume"
      Environment = "dev"
      Team        = "infra"
      Product     = "misc"
    }
  }

  source_dest_check = "true"
  subnet_id         = "subnet-0451ffa7b5ed945eb"

  tags = {
    Name        = "docker-build-validation-vm"
    Environment = "dev"
    Team        = "infra"
    Product     = "misc"
  }

  tenancy                = "default"
  vpc_security_group_ids = ["sg-0fde4f72da313ea3d"]
}

resource "aws_instance" "mail-sec-gmail-analyzer-01" {
  ami                         = "ami-07d9b9ddc6cd8dd30"
  associate_public_ip_address = "false"
  availability_zone           = "us-east-1a"

  capacity_reservation_specification {
    capacity_reservation_preference = "open"
  }

  cpu_options {
    core_count       = "1"
    threads_per_core = "1"
  }


  credit_specification {
    cpu_credits = "standard"
  }

  disable_api_stop        = "false"
  disable_api_termination = "false"
  ebs_optimized           = "false"

  enclave_options {
    enabled = "false"
  }

  get_password_data                    = "false"
  hibernation                          = "false"
  instance_initiated_shutdown_behavior = "stop"
  instance_type                        = "t2.micro"
  ipv6_address_count                   = "0"
  key_name                             = "mail-sec-gmail-analyzer-01"

  maintenance_options {
    auto_recovery = "default"
  }

  metadata_options {
    http_endpoint               = "enabled"
    http_protocol_ipv6          = "disabled"
    http_put_response_hop_limit = "2"
    http_tokens                 = "required"
    instance_metadata_tags      = "disabled"
  }

  monitoring                 = "false"
  placement_partition_number = "0"

  private_dns_name_options {
    enable_resource_name_dns_a_record    = "false"
    enable_resource_name_dns_aaaa_record = "false"
    hostname_type                        = "ip-name"
  }

  private_ip = "************"

  root_block_device {
    delete_on_termination = "true"
    encrypted             = "false"
    volume_size           = "16"
    volume_type           = "gp2"
  }

  source_dest_check = "true"
  subnet_id         = "subnet-065553f632d0a194b"

  tags = {
    Name        = "mail-sec-gmail-analyzer-01"
    Environment = "dev"
    Team        = "infra"
    Product     = "mailsec"
  }

  tenancy                = "default"
  vpc_security_group_ids = ["sg-05860af8d4d6f009c"]
}

resource "aws_instance" "mail-sec-bastion-01" {
  ami                         = "ami-07d9b9ddc6cd8dd30"
  associate_public_ip_address = "true"
  availability_zone           = "us-east-1a"

  capacity_reservation_specification {
    capacity_reservation_preference = "open"
  }

  cpu_options {
    core_count       = "1"
    threads_per_core = "1"
  }


  credit_specification {
    cpu_credits = "standard"
  }

  disable_api_stop        = "false"
  disable_api_termination = "false"
  ebs_optimized           = "false"

  enclave_options {
    enabled = "false"
  }

  get_password_data                    = "false"
  hibernation                          = "false"
  instance_initiated_shutdown_behavior = "stop"
  instance_type                        = "t2.micro"
  ipv6_address_count                   = "0"
  key_name                             = "mail-sec-bastion-01"

  maintenance_options {
    auto_recovery = "default"
  }

  metadata_options {
    http_endpoint               = "enabled"
    http_protocol_ipv6          = "disabled"
    http_put_response_hop_limit = "2"
    http_tokens                 = "required"
    instance_metadata_tags      = "disabled"
  }

  monitoring                 = "false"
  placement_partition_number = "0"

  private_dns_name_options {
    enable_resource_name_dns_a_record    = "false"
    enable_resource_name_dns_aaaa_record = "false"
    hostname_type                        = "ip-name"
  }

  private_ip = "*********"

  root_block_device {
    delete_on_termination = "true"
    encrypted             = "false"
    volume_size           = "8"
    volume_type           = "gp2"
  }

  source_dest_check = "true"
  subnet_id         = "subnet-0451ffa7b5ed945eb"

  tags = {
    Name        = "mail-sec-bastion-01"
    Environment = "dev"
    Product     = "mailsec"
    Team        = "infra"
  }

  tenancy                = "default"
  vpc_security_group_ids = ["sg-0fde4f72da313ea3d"]
}

# resource "aws_instance" "datascience-workspace-01" {
#   ami                         = data.aws_ami.graviton_instance_ami.id
#   associate_public_ip_address = "false"
#   availability_zone           = "us-east-1a"

#   capacity_reservation_specification {
#     capacity_reservation_preference = "open"
#   }

#   # cpu_options {
#   #   core_count       = "2"
#   #   threads_per_core = "2"
#   # }


#   credit_specification {
#     cpu_credits = "unlimited"
#   }

#   disable_api_stop        = "false"
#   disable_api_termination = "false"
#   ebs_optimized           = "true"

#   enclave_options {
#     enabled = "false"
#   }

#   get_password_data                    = "false"
#   hibernation                          = "false"
#   instance_initiated_shutdown_behavior = "stop"
#   instance_type                        = "t4g.xlarge"
#   ipv6_address_count                   = "0"
#   key_name                             = "datascience-workspace-01"

#   maintenance_options {
#     auto_recovery = "default"
#   }

#   metadata_options {
#     http_endpoint               = "enabled"
#     http_protocol_ipv6          = "disabled"
#     http_put_response_hop_limit = "2"
#     http_tokens                 = "required"
#     instance_metadata_tags      = "disabled"
#   }

#   monitoring                 = "false"
#   placement_partition_number = "0"

#   private_dns_name_options {
#     enable_resource_name_dns_a_record    = "false"
#     enable_resource_name_dns_aaaa_record = "false"
#     hostname_type                        = "ip-name"
#   }

#   private_ip = "***********"

#   root_block_device {
#     delete_on_termination = "false"
#     encrypted             = "false"
#     iops                  = "3000"
#     throughput            = "125"
#     volume_size           = "50"
#     volume_type           = "gp3"
#     tags                  = {}
#   }

#   source_dest_check = "true"
#   subnet_id         = "subnet-065553f632d0a194b"

#   tags = {
#     Name        = "datascience-workspace-01"
#     Environment = "dev"
#     Team        = "infra"
#     Product     = "datascience"
#   }

#   iam_instance_profile = aws_iam_instance_profile.S3DevReadOnlyProfile.name

#   tenancy                = "default"
#   vpc_security_group_ids = ["sg-05a3ccba0cfa1b139"]
# }

resource "aws_instance" "ravenclaw-smart-host-server-01" {
  ami                         = "ami-04b70fa74e45c3917"
  associate_public_ip_address = "false"
  availability_zone           = "us-east-1a"

  capacity_reservation_specification {
    capacity_reservation_preference = "open"
  }

  cpu_options {
    core_count       = "1"
    threads_per_core = "1"
  }

  credit_specification {
    cpu_credits = "standard"
  }

  disable_api_stop        = "false"
  disable_api_termination = "false"
  ebs_optimized           = "false"

  enclave_options {
    enabled = "false"
  }

  get_password_data                    = "false"
  hibernation                          = "false"
  instance_initiated_shutdown_behavior = "stop"
  instance_type                        = "t2.micro"
  ipv6_address_count                   = "0"
  key_name                             = "ravenclaw-smart-host-server-01"

  maintenance_options {
    auto_recovery = "default"
  }

  metadata_options {
    http_endpoint               = "enabled"
    http_protocol_ipv6          = "disabled"
    http_put_response_hop_limit = "2"
    http_tokens                 = "required"
    instance_metadata_tags      = "disabled"
  }

  monitoring                 = "false"
  placement_partition_number = "0"

  private_dns_name_options {
    enable_resource_name_dns_a_record    = "false"
    enable_resource_name_dns_aaaa_record = "false"
    hostname_type                        = "ip-name"
  }

  private_ip = "************"

  root_block_device {
    delete_on_termination = "true"
    encrypted             = "false"
    iops                  = "3000"
    throughput            = "125"
    volume_size           = "30"
    volume_type           = "gp3"
  }

  source_dest_check = "true"
  subnet_id         = "subnet-065553f632d0a194b"

  tags = {
    Name        = "ravenclaw-smart-host-server-01"
    Environment = "dev"
    Team        = "infra"
    Product     = "ravenclaw"
  }

  tags_all = {
    Name = "ravenclaw-smart-host-server-01"
  }

  tenancy                = "default"
  vpc_security_group_ids = ["sg-05860af8d4d6f009c", "sg-09fddb9775d227880"]
}
