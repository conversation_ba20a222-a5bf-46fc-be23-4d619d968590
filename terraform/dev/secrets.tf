resource "aws_secretsmanager_secret_version" "NATS_TI_USERNAME" {
  secret_id     = aws_secretsmanager_secret.NATS_TI_USERNAME.arn
  secret_string = var.NATS_TI_USERNAME
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "NATS_TI_PASSWORD" {
  secret_id     = aws_secretsmanager_secret.NATS_TI_PASSWORD.arn
  secret_string = var.NATS_TI_PASSWORD
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET" {
  secret_id     = aws_secretsmanager_secret.GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET.arn
  secret_string = var.GATEWAY_MICROSOFT_SUBSCRIPTION_SECRET
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "PG_PASSWORD" {
  secret_id     = aws_secretsmanager_secret.PG_PASSWORD.arn
  secret_string = var.PG_PASSWORD
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "GROQ_API_KEY" {
  secret_id     = aws_secretsmanager_secret.GROQ_API_KEY.arn
  secret_string = var.GROQ_API_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "INTENT_EXTRACTION_MODEL_API_KEY" {
  secret_id     = aws_secretsmanager_secret.INTENT_EXTRACTION_MODEL_API_KEY.arn
  secret_string = var.INTENT_EXTRACTION_MODEL_API_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "URL_SCAN_API_KEY" {
  secret_id     = aws_secretsmanager_secret.URL_SCAN_API_KEY.arn
  secret_string = var.URL_SCAN_API_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "VIRUSTOTAL_API_KEY" {
  secret_id     = aws_secretsmanager_secret.VIRUSTOTAL_API_KEY.arn
  secret_string = var.VIRUSTOTAL_API_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "KEYCLOAK_ADMIN_USERNAME" {
  secret_id     = aws_secretsmanager_secret.KEYCLOAK_ADMIN_USERNAME.arn
  secret_string = var.KEYCLOAK_ADMIN_USERNAME
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "KEYCLOAK_ADMIN_PASSWORD" {
  secret_id     = aws_secretsmanager_secret.KEYCLOAK_ADMIN_PASSWORD.arn
  secret_string = var.KEYCLOAK_ADMIN_PASSWORD
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "KEYCLOAK_ADMIN_CLIENT_SECRET" {
  secret_id     = aws_secretsmanager_secret.KEYCLOAK_ADMIN_CLIENT_SECRET.arn
  secret_string = var.KEYCLOAK_ADMIN_CLIENT_SECRET
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "KEYCLOAK_ADMIN_CLIENT_ID" {
  secret_id     = aws_secretsmanager_secret.KEYCLOAK_ADMIN_CLIENT_ID.arn
  secret_string = var.KEYCLOAK_ADMIN_CLIENT_ID
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "KEYCLOAK_SERVICE_ACCOUNT_ID" {
  secret_id     = aws_secretsmanager_secret.KEYCLOAK_SERVICE_ACCOUNT_ID.arn
  secret_string = var.KEYCLOAK_SERVICE_ACCOUNT_ID
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "MS_AUTH_CLIENT_ID" {
  secret_id     = aws_secretsmanager_secret.MS_AUTH_CLIENT_ID.arn
  secret_string = var.MS_AUTH_CLIENT_ID
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "MS_AUTH_CLIENT_SECRET" {
  secret_id     = aws_secretsmanager_secret.MS_AUTH_CLIENT_SECRET.arn
  secret_string = var.MS_AUTH_CLIENT_SECRET
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "MS_AUTH_TENANT_ID" {
  secret_id     = aws_secretsmanager_secret.MS_AUTH_TENANT_ID.arn
  secret_string = var.MS_AUTH_TENANT_ID
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "DETECTION_MODEL_API_KEY" {
  secret_id     = aws_secretsmanager_secret.DETECTION_MODEL_API_KEY.arn
  secret_string = var.DETECTION_MODEL_API_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "DETECTION_MODEL_ENDPOINT" {
  secret_id     = aws_secretsmanager_secret.DETECTION_MODEL_ENDPOINT.arn
  secret_string = var.DETECTION_MODEL_ENDPOINT
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "DETECTION_MODEL_DEPLOYMENT" {
  secret_id     = aws_secretsmanager_secret.DETECTION_MODEL_DEPLOYMENT.arn
  secret_string = var.DETECTION_MODEL_DEPLOYMENT
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "DETECTION_MODEL_API_VERSION" {
  secret_id     = aws_secretsmanager_secret.DETECTION_MODEL_API_VERSION.arn
  secret_string = var.DETECTION_MODEL_API_VERSION
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "DETECTION_MODEL_DEPLOYMENT_WITH_TOKEN_DEPLOYMENT" {
  secret_id     = aws_secretsmanager_secret.DETECTION_MODEL_DEPLOYMENT_WITH_TOKEN_DEPLOYMENT.arn
  secret_string = var.DETECTION_MODEL_DEPLOYMENT_WITH_TOKEN_DEPLOYMENT
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "GW_AUTH_CLIENT_ID" {
  secret_id     = aws_secretsmanager_secret.GW_AUTH_CLIENT_ID.arn
  secret_string = var.GW_AUTH_CLIENT_ID
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "GW_AUTH_CLIENT_SECRET" {
  secret_id     = aws_secretsmanager_secret.GW_AUTH_CLIENT_SECRET.arn
  secret_string = var.GW_AUTH_CLIENT_SECRET
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "GW_SERVICE_ACCOUNT" {
  secret_id     = aws_secretsmanager_secret.GW_SERVICE_ACCOUNT.arn
  secret_string = var.GW_SERVICE_ACCOUNT
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC" {
  secret_id     = aws_secretsmanager_secret.GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC.arn
  secret_string = var.GATEWAY_GOOGLE_SUBSCRIPTION_TOPIC
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "ATTACHMENT_MODEL_API_KEY" {
  secret_id     = aws_secretsmanager_secret.ATTACHMENT_MODEL_API_KEY.arn
  secret_string = var.ATTACHMENT_MODEL_API_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "NATS_SERVER_URL" {
  secret_id     = aws_secretsmanager_secret.NATS_SERVER_URL.arn
  secret_string = "nats://${aws_secretsmanager_secret_version.NATS_TI_USERNAME.secret_string}:${aws_secretsmanager_secret_version.NATS_TI_PASSWORD.secret_string}@nats-1-dev.ravenclaw-dev-ns:${var.NATS_SERVER_PORT}, nats://${aws_secretsmanager_secret_version.NATS_TI_USERNAME.secret_string}:${aws_secretsmanager_secret_version.NATS_TI_PASSWORD.secret_string}@nats-2-dev.ravenclaw-dev-ns:${var.NATS_SERVER_PORT}, nats://${aws_secretsmanager_secret_version.NATS_TI_USERNAME.secret_string}:${aws_secretsmanager_secret_version.NATS_TI_PASSWORD.secret_string}@nats-3-dev.ravenclaw-dev-ns:${var.NATS_SERVER_PORT}"
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "GW_SERVICE_ACCOUNT_INGESTION" {
  secret_id     = aws_secretsmanager_secret.GW_SERVICE_ACCOUNT_INGESTION.arn
  secret_string = var.GW_SERVICE_ACCOUNT_INGESTION
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "GATEWAY_SUBCOMMAND_GW_SERVICE_ACCOUNT" {
  secret_id     = aws_secretsmanager_secret.GATEWAY_SUBCOMMAND_GW_SERVICE_ACCOUNT.arn
  secret_string = var.GATEWAY_SUBCOMMAND_GW_SERVICE_ACCOUNT
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "SMARTHOST_TLS_CERT" {
  secret_id     = aws_secretsmanager_secret.SMARTHOST_TLS_CERT.arn
  secret_string = var.SMARTHOST_TLS_CERT
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "DKIM_PRIVATE_KEY" {
  secret_id     = aws_secretsmanager_secret.DKIM_PRIVATE_KEY.arn
  secret_string = var.DKIM_PRIVATE_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "SMARTHOST_TLS_KEY" {
  secret_id     = aws_secretsmanager_secret.SMARTHOST_TLS_KEY.arn
  secret_string = var.SMARTHOST_TLS_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "DLP_MODEL_CREDENTIALS" {
  secret_id     = aws_secretsmanager_secret.DLP_MODEL_CREDENTIALS.arn
  secret_string = var.DLP_MODEL_CREDENTIALS
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "SPLUNK_ORGANIZATIONS_KEY" {
  secret_id     = aws_secretsmanager_secret.SPLUNK_ORGANIZATIONS_KEY.arn
  secret_string = var.SPLUNK_ORGANIZATIONS_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "CLASSIFICATION_OAI_API_KEY" {
  secret_id     = aws_secretsmanager_secret.CLASSIFICATION_OAI_API_KEY.arn
  secret_string = var.CLASSIFICATION_OAI_API_KEY
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "JWT_SECRET" {
  secret_id     = aws_secretsmanager_secret.JWT_SECRET.arn
  secret_string = var.JWT_SECRET
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "ATTACHMENT_SCAN_ENABLED_ORGS" {
  secret_id     = aws_secretsmanager_secret.ATTACHMENT_SCAN_ENABLED_ORGS.arn
  secret_string = var.ATTACHMENT_SCAN_ENABLED_ORGS
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "CLICKSTACK_PASSWORD" {
  secret_id     = aws_secretsmanager_secret.CLICKSTACK_PASSWORD.arn
  secret_string = var.CLICKSTACK_PASSWORD
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "CLICKSTACK_USER" {
  secret_id     = aws_secretsmanager_secret.CLICKSTACK_USER.arn
  secret_string = var.CLICKSTACK_USER
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "ANALYTICS_EXPORTER_RDS_USER" {
  secret_id     = aws_secretsmanager_secret.ANALYTICS_EXPORTER_RDS_USER.arn
  secret_string = var.ANALYTICS_EXPORTER_RDS_USER
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "ANALYTICS_EXPORTER_RDS_PASSWORD" {
  secret_id     = aws_secretsmanager_secret.ANALYTICS_EXPORTER_RDS_PASSWORD.arn
  secret_string = var.ANALYTICS_EXPORTER_RDS_PASSWORD
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "ANALYTICS_EXPORTER_CLICKHOUSE_USER" {
  secret_id     = aws_secretsmanager_secret.ANALYTICS_EXPORTER_CLICKHOUSE_USER.arn
  secret_string = var.ANALYTICS_EXPORTER_CLICKHOUSE_USER
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

resource "aws_secretsmanager_secret_version" "ANALYTICS_EXPORTER_CLICKHOUSE_PASSWORD" {
  secret_id     = aws_secretsmanager_secret.ANALYTICS_EXPORTER_CLICKHOUSE_PASSWORD.arn
  secret_string = var.ANALYTICS_EXPORTER_CLICKHOUSE_PASSWORD
  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}
