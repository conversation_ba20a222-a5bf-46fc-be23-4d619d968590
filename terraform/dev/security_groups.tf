resource "aws_security_group" "bastion_sg" {
  description = "SG for dev bastion instance"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    # cidr_blocks = ["${var.vpc_cidr_block}"]
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "true"
    to_port     = "22"
  }

  name   = "bastion_sg"
  tags   = var.bastion_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "ecs_alb_loadbalancer_sg" {
  description = "For ECS"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    # cidr_blocks = ["${var.vpc_cidr_block}"]
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "443"
  }

  ingress {
    # cidr_blocks = ["${var.vpc_cidr_block}"]
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  name   = "ecs_alb_loadbalancer_sg"
  tags   = var.alb_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "ecs_sg" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks      = ["${var.vpc_cidr_block}"]
    from_port        = "8081"
    ipv6_cidr_blocks = ["::/0"]
    protocol         = "tcp"
    self             = "false"
    to_port          = "8081"
  }

  name   = "ecs_sg"
  tags   = var.ecs_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "ecs_ravenclaw_bff_sg" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  # ingress {
  #   cidr_blocks     = ["${var.vpc_cidr_block}"]
  #   from_port       = 80
  #   to_port         = 80
  #   protocol        = "tcp"
  #   security_groups = [aws_security_group.bastion_sg.id]
  # }


  ingress {
    cidr_blocks      = ["${var.vpc_cidr_block}"]
    from_port        = "8080"
    ipv6_cidr_blocks = ["::/0"]
    protocol         = "tcp"
    self             = "false"
    to_port          = "8080"
  }

  ingress {
    cidr_blocks      = ["${var.vpc_cidr_block}"]
    from_port        = "3000"
    ipv6_cidr_blocks = ["::/0"]
    protocol         = "tcp"
    self             = "false"
    to_port          = "3000"
  }

  name = "ecs_ravenclaw_bff_sg"

  tags = var.bff_sg_tags

  tags_all = {
    Name = "ecs_ravenclaw_bff_sg"
  }

  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "ecs_ravenclaw_setup_sg" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks      = ["${var.vpc_cidr_block}"]
    from_port        = "8080"
    ipv6_cidr_blocks = ["::/0"]
    protocol         = "tcp"
    self             = "false"
    to_port          = "8080"
  }

  ingress {
    cidr_blocks      = ["${var.vpc_cidr_block}"]
    from_port        = "8081"
    ipv6_cidr_blocks = ["::/0"]
    protocol         = "tcp"
    self             = "false"
    to_port          = "8081"
  }

  ingress {
    cidr_blocks      = ["${var.vpc_cidr_block}"]
    from_port        = "3000"
    ipv6_cidr_blocks = ["::/0"]
    protocol         = "tcp"
    self             = "false"
    to_port          = "3000"
  }

  name = "ecs_ravenclaw_setup_sg"

  tags = var.setup_sg_tags

  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "gateway_sg" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }
  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "8081"
    protocol  = "tcp"
    self      = "false"
    to_port   = "8081"
  }

  name   = "gateway"
  tags   = var.gateway_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "nats_sg" {
  description = "SG for dev NATS setup"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "4222"
    protocol    = "tcp"
    self        = "false"
    to_port     = "4222"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "6222"
    protocol    = "tcp"
    self        = "false"
    to_port     = "6222"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "8222"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8222"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "7777"
    protocol    = "tcp"
    self        = "false"
    to_port     = "7777"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "7778"
    protocol    = "tcp"
    self        = "false"
    to_port     = "7778"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    description = "Allow HTTP - Node Exporter"
    from_port   = 9100
    to_port     = 9100
    protocol    = "tcp"
    cidr_blocks = ["${var.vpc_cidr_block}"]
  }

  # ingress {
  #   cidr_blocks = ["${var.vpc_cidr_block}"]
  #   # cidr_blocks = ["0.0.0.0/0"]
  #   from_port   = "0"
  #   protocol    = "-1"
  #   self        = "false"
  #   to_port     = "0"
  # }

  name   = "nats_dev_sg"
  tags   = var.nats_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "ravenclaw_db_sg" {
  description = "Created by RDS management console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "5432"
    protocol    = "tcp"
    self        = "false"
    to_port     = "5432"
  }

  name   = "ravenclaw_db_sg"
  tags   = var.rds_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "remediator_sg" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  name   = "remediator_sg"
  tags   = var.remediator_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "ml_inference_sg" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "8000"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8000"
  }

  name   = "ml_inference_sg"
  tags   = var.ml_inference_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "keycloak_sg" {
  description = "SG for keycloak service"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "8443"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8443"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "9000"
    protocol    = "tcp"
    self        = "false"
    to_port     = "9000"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "7800"
    protocol    = "tcp"
    self        = "false"
    to_port     = "7800"
  }

  name   = "keycloak_sg"
  tags   = var.keycloak_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "ecs_ravenclaw_ingestion_sg" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks      = ["${var.vpc_cidr_block}"]
    from_port        = "80"
    ipv6_cidr_blocks = ["::/0"]
    protocol         = "tcp"
    self             = "false"
    to_port          = "80"
  }

  name = "ecs_ravenclaw_ingestion_sg"

  tags = var.ingestion_sg_tags

  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "ravenclaw_ti_go_service" {
  description = "Created in ECS Console"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  name   = "ravenclaw_ti_go_service"
  tags   = var.ti_go_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "llama_sg" {
  description = "SG for dev llama instance"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "22"
    protocol    = "tcp"
    self        = "false"
    to_port     = "22"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "8000"
    protocol    = "tcp"
    self        = "false"
    to_port     = "8000"
  }

  name   = "llama_sg"
  tags   = var.llama_sg_tags
  vpc_id = module.vpc_main.vpc_id
}

# resource "aws_security_group" "vpn_sg" {
#   description = "SG for dev vpn"

#   egress {
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "0"
#     protocol    = "-1"
#     self        = "false"
#     to_port     = "0"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "22"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "22"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "8000"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "8000"
#   }

#   ingress {
#     # cidr_blocks = ["${var.vpc_cidr_block}"]
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "443"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "443"
#   }

#   ingress {
#     # cidr_blocks = ["${var.vpc_cidr_block}"]
#     cidr_blocks = ["0.0.0.0/0"]
#     from_port   = "80"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "80"
#   }

#   ingress {
#     cidr_blocks      = ["${var.vpc_cidr_block}"]
#     from_port        = "8081"
#     ipv6_cidr_blocks = ["::/0"]
#     protocol         = "tcp"
#     self             = "false"
#     to_port          = "8081"
#   }

#   ingress {
#     cidr_blocks      = ["${var.vpc_cidr_block}"]
#     from_port        = "8080"
#     ipv6_cidr_blocks = ["::/0"]
#     protocol         = "tcp"
#     self             = "false"
#     to_port          = "8080"
#   }

#   ingress {
#     cidr_blocks      = ["${var.vpc_cidr_block}"]
#     from_port        = "3000"
#     ipv6_cidr_blocks = ["::/0"]
#     protocol         = "tcp"
#     self             = "false"
#     to_port          = "3000"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "4222"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "4222"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "6222"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "6222"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "8222"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "8222"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "7777"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "7777"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "7778"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "7778"
#   }

#   ingress {
#     description = "Allow HTTP - Node Exporter"
#     from_port   = 9100
#     to_port     = 9100
#     protocol    = "tcp"
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "5432"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "5432"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "8000"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "8000"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "8443"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "8443"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "9000"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "9000"
#   }

#   ingress {
#     cidr_blocks = ["${var.vpc_cidr_block}"]
#     from_port   = "7800"
#     protocol    = "tcp"
#     self        = "false"
#     to_port     = "7800"
#   }


#   name = "vpn_sg"
#   tags = {
#     Name        = "ravenclaw-dev-vpn-sg"
#     Environment = "dev"
#     Product     = "AWS"
#     Team        = "infra"
#   }
#   vpc_id = module.vpc_main.vpc_id
# }

resource "aws_security_group" "internal_alb_loadbalancer_sg" {
  description            = "For Internal ALB"
  revoke_rules_on_delete = false

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "443"
    protocol  = "tcp"
    self      = "false"
    to_port   = "443"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "80"
    protocol  = "tcp"
    self      = "false"
    to_port   = "80"
  }

  name = "internal_alb_loadbalancer_sg"
  tags = {
    Name        = "internal-alb-sg"
    Team        = "infra"
    Product     = "ravenclaw"
    Environment = "dev"
  }
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "gosmtp_service_sg" {
  description            = "Security group for SMTP server ECS Fargate tasks"
  revoke_rules_on_delete = false

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "80"
    protocol    = "tcp"
    self        = "false"
    to_port     = "80"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "587"
    protocol    = "tcp"
    self        = "false"
    to_port     = "587"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    from_port   = "2525"
    protocol    = "tcp"
    self        = "false"
    to_port     = "2525"
  }

  name = "gosmtp-server-ecs-sg"
  tags = {
    Name        = "gosmtp-server-ecs-sg"
    Environment = "dev"
    Product     = "inline"
    Team        = "infra"
  }
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "microsoft_smtp_nlb_loadbalancer_sg" {
  description = "Security group for GOSMTP server Network Load Balancer - Microsoft CIDRs"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    # cidr_blocks = ["${var.vpc_cidr_block}"]
    ipv6_cidr_blocks = [
      "2a01:111:f400::/48",
      "2a01:111:f403::/48",
      "2a01:111:f403::/49",
      "2a01:111:f403:8000::/51",
      "2a01:111:f403:c000::/51",
      "2a01:111:f403:f000::/52",
      "2603:1006::/40",
      "2603:1016::/36",
      "2603:1026::/36",
      "2603:1036::/36",
      "2603:1046::/36",
      "2603:1056::/36",
      "2620:1ec:4::152/128",
      "2620:1ec:4::153/128",
      "2620:1ec:c::10/128",
      "2620:1ec:c::11/128",
      "2620:1ec:d::10/128",
      "2620:1ec:d::11/128",
      "2620:1ec:8f0::/46",
      "2620:1ec:900::/46",
      "2620:1ec:a92::152/128",
      "2620:1ec:a92::153/128",
    ]
    cidr_blocks = [
      # Microsoft
      # https://endpoints.office.com/endpoints/worldwide?ClientRequestId=b10c5ed1-bad1-445f-b386-b919946339a7&service=exchange
      "************/31",
      "************/31",
      "************/22",
      "************/20",
      "*********/15",
      "*********/13",
      "**********/15",
      "**********/16",
      "52.96.0.0/14",
      "52.100.0.0/14",
      "52.102.0.0/16",
      "52.103.0.0/17",
      "52.238.78.88/32",
      "104.47.0.0/17",
      "131.253.33.215/32",
      "132.245.0.0/16",
      "150.171.32.0/22",
      "204.79.197.215/32",
    ]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "25"
    protocol  = "tcp"
    self      = "false"
    to_port   = "25"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "587"
    protocol  = "tcp"
    self      = "false"
    to_port   = "587"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "80"
    protocol  = "tcp"
    self      = "false"
    to_port   = "80"
  }

  name = "microsoft-gosmtp-server-nlb-sg"
  tags = {
    Name        = "microsoft-gosmtp-server-nlb-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "google_smtp_nlb_loadbalancer_sg" {
  description = "Security group for GOSMTP server Network Load Balancer - Google CIDRs"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    # cidr_blocks = ["${var.vpc_cidr_block}"]
    ipv6_cidr_blocks = [
      "2001:4860:4000::/36",
      "2404:6800:4000::/36",
      "2607:f8b0:4000::/36",
      "2800:3f0:4000::/36",
      "2a00:1450:4000::/36",
      "2c0f:fb50:4000::/36",
      "2600:1901:101::8/126",
      "2600:1901:101::14/126",
      "2600:1901:101::10/126",
      "2600:1901:101::c/126",
      "2600:1901:101::4/126",
      "2600:1901:101::/126",
    ]
    cidr_blocks = [
      # Google
      # https://support.google.com/a/answer/60764
      # https://mxtoolbox.com/SuperTool.aspx?action=spf%3a_netblocks.google.com&run=toolpage#
      # _netblocks, _netblocks2, _netblocks3
      "************/24",
      "************/19",
      "**********/20",
      "***********/20",
      "***********/18",
      "**********/16",
      "***********/21",
      "***********/16",
      "************/17",
      "************/19",
      "************/19",
      "***********/19",
      "************/19",
      "*************/19",
      "************/24",
      "*************/19",
      "35.191.0.0/16",
      "142.250.221.0/24",
      "172.253.56.0/21",
      "172.253.112.0/20",
      "130.211.0.0/22",
      "172.217.160.0/20",
      "172.217.32.0/20",
      "108.177.16.0/24",
      "142.250.220.0/24",
    ]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "25"
    protocol  = "tcp"
    self      = "false"
    to_port   = "25"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "587"
    protocol  = "tcp"
    self      = "false"
    to_port   = "587"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "80"
    protocol  = "tcp"
    self      = "false"
    to_port   = "80"
  }

  name = "google-gosmtp-server-nlb-sg"
  tags = {
    Name        = "google-gosmtp-server-nlb-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
  vpc_id = module.vpc_main.vpc_id
}

resource "aws_security_group" "smtp_nlb_loadbalancer_sg" {
  description = "Security group for GOSMTP server Network Load Balancer"

  egress {
    cidr_blocks = ["0.0.0.0/0"]
    from_port   = "0"
    protocol    = "-1"
    self        = "false"
    to_port     = "0"
  }

  ingress {
    # cidr_blocks = ["${var.vpc_cidr_block}"]
    ipv6_cidr_blocks = [
      "2a01:111:f403:8000::/51",
      "2a01:111:f400::/48",
      "2a01:111:f403::/49",
      "2a01:111:f403:c000::/51",
      "2a01:111:f403:f000::/52",
      "2620:1ec:4::152/128",
      "2600:1901:101::8/126",
      "2603:1046::/36",
      "2603:1036::/36",
      "2620:1ec:d::11/128",
      "2620:1ec:900::/46",
      "2620:1ec:a92::153/128",
      "2600:1901:101::18/126",
      "2620:1ec:c::10/128",
      "2800:3f0:4000::/36",
      "2600:1901:101::14/126",
      "2603:1026::/36",
      "2a00:1450:4000::/36",
      "2603:1016::/36",
      "2603:1056::/36",
      "2620:1ec:4::153/128",
      "2620:1ec:d::10/128",
      "2603:1006::/40",
      "2620:1ec:a92::152/128",
      "2620:1ec:c::11/128",
      "2603:1063::/38",
      "2c0f:fb50:4000::/36",
      "2620:1ec:8f0::/46",
      "2600:1901:101::10/126",
      "2a01:111:f403::/48",
      "2607:f8b0:4000::/36",
      "2600:1901:101::c/126",
      "2001:4860:4000::/36",
      "2600:1901:101::4/126",
      "2404:6800:4000::/36",
      "2600:1901:101::/126",
    ]
    cidr_blocks = [
      "***********/19",
      "************/19",
      "**********/16",
      "*************/19",
      "*************/19",
      "35.191.0.0/16",
      "172.253.56.0/21",
      "172.253.112.0/20",
      "52.102.0.0/16",
      "130.211.0.0/22",
      "52.100.0.0/15",
      "52.103.0.0/17",
      "*********/15",
      "172.217.160.0/20",
      "104.47.0.0/17",
      "172.217.32.0/20",
      "132.245.0.0/16",
      "************/31",
      "52.238.119.141/32",
      "**********/15",
      "***********/18",
      "************/22",

      "************/24",
      "***********/20",
      "************/20",
      "142.250.221.0/24",
      "52.112.0.0/14",
      "************/17",
      "52.96.0.0/14",
      "***********/21",
      "************/19",
      "52.238.78.88/32",
      "************/19",
      "150.171.32.0/22",
      "************/31",
      "************/24",
      "52.100.0.0/14",
      "***********/16",
      "************/19",
      "204.79.197.215/32",
      "*********/13",
      "131.253.33.215/32",
      "52.244.160.207/32",
      "**********/16",
      "52.122.0.0/15",
      "108.177.16.0/24",
      "142.250.220.0/24",
      "**********/20",

    ]

    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "25"
    protocol  = "tcp"
    self      = "false"
    to_port   = "25"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "587"
    protocol  = "tcp"
    self      = "false"
    to_port   = "587"
  }

  ingress {
    cidr_blocks = ["${var.vpc_cidr_block}"]
    # cidr_blocks = ["0.0.0.0/0"]
    from_port = "80"
    protocol  = "tcp"
    self      = "false"
    to_port   = "80"
  }

  name = "gosmtp-server-nlb-sg"
  tags = {
    Name        = "gosmtp-server-nlb-sg"
    Environment = "dev"
    Product     = "ravenclaw"
    Team        = "infra"
  }
  vpc_id = module.vpc_main.vpc_id
}
